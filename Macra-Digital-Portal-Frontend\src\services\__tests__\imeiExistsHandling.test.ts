import { imeiValidationService } from '../imeiValidationService';
import { apiClient } from '../../lib/apiClient';

// Mock the API client
jest.mock('../../lib/apiClient');
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock processApiResponse
jest.mock('@/lib/authUtils', () => ({
  processApiResponse: jest.fn((response) => response.data)
}));

describe('IMEI Exists Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateIMEI - when IMEI exists in database', () => {
    test('should handle existing IMEI with all device fields', async () => {
      const mockResponse = {
        data: {
          model_name: 'iPhone 13 Pro',
          device_serial_number: 'ABC123456789',
          device_approval_date: '2023-06-15T00:00:00Z',
          device_type: 'smartphone',
          manufacturer: 'Apple',
          tac: '35175605'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      expect(result.status).toBe('exists');
      expect(result.isValid).toBe(true);
      expect(result.message).toBe('Device found in database');
      expect(result.model_name).toBe('iPhone 13 Pro');
      expect(result.device_serial_number).toBe('ABC123456789');
      expect(result.device_approval_date).toBe('2023-06-15T00:00:00Z');
      expect(result.device_type).toBe('smartphone');
      
      // Check deviceInfo object
      expect(result.deviceInfo?.model_name).toBe('iPhone 13 Pro');
      expect(result.deviceInfo?.device_serial_number).toBe('ABC123456789');
      expect(result.deviceInfo?.device_approval_date).toBe('2023-06-15T00:00:00Z');
      expect(result.deviceInfo?.device_type).toBe('smartphone');
    });

    test('should handle existing IMEI with partial device fields', async () => {
      const mockResponse = {
        data: {
          model_name: 'Samsung Galaxy S21',
          device_type: 'smartphone'
          // Missing device_serial_number and device_approval_date
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      expect(result.status).toBe('exists');
      expect(result.isValid).toBe(true);
      expect(result.model_name).toBe('Samsung Galaxy S21');
      expect(result.device_type).toBe('smartphone');
      expect(result.device_serial_number).toBeUndefined();
      expect(result.device_approval_date).toBeUndefined();
    });

    test('should handle existing IMEI with only device_serial_number', async () => {
      const mockResponse = {
        data: {
          device_serial_number: 'XYZ987654321'
          // Only serial number present
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      expect(result.status).toBe('exists');
      expect(result.isValid).toBe(true);
      expect(result.device_serial_number).toBe('XYZ987654321');
      expect(result.model_name).toBeUndefined();
    });

    test('should not treat as existing IMEI if no device fields present', async () => {
      const mockResponse = {
        data: {
          imei: '351756051523999',
          isValid: true,
          status: 'valid',
          validatedAt: '2024-01-01T00:00:00Z'
          // No device-specific fields
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      // Should return the original response, not convert to 'exists'
      expect(result.status).toBe('valid');
      expect(result.isValid).toBe(true);
      expect(result.model_name).toBeUndefined();
    });
  });

  describe('Edge cases for existing IMEI', () => {
    test('should handle null/undefined device fields gracefully', async () => {
      const mockResponse = {
        data: {
          model_name: null,
          device_serial_number: undefined,
          device_approval_date: '',
          device_type: 'tablet'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      expect(result.status).toBe('exists');
      expect(result.device_type).toBe('tablet');
      expect(result.model_name).toBeNull();
      expect(result.device_serial_number).toBeUndefined();
      expect(result.device_approval_date).toBe('');
    });

    test('should handle invalid date formats in device_approval_date', async () => {
      const mockResponse = {
        data: {
          model_name: 'Test Device',
          device_approval_date: 'invalid-date-format'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('351756051523999');

      expect(result.status).toBe('exists');
      expect(result.device_approval_date).toBe('invalid-date-format');
      // Should still work even with invalid date format
    });
  });
});
