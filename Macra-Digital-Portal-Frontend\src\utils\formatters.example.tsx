/**
 * Example usage of the formatters utility
 * This file demonstrates how to use various formatters throughout the application
 */

import React from 'react';
import {
  // Amount & Currency
  formatAmount,
  formatNumber,
  formatPercentage,
  formatCurrency,
  
  // Date & Time
  formatDate,
  formatDateLong,
  formatTime,
  formatDateTime,
  formatRelativeTime,
  formatDateForInput,
  
  // String Cases
  toCamelCase,
  toPascalCase,
  toKebabCase,
  toSnakeCase,
  toTitleCase,
  toSentenceCase,
  
  // Text Utilities
  truncateText,
  capitalizeWords,
  normalizeWhitespace,
  getInitials,
  toSlug,
  highlightText,
  
  // Phone & Email
  formatPhone,
  maskEmail,
  isValidEmail,
  
  // ID & References
  formatApplicationNumber,
  formatInvoiceNumber,
  formatTaskNumber,
  generateReferenceId,
  maskId,
  
  // Status & Badges
  formatStatus,
  getStatusColor,
  
  // File Utilities
  formatFileSize,
  getFileExtension,
  getFileTypeIcon
} from './formatters';

const FormattersExample: React.FC = () => {
  const sampleData = {
    amount: 1234567.89,
    date: new Date(),
    pastDate: new Date('2024-01-15T14:30:00Z'),
    email: '<EMAIL>',
    phone: '1234567890',
    name: 'John Doe Smith',
    text: 'This is a very long text that needs to be truncated for display purposes',
    status: 'pending_review',
    fileSize: 2048576,
    filename: 'document.pdf'
  };

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
        Formatters Utility Examples
      </h1>

      {/* Amount & Currency Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Amount & Currency Formatting
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatAmount</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: {sampleData.amount}
            </p>
            <p className="font-mono">
              {formatAmount(sampleData.amount, 'MWK')}
            </p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatNumber</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: {sampleData.amount}
            </p>
            <p className="font-mono">
              {formatNumber(sampleData.amount)}
            </p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatPercentage</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: 85.67
            </p>
            <p className="font-mono">
              {formatPercentage(85.67)}
            </p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatCurrency (legacy)</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: {sampleData.amount}
            </p>
            <p className="font-mono">
              {formatCurrency(sampleData.amount, 'MWK')}
            </p>
          </div>
        </div>
      </section>

      {/* Date & Time Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Date & Time Formatting
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatDate</h3>
            <p className="font-mono">{formatDate(sampleData.date)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatDateLong</h3>
            <p className="font-mono">{formatDateLong(sampleData.date)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatTime</h3>
            <p className="font-mono">{formatTime(sampleData.date)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatDateTime</h3>
            <p className="font-mono">{formatDateTime(sampleData.date)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatRelativeTime</h3>
            <p className="font-mono">{formatRelativeTime(sampleData.pastDate)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatDateForInput</h3>
            <p className="font-mono">{formatDateForInput(sampleData.date)}</p>
          </div>
        </div>
      </section>

      {/* String Case Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          String Case Formatting
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toCamelCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "hello world example"
            </p>
            <p className="font-mono">{toCamelCase("hello world example")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toPascalCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "hello world example"
            </p>
            <p className="font-mono">{toPascalCase("hello world example")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toKebabCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "Hello World Example"
            </p>
            <p className="font-mono">{toKebabCase("Hello World Example")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toSnakeCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "Hello World Example"
            </p>
            <p className="font-mono">{toSnakeCase("Hello World Example")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toTitleCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "hello world example"
            </p>
            <p className="font-mono">{toTitleCase("hello world example")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toSentenceCase</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "HELLO WORLD EXAMPLE"
            </p>
            <p className="font-mono">{toSentenceCase("HELLO WORLD EXAMPLE")}</p>
          </div>
        </div>
      </section>

      {/* Text Utilities Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Text Utilities
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">truncateText</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Original: {sampleData.text}
            </p>
            <p className="font-mono">{truncateText(sampleData.text, 30)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">getInitials</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Name: {sampleData.name}
            </p>
            <p className="font-mono text-lg">{getInitials(sampleData.name)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">toSlug</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "Hello World! This is a Test."
            </p>
            <p className="font-mono">{toSlug("Hello World! This is a Test.")}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">capitalizeWords</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              "hello world example"
            </p>
            <p className="font-mono">{capitalizeWords("hello world example")}</p>
          </div>
        </div>
      </section>

      {/* Contact & ID Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Contact & ID Formatting
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatPhone</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: {sampleData.phone}
            </p>
            <p className="font-mono">{formatPhone(sampleData.phone)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">maskEmail</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Original: {sampleData.email}
            </p>
            <p className="font-mono">{maskEmail(sampleData.email)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatApplicationNumber</h3>
            <p className="font-mono">{formatApplicationNumber(12345)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">generateReferenceId</h3>
            <p className="font-mono">{generateReferenceId(8, 'REF')}</p>
          </div>
        </div>
      </section>

      {/* Status & File Examples */}
      <section className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Status & File Utilities
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatStatus</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Raw: {sampleData.status}
            </p>
            <p className="font-mono">{formatStatus(sampleData.status)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">formatFileSize</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              Bytes: {sampleData.fileSize}
            </p>
            <p className="font-mono">{formatFileSize(sampleData.fileSize)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">getFileExtension</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              File: {sampleData.filename}
            </p>
            <p className="font-mono">{getFileExtension(sampleData.filename)}</p>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-medium mb-2">getFileTypeIcon</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              File: {sampleData.filename}
            </p>
            <i className={getFileTypeIcon(sampleData.filename) + " text-2xl"}></i>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FormattersExample;
