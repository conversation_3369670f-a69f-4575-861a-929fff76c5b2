/* Form input visibility fixes for dark mode browsers */

/* Force text color for inputs to ensure visibility regardless of browser/system dark mode */
input, 
textarea, 
select {
  color: black !important;
}

/* When the app is explicitly in dark mode, use white text */
.dark input,
.dark textarea,
.dark select {
  color: white !important;
}

/* Ensure labels are visible */
label {
  color: black;
}

.dark label {
  color: white;
}

/* Fix for placeholder text */
::placeholder {
  color: #9ca3af !important; /* gray-400 */
  opacity: 1;
}

.dark ::placeholder {
  color: #6b7280 !important; /* gray-500 */
  opacity: 1;
}