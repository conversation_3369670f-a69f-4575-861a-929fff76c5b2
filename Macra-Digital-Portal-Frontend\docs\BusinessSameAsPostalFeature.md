# Business Same as Postal Address Feature

## Overview

Added a "Business same as Postal Address" radio button option that allows users to automatically copy business address details to create a postal address, with proper validation to ensure both address types are provided.

## Key Features

### ✅ **Radio Button Option**
- **Location**: Appears in the Address Management section when Postal Address type is selected
- **Functionality**: Automatically copies business address details to create a postal address
- **Auto-Enable**: Automatically selected as true when user attempts to save/continue without a postal address
- **Validation**: Ensures business address exists before allowing the copy operation

### ✅ **Smart Validation Logic**
- **Business Address Required**: Validates that a business address exists before saving
- **Postal Address Required**: Validates that either a postal address exists OR the "Business same as Postal" option is selected
- **Error Messages**: Clear, actionable error messages guide users to complete required steps

### ✅ **State Management**
- **useBusinessAsPostal**: Boolean state to track if the option is selected
- **Auto-reset**: Option resets when switching between address types
- **Error Clearing**: Validation errors clear appropriately when option is toggled

## Implementation Details

### **1. State Addition**
```typescript
// State for "Business same as Postal Address" option
const [useBusinessAsPostal, setUseBusinessAsPostal] = useState(false);
```

### **2. UI Component**
```typescript
{/* Business same as Postal Address Option - Only show when postal address is selected */}
{selectedAddressType === ADDRESS_TYPES.POSTAL && (
  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
    <div className="flex items-center space-x-3">
      <input
        type="radio"
        id="useBusinessAsPostal"
        name="postalAddressOption"
        checked={useBusinessAsPostal}
        onChange={(e) => handleUseBusinessAsPostal(e.target.checked)}
        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
      />
      <label htmlFor="useBusinessAsPostal" className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Business same as Postal Address
      </label>
    </div>
    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 ml-7">
      Check this option to automatically copy your business address details to create a postal address.
    </p>
  </div>
)}
```

### **3. Copy Logic Handler**
```typescript
const handleUseBusinessAsPostal = (checked: boolean) => {
  setUseBusinessAsPostal(checked);
  
  if (checked) {
    // Check if business address exists
    const businessAddresses = addressesByType[ADDRESS_TYPES.BUSINESS];
    const selectedBusinessId = selectedAddressIds[ADDRESS_TYPES.BUSINESS];
    const businessAddress = selectedBusinessId 
      ? businessAddresses.find(addr => addr.address_id === selectedBusinessId)
      : businessAddresses[0]; // Use first business address if none selected
    
    if (businessAddress) {
      // Copy business address to postal address
      const postalAddressData = {
        address_type: ADDRESS_TYPES.POSTAL,
        entity_type: 'applicant',
        entity_id: applicantId || '',
        address_line_1: businessAddress.address_line_1,
        address_line_2: businessAddress.address_line_2,
        address_line_3: businessAddress.address_line_3,
        postal_code: businessAddress.postal_code,
        country: businessAddress.country,
        city: businessAddress.city,
      };

      // Create postal address with business address data
      createAddress(postalAddressData)
        .then((response) => {
          const newPostalAddress = response.data || response;
          
          // Update state with new postal address
          setAddressesByType(prev => ({
            ...prev,
            [ADDRESS_TYPES.POSTAL]: [...prev[ADDRESS_TYPES.POSTAL], newPostalAddress]
          }));
          
          setSelectedAddressIds(prev => ({
            ...prev,
            [ADDRESS_TYPES.POSTAL]: newPostalAddress.address_id
          }));

          setSuccessMessage('Postal address created using business address details.');
        })
        .catch((error) => {
          setValidationErrors(prev => ({
            ...prev,
            postal_copy: 'Failed to copy business address to postal address. Please try again.'
          }));
        });
    } else {
      // No business address available
      setValidationErrors(prev => ({
        ...prev,
        business_required: 'Please create a business address first before copying to postal address.'
      }));
      setUseBusinessAsPostal(false); // Reset the checkbox
    }
  }
};
```

### **4. Enhanced Validation**
```typescript
const handleSave = async () => {
  // Validate that both business and postal addresses exist
  const businessAddresses = addressesByType[ADDRESS_TYPES.BUSINESS];
  const postalAddresses = addressesByType[ADDRESS_TYPES.POSTAL];
  
  if (businessAddresses.length === 0) {
    setValidationErrors({ business_required: 'Business address is required.' });
    return false;
  }
  
  if (postalAddresses.length === 0 && !useBusinessAsPostal) {
    setValidationErrors({ 
      postal_required: 'Postal address is required. Either create a postal address or select "Business same as Postal Address".' 
    });
    return false;
  }
  
  // ... rest of save logic
};
```

### **5. Address Type Change Handler**
```typescript
const handleAddressTypeChange = (newType: SupportedAddressType) => {
  setSelectedAddressType(newType);
  
  // Reset "Business same as Postal" option when switching types
  if (useBusinessAsPostal) {
    setUseBusinessAsPostal(false);
  }
  
  // Clear any type-specific validation errors
  setValidationErrors(prev => {
    const newErrors = { ...prev };
    delete newErrors.business_required;
    delete newErrors.postal_required;
    delete newErrors.postal_copy;
    return newErrors;
  });
};
```

## User Experience Flow

### **1. Business Address Creation**
1. User selects "Business Address" type
2. User creates business address using the form
3. Business address is saved and appears in the selection

### **2. Postal Address Options**
1. User selects "Postal Address" type
2. User sees two options:
   - Create a new postal address manually
   - Select "Business same as Postal Address" radio button

### **3. Using Business Same as Postal**
1. User checks "Business same as Postal Address" radio button
2. System validates that business address exists
3. If business address exists:
   - Automatically creates postal address with business address details
   - Shows success message
   - Postal address appears in selection
4. If no business address exists:
   - Shows error message
   - Unchecks the radio button
   - Prompts user to create business address first

### **4. Validation on Save**
1. System checks that business address exists
2. System checks that either:
   - Postal address exists, OR
   - "Business same as Postal Address" option was used
3. If validation fails, shows appropriate error messages

## Error Messages

### **Business Address Required**
```
"Please create a business address first before copying to postal address."
```

### **Postal Address Required**
```
"Postal address is required. Either create a postal address or select 'Business same as Postal Address'."
```

### **Copy Operation Failed**
```
"Failed to copy business address to postal address. Please try again."
```

## Benefits

### ✅ **User Convenience**
- **One-Click Copy**: Eliminates need to manually re-enter identical address details
- **Time Saving**: Reduces form completion time for users with same business/postal addresses
- **Error Reduction**: Prevents typos when manually copying address information

### ✅ **Data Integrity**
- **Validation**: Ensures both required address types are provided
- **Consistency**: Guarantees identical data when addresses should be the same
- **Error Handling**: Graceful handling of edge cases and failures

### ✅ **Flexible Options**
- **Choice**: Users can still create separate postal addresses if needed
- **Conditional Display**: Option only appears when relevant (postal address type selected)
- **Reset Logic**: Smart reset when switching between address types

### ✅ **Clear Feedback**
- **Success Messages**: Confirms when postal address is created successfully
- **Error Messages**: Clear guidance on what needs to be done
- **Visual Indicators**: Radio button state clearly shows current selection

## Technical Implementation

### **State Structure:**
```typescript
interface AddressState {
  useBusinessAsPostal: boolean;
  addressesByType: Record<SupportedAddressType, Address[]>;
  selectedAddressIds: Record<SupportedAddressType, string | null>;
}
```

### **Validation Logic:**
- Business address existence check
- Postal address requirement with fallback option
- Error state management and clearing

### **API Integration:**
- Automatic postal address creation using business address data
- Proper address type assignment
- State synchronization after creation

The implementation provides a user-friendly solution for handling cases where business and postal addresses are identical, while maintaining flexibility for users who need separate addresses and ensuring data integrity through comprehensive validation.
