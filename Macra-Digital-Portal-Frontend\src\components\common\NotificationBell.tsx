'use client';

import React, { useState, useEffect } from 'react';
import { notificationService } from '@/services/notificationService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import NotificationModal from '../notifications/NotificationModal';

interface NotificationBellProps {
  className?: string;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const { showError } = useToast();
  const [unreadCount, setUnreadCount] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch notification count
  const fetchNotificationCount = async () => {
    if (!user) return;
    
    try {
      const data = await notificationService.getNotificationCount();
      setUnreadCount(data.unread);
    } catch (error) {
      console.error('Error fetching notification count:', error);
      showError('Failed to load notification count');
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchNotificationCount();
  }, [user]);

  // Poll for new notifications every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchNotificationCount, 30000);
    return () => clearInterval(interval);
  }, [user]);

  // Handle modal close and refresh count
  const handleModalClose = () => {
    setIsModalOpen(false);
    fetchNotificationCount(); // Refresh count when modal closes
  };

  if (!user) return null;

  return (
    <>
      <div className={`relative ${className}`}>
        {/* Notification Bell */}
        <button
          onClick={() => setIsModalOpen(true)}
          className="relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          title="Notifications"
        >
          <i className="ri-notification-line text-xl"></i>
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
        </button>
      </div>

      {/* Notification Modal */}
      <NotificationModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </>
  );
};

export default NotificationBell;