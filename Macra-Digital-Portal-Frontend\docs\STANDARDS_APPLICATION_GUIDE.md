# Standards Application with IMEI Validation

This guide explains the customized standards application process that includes IMEI validation as the first step.

## Overview

The standards application process has been customized for license type ID `89d0a698-e9f7-4cc7-a612-e27ea5892837` to include device IMEI validation before the standard application steps.

## Features

### 1. IMEI Validation Component
- **Location**: `src/components/forms/IMEIInput.tsx`
- **Purpose**: Validates International Mobile Equipment Identity (IMEI) numbers
- **Validation**: 
  - 15-digit format validation
  - Luhn algorithm checksum verification
  - Real-time formatting with spaces for readability
  - Visual feedback for valid/invalid states

### 2. IMEI Validation Step
- **Route**: `/customer/applications/apply/imei-validation`
- **Component**: `src/app/customer/applications/apply/imei-validation/page.tsx`
- **Purpose**: First step in standards application process
- **Features**:
  - IMEI input with validation
  - Save functionality with localStorage backup
  - Navigation to next step (applicant-info)
  - Educational information about IMEI requirements

### 3. Enhanced License Type Detection
- **File**: `src/app/customer/applications/[licenseTypeId]/page.tsx`
- **Enhancement**: Detects standards compliance license type and routes to IMEI validation
- **Trigger**: License type ID `89d0a698-e9f7-4cc7-a612-e27ea5892837` or name containing "standards" or "compliance"

### 4. Updated Step Configuration
- **File**: `src/config/licenseTypeStepConfig.ts`
- **Changes**:
  - Added `imeiValidation` base step
  - Updated `standards_compliance` configuration to include IMEI validation as first step
  - Added UUID to code mapping for the specific license type

## Application Flow

### For Standards Compliance Applications:

1. **License Selection**: User selects standards compliance license type
2. **IMEI Validation**: First step - user enters and validates device IMEI
3. **Standard Steps**: Continues with normal application flow:
   - Applicant Information
   - Address Information
   - Contact Information
   - Management Structure
   - Professional Services
   - Service Scope
   - Legal History
   - Documents
   - Submit

### For Other License Types:

1. **License Selection**: User selects any other license type
2. **Standard Flow**: Starts directly with Applicant Information step

## IMEI Validation Details

### Format Requirements
- Exactly 15 digits
- No letters or special characters
- Passes Luhn algorithm checksum validation

### User Experience
- Input automatically formats with spaces (XXX XXX XXX XXX XXX)
- Real-time validation feedback
- Visual indicators for valid/invalid states
- Helpful error messages
- Educational content about IMEI importance

### Finding IMEI Numbers
Users can find their device IMEI by:
- Dialing `*#06#` on their device
- Checking device settings
- Looking at the device packaging or battery compartment

## Technical Implementation

### Components Used
- `IMEIInput`: Custom IMEI input component with validation
- `ApplicationLayout`: Standard application layout wrapper
- `FormMessages`: Error and success message display
- `useDynamicNavigation`: Navigation between application steps

### Validation Functions
- `validateIMEIChecksum()`: Implements Luhn algorithm for IMEI validation
- `patterns.imei`: Regex pattern for 15-digit format validation
- `commonRules.imei`: Validation rules for form integration

### Data Storage
- IMEI data is validated against `/standards/devices/imei/:imei` endpoint
- Validation results are saved via `/applications/{id}/imei` endpoint
- Fallback to localStorage if API is unavailable
- Key format: `imei_validation_${applicationId}` for localStorage

## Configuration

### License Type Mapping
The system recognizes standards applications through:
- Exact UUID match: `89d0a698-e9f7-4cc7-a612-e27ea5892837`
- License type name containing "standards" or "compliance"
- Code mapping in `licenseTypeUUIDToCodeMap`

### Step Configuration
```typescript
standards_compliance: {
  licenseTypeId: 'standards_compliance',
  name: 'Standards Compliance License',
  steps: [
    BASE_STEPS.imeiValidation,  // New first step
    BASE_STEPS.applicantInfo,
    // ... other steps
  ],
  estimatedTotalTime: '87 minutes',
  requirements: [
    'Device IMEI number',  // New requirement
    // ... other requirements
  ]
}
```

## Testing

### Unit Tests
- Location: `src/utils/__tests__/imeiValidation.test.ts`
- Coverage: IMEI validation functions and Luhn algorithm
- Test cases: Valid IMEIs, invalid IMEIs, edge cases

### Manual Testing
1. Navigate to applications page
2. Select standards compliance license type
3. Verify IMEI validation step appears first
4. Test IMEI input with valid/invalid numbers
5. Verify navigation to next step works

## Future Enhancements

### Potential Improvements
1. **Backend Integration**: Save IMEI data to database
2. **Device Verification**: Check IMEI against blacklist databases
3. **Device Information**: Display device details based on IMEI
4. **Bulk IMEI**: Support multiple device IMEIs for organizations
5. **QR Code**: Allow IMEI input via QR code scanning

### API Integration
The system now integrates with backend APIs:
- GET `/devices/imei/{imei}` - Validate IMEI against device database
- POST `/devices` - Create device record
- PUT `/devices/{id}` - Update device record
- GET `/devices/application/{applicationId}` - Get devices for application
- POST `/devices/imei/batch-validate` - Batch validate multiple IMEIs

## Troubleshooting

### Common Issues
1. **IMEI not recognized**: Ensure 15-digit format without spaces or dashes
2. **Checksum failed**: Verify IMEI is correct using device settings
3. **Navigation issues**: Check license type ID matches expected value
4. **Step not appearing**: Verify license type detection logic

### Debug Information
- Check browser console for navigation logs
- Verify localStorage data for IMEI persistence
- Check license type ID in URL parameters
- Validate step configuration loading
