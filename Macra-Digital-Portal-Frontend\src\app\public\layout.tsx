import PublicLayout from '@/components/public/PublicLayout';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    template: '%s | MACRA License Verification',
    default: 'MACRA License Verification Portal'
  },
  description: 'Official license verification portal for the Malawi Communications Regulatory Authority (MACRA). Verify the authenticity of licenses issued by MACRA instantly and securely.',
  keywords: [
    'MACRA',
    'license verification',
    'Malawi',
    'communications',
    'regulatory authority',
    'license authenticity',
    'telecommunications',
    'broadcasting',
    'postal services'
  ],
  authors: [{ name: 'Malawi Communications Regulatory Authority' }],
  creator: 'MACRA',
  publisher: 'MACRA',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_MW',
    url: 'https://portal.macra.mw/public',
    siteName: 'MACRA License Verification Portal',
    title: 'MACRA License Verification Portal',
    description: 'Verify the authenticity of licenses issued by the Malawi Communications Regulatory Authority (MACRA)',
    images: [
      {
        url: '/images/macra-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'MACRA License Verification Portal',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MACRA License Verification Portal',
    description: 'Verify the authenticity of licenses issued by the Malawi Communications Regulatory Authority (MACRA)',
    images: ['/images/macra-twitter-image.jpg'],
    creator: '@MACRA_MW',
  },
  alternates: {
    canonical: 'https://portal.macra.mw/public',
  },
  other: {
    'msapplication-TileColor': '#2563eb',
    'theme-color': '#2563eb',
  },
};

export default function PublicRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <PublicLayout>{children}</PublicLayout>;
}
