'use client';

import React, { useState, useEffect } from 'react';
import Modal from '@/components/common/Modal';
import NotificationItem from './NotificationItem';
import Loader from '@/components/Loader';
import { useNotifications } from '@/hooks/useNotifications';
import { useToast } from '@/contexts/ToastContext';
import { AppNotification } from '@/types/notification';

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
}) => {
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
  } = useNotifications();
  
  const { showSuccess, showError } = useToast();
  const [filter, setFilter] = useState<'all' | 'unread'>('unread');
  const [localLoading, setLocalLoading] = useState(false);

  // Filter notifications based on selected filter
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') {
      return !notification.read_at;
    }
    return true;
  });


  // Refresh notifications when modal opens
  useEffect(() => {
    if (isOpen) {
      refreshNotifications();
    }
  }, [isOpen, refreshNotifications]);

  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsRead(id);
      showSuccess('Notification marked as read');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      showError('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) {
      showError('No unread notifications to mark');
      return;
    }

    setLocalLoading(true);
    try {
      await markAllAsRead();
      showSuccess('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      showError('Failed to mark all notifications as read');
    } finally {
      setLocalLoading(false);
    }
  };

  const handleNotificationClick = (notification: AppNotification) => {
    // Handle notification click - could navigate to related page
    if (notification.entity_type === 'application' && notification.entity_id) {
      // Navigate to application details
      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;
    }
  };

  const handleRefresh = async () => {
    setLocalLoading(true);
    try {
      await refreshNotifications();
    } catch (error) {
      console.error('Error refreshing notifications:', error);
      showError('Failed to refresh notifications');
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Notifications"
      size="lg"
    >
      <div className="flex flex-col h-96">
        {/* Header with filters and actions */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            {/* Filter buttons */}
            <div className="flex space-x-2">
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
                  filter === 'all'
                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                All ({notifications.length})
              </button>
              <button
                onClick={() => setFilter('unread')}
                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${
                  filter === 'unread'
                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                Unread ({unreadCount})
              </button>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={loading || localLoading}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50"
              title="Refresh notifications"
            >
              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>
            </button>
            
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                disabled={loading || localLoading}
                className="px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50"
              >
                Mark all as read
              </button>
            )}
          </div>
        </div>

        {/* Notifications list */}
        <div className="flex-1 overflow-y-auto">
          {loading && notifications.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Loader message="Loading notifications..." />
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4">
              <i className="ri-error-warning-line text-4xl text-red-500 mb-2"></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                Error Loading Notifications
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {error}
              </p>
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                Try Again
              </button>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-4">
              <i className="ri-notification-off-line text-4xl text-gray-400 mb-2"></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {filter === 'unread' 
                  ? 'All caught up! You have no unread notifications.'
                  : 'You have no notifications at this time.'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.notification_id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onNotificationClick={handleNotificationClick}
                />
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {filteredNotifications.length > 0 && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredNotifications.length} of {notifications.length} notifications
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default NotificationModal;
