'use client';

import React from 'react';
import { CheckCircleIcon, ExclamationCircleIcon, InformationCircleIcon } from '@heroicons/react/24/solid';

interface StatusMessageProps {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  className?: string;
  showIcon?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
}

const StatusMessage: React.FC<StatusMessageProps> = ({
  type,
  message,
  className = '',
  showIcon = true,
  dismissible = false,
  onDismiss
}) => {
  const getStatusStyles = () => {
    switch (type) {
      case 'success':
        return {
          container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400',
          icon: 'text-green-500 dark:text-green-400'
        };
      case 'error':
        return {
          container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400',
          icon: 'text-red-500 dark:text-red-400'
        };
      case 'warning':
        return {
          container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-600 dark:text-yellow-400',
          icon: 'text-yellow-500 dark:text-yellow-400'
        };
      case 'info':
      default:
        return {
          container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400',
          icon: 'text-blue-500 dark:text-blue-400'
        };
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5" />;
      case 'error':
      case 'warning':
        return <ExclamationCircleIcon className="h-5 w-5" />;
      case 'info':
      default:
        return <InformationCircleIcon className="h-5 w-5" />;
    }
  };

  const styles = getStatusStyles();

  return (
    <div className={`border px-4 py-3 rounded-md transition-smooth status-message-enter ${styles.container} ${className} ${type === 'error' ? 'animate-shake' : ''}`}>
      <div className="flex items-start">
        {showIcon && (
          <div className={`flex-shrink-0 ${styles.icon} animate-scaleIn`}>
            {getIcon()}
          </div>
        )}
        <div className={`${showIcon ? 'ml-3' : ''} flex-1 animate-slideInFromBottom animate-delay-100`}>
          <p className="text-sm font-medium">{message}</p>
        </div>
        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className="ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:scale-110 transform duration-200"
          >
            <span className="sr-only">Dismiss</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default StatusMessage;
