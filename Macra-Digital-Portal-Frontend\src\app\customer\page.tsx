'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';
import PaymentCard from '@/components/customer/PaymentCard';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import { customerApi } from '@/lib/customer-api';
import { measureApiCall } from '@/utils/performance';
import TwoFactorSetupPopup from '@/components/auth/TwoFactorSetupPopup';
import LandingPage from '@/components/customer/LandingPage';
import { License, Application, Payment } from '@/types';

const CustomerDashboard = () => {
  const { user, isAuthenticated } = useAuth();

  // Check for 2FA setup popup flag
  useEffect(() => {
    const shouldShow2FAPopup = sessionStorage.getItem('show_2fa_setup_popup');
    if (shouldShow2FAPopup === 'true' && user && !user.two_factor_enabled) {
      setShow2FAPopup(true);
      sessionStorage.removeItem('show_2fa_setup_popup');
    }
  }, [user]);

  const [dashboardData, setDashboardData] = useState({
    licenses: [] as License[],
    applications: [] as Application[],
    payments: [] as Payment[],
    stats: {
      activeLicenses: 0,
      pendingApplications: 0,
      expiringSoon: 0,
      paymentsDue: 0,
      totalPaymentAmount: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [show2FAPopup, setShow2FAPopup] = useState(false);

  // Fetch dashboard data with optimized parallel requests
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!isAuthenticated) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError('');

        // Measure API performance
        const endApiCall = measureApiCall('dashboard-data');

        // Use Promise.all for better performance
        const [licensesRes, applicationsRes, paymentsRes, statsRes] = await Promise.all([
          customerApi.getLicenses({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getApplications({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getPayments({ limit: 10 }).catch(() => ({ data: [] })),
          customerApi.getDashboardStats().catch(() => ({}))
        ]);

        endApiCall();

        // Process data efficiently
        const licenses = licensesRes.data || licensesRes || [];
        const applications = applicationsRes.data || applicationsRes || [];
        const payments = paymentsRes.data || paymentsRes || [];

        // Process stats or calculate from data
        let stats;
        if (statsRes && Object.keys(statsRes).length > 0) {
          stats = statsRes.data || statsRes;
        } else {
          // Calculate stats from fetched data
          const activeLicenses = licenses.filter((l: License) => l.status === 'active').length;
          const pendingApplications = applications.filter((a: Application) =>
            ['submitted', 'under_review'].includes(a.status)
          ).length;

          // Check for licenses expiring in next 30 days
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          const expiringSoon = licenses.filter((l: License) => {
            const expirationDate = new Date(l.expiry_date);
            return l.status === 'active' && expirationDate <= thirtyDaysFromNow;
          }).length;

          const pendingPayments = payments.filter((p: Payment) =>
            ['pending', 'overdue'].includes(p.status)
          );
          const totalPaymentAmount = pendingPayments.reduce((sum: number, p: Payment) => sum + p.amount, 0);

          stats = {
            activeLicenses,
            pendingApplications,
            expiringSoon,
            paymentsDue: pendingPayments.length,
            totalPaymentAmount
          };
        }

        setDashboardData({
          licenses,
          applications,
          payments,
          stats
        });

      } catch (err: unknown) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try refreshing the page.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated]);

  // Memoize expensive calculations
  const quickStats = useMemo(() => {
    const { licenses, applications, payments } = dashboardData;
    
    return {
      totalLicenses: licenses.length,
      activeLicenses: licenses.filter(l => l.status === 'active').length,
      pendingApplications: applications.filter(a => ['submitted', 'under_review'].includes(a.status)).length,
      overduePayments: payments.filter(p => p.status === 'overdue').length
    };
  }, [dashboardData]);

  // Memoize filtered data for display
  const displayData = useMemo(() => {
    const { payments } = dashboardData;
    
    return {
      urgentPayments: payments.filter(p => ['pending', 'overdue'].includes(p.status)).slice(0, 3)
    };
  }, [dashboardData]);

  // Show loading state
  if (isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading your dashboard..." />
        </div>
      </CustomerLayout>
    );
  }

  
  // Show Landing Page
  if (!user) {
    return <LandingPage />;
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      </CustomerLayout>
    );
  }





  // Transform payment data for display (using memoized data)
  const upcomingPayments = displayData.urgentPayments
    .map((payment: Payment) => {
      const dueDate = new Date(payment.due_date);
      const today = new Date();
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      let dueDateText;
      if (diffDays < 0) {
        dueDateText = `Overdue by ${Math.abs(diffDays)} days`;
      } else if (diffDays === 0) {
        dueDateText = 'Due today';
      } else if (diffDays === 1) {
        dueDateText = 'Due tomorrow';
      } else {
        dueDateText = `Due in ${diffDays} days`;
      }

      return {
        id: payment.payment_id,
        title: payment.description ,
        amount: `MK${payment.amount.toLocaleString()}`,
        dueDate: dueDateText,
        status: payment.status === 'overdue' ? 'Overdue' as const : 'Due' as const,
        description: dueDateText
      };
    });

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                Welcome, {user?.first_name || 'Customer'}!
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Apply for licenses or procurement bids, and report concerns to Consumer Affairs or Data Breach.
              </p>
            </div>
            <div className="flex space-x-3">
              <Link
                href="/customer/applications/apply"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
              >
                <div className="w-4 h-4 flex items-center justify-center mr-2">
                  <i className="ri-add-line"></i>
                </div>
                New Application
              </Link>
            </div>
          </div>
        </div>

        {/* Available Services Section */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
          <div className="p-4">
            <h3 className="text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4">Available Services</h3>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4">
              {/* Licenses Service */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex flex-col space-y-3">
                <div className="flex place-content-start items-center">
                  <div className="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-2">
                    <div className="w-5 h-5 flex items-center justify-center text-blue-600 dark:text-blue-400">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3 flex flex-col">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">Licenses</h4>
                    <div className="mt-1 flex items-baseline">
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">Apply & Manage</div>
                    </div>
                  </div>
                </div>
                <div>
                  <Link
                    href="/customer/applications"
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']"
                  >
                    Access Service
                  </Link>
                </div>
              </div>

              {/* Procurement Service */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex flex-col space-y-3">
                <div className="flex place-content-start items-center">
                  <div className="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-2">
                    <div className="w-5 h-5 flex items-center justify-center text-green-600 dark:text-green-400">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3 flex flex-col">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">Procurement</h4>
                    <div className="mt-1 flex items-baseline">
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">Tender & Supply</div>
                    </div>
                  </div>
                </div>
                <div>
                  <Link
                    href="/customer/procurement"
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']"
                  >
                    Access Service
                  </Link>
                </div>
              </div>

              {/* Consumer Affairs Service */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex flex-col space-y-3">
                <div className="flex place-content-start items-center">
                  <div className="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-2">
                    <div className="w-5 h-5 flex items-center justify-center text-purple-600 dark:text-purple-400">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3 flex flex-col">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">Consumer Affairs</h4>
                    <div className="mt-1 flex items-baseline">
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">Reports & Support</div>
                    </div>
                  </div>
                </div>
                <div>
                  <Link
                    href="/customer/consumer-affairs"
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']"
                  >
                    Access Service
                  </Link>
                </div>
              </div>

              {/* Data Breach Service */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex flex-col space-y-3">
                <div className="flex place-content-start items-center">
                  <div className="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-2">
                    <div className="w-5 h-5 flex items-center justify-center text-red-600 dark:text-red-400">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3 flex flex-col">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">Data Breach</h4>
                    <div className="mt-1 flex items-baseline">
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">Report Data Misuse</div>
                    </div>
                  </div>
                </div>
                <div>
                  <Link
                    href="/customer/data-breach"
                    className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']"
                  >
                    Access Service
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

    

        {/* Application Processes Section */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
          <div className="p-6">
            <h3 className="text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-6">Application Processes</h3>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-4">
              {/* License Application Process */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-4">License Application Process</h4>
                <div className="space-y-3">
                  {[
                    {
                      step: 1,
                      title: 'Submit Application',
                      description: 'Fill out the application form with your details and submit required documents.',
                      icon: 'ri-file-edit-line',
                      bgColor: 'bg-blue-100 dark:bg-blue-900',
                      textColor: 'text-blue-600 dark:text-blue-400'
                    },
                    {
                      step: 2,
                      title: 'Application Review',
                      description: 'Our team reviews your application and may request additional information if needed.',
                      icon: 'ri-search-eye-line',
                      bgColor: 'bg-yellow-100 dark:bg-yellow-900',
                      textColor: 'text-yellow-600 dark:text-yellow-400'
                    },
                    {
                      step: 3,
                      title: 'Payment',
                      description: 'Once approved, you\'ll receive an invoice for the license fee that must be paid.',
                      icon: 'ri-bank-card-line',
                      bgColor: 'bg-green-100 dark:bg-green-900',
                      textColor: 'text-green-600 dark:text-green-400'
                    },
                    {
                      step: 4,
                      title: 'License Issuance',
                      description: 'After payment confirmation, your license will be issued and available for download.',
                      icon: 'ri-award-line',
                      bgColor: 'bg-purple-100 dark:bg-purple-900',
                      textColor: 'text-purple-600 dark:text-purple-400'
                    }
                  ].map((item) => (
                    <div key={item.step} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-8 w-8 rounded-full ${item.bgColor} ${item.textColor}`}>
                          <i className={`${item.icon} text-sm`}></i>
                        </div>
                      </div>
                      <div className="ml-3">
                        <h5 className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.title}</h5>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Procurement Application Process */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-4">Procurement Application Process</h4>
                <div className="space-y-3">
                  {[
                    {
                      step: 1,
                      title: 'Vendor Registration',
                      description: 'Register your company',
                      icon: 'ri-building-line',
                      bgColor: 'bg-green-100 dark:bg-green-900',
                      textColor: 'text-green-600 dark:text-green-400'
                    },
                    {
                      step: 2,
                      title: 'Tender Application',
                      description: 'Payment is made to access tender document and submit your proposal.',
                      icon: 'ri-file-list-3-line',
                      bgColor: 'bg-blue-100 dark:bg-blue-900',
                      textColor: 'text-blue-600 dark:text-blue-400'
                    },
                    {
                      step: 3,
                      title: 'Evaluation & Award',
                      description: 'Your proposal will be evaluated and you\'ll be notified of the award decision.',
                      icon: 'ri-trophy-line',
                      bgColor: 'bg-yellow-100 dark:bg-yellow-900',
                      textColor: 'text-yellow-600 dark:text-yellow-400'
                    },
                    {
                      step: 4,
                      title: 'Contract Execution',
                      description: 'Sign the contract and begin delivery of goods or services as specified.',
                      icon: 'ri-pen-nib-line',
                      bgColor: 'bg-purple-100 dark:bg-purple-900',
                      textColor: 'text-purple-600 dark:text-purple-400'
                    }
                  ].map((item) => (
                    <div key={item.step} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-8 w-8 rounded-full ${item.bgColor} ${item.textColor}`}>
                          <i className={`${item.icon} text-sm`}></i>
                        </div>
                      </div>
                      <div className="ml-3">
                        <h5 className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.title}</h5>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Consumer Affairs Application Process */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-4">Consumer Affairs Application Process</h4>
                <div className="space-y-3">
                  {[
                    {
                      step: 1,
                      title: 'Submit Complaint',
                      description: 'File your complaint with detailed information about the issue and supporting evidence.',
                      icon: 'ri-alarm-warning-line',
                      bgColor: 'bg-red-100 dark:bg-red-900',
                      textColor: 'text-red-600 dark:text-red-400'
                    },
                    {
                      step: 2,
                      title: 'Investigation',
                      description: 'Our team will investigate the matter and may contact all parties involved.',
                      icon: 'ri-search-line',
                      bgColor: 'bg-orange-100 dark:bg-orange-900',
                      textColor: 'text-orange-600 dark:text-orange-400'
                    },
                    {
                      step: 3,
                      title: 'Mediation',
                      description: 'We facilitate mediation between parties to reach a mutually acceptable resolution.',
                      icon: 'ri-scales-line',
                      bgColor: 'bg-blue-100 dark:bg-blue-900',
                      textColor: 'text-blue-600 dark:text-blue-400'
                    },
                    {
                      step: 4,
                      title: 'Resolution',
                      description: 'Final resolution is communicated to all parties with any necessary enforcement actions.',
                      icon: 'ri-check-line',
                      bgColor: 'bg-green-100 dark:bg-green-900',
                      textColor: 'text-green-600 dark:text-green-400'
                    }
                  ].map((item) => (
                    <div key={item.step} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-8 w-8 rounded-full ${item.bgColor} ${item.textColor}`}>
                          <i className={`${item.icon} text-sm`}></i>
                        </div>
                      </div>
                      <div className="ml-3">
                        <h5 className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.title}</h5>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Data Breach Application Process */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-4">Personal Data Protection Reporting</h4>
                <div className="space-y-3">
                  {[
                    {
                      step: 1,
                      title: 'Report Unauthorized Data Usage',
                      description: 'Report when your personal data has been leaked or is being used without your permission.',
                      icon: 'ri-error-warning-line',
                      bgColor: 'bg-red-100 dark:bg-red-900',
                      textColor: 'text-red-600 dark:text-red-400'
                    },
                    {
                      step: 2,
                      title: 'Provide Evidence',
                      description: 'Submit any evidence of unauthorized data usage or leakage (screenshots, emails, messages).',
                      icon: 'ri-file-list-3-line',
                      bgColor: 'bg-orange-100 dark:bg-orange-900',
                      textColor: 'text-orange-600 dark:text-orange-400'
                    },
                    {
                      step: 3,
                      title: 'MACRA Investigation',
                      description: 'MACRA will investigate the reported incident and contact the responsible parties.',
                      icon: 'ri-search-line',
                      bgColor: 'bg-blue-100 dark:bg-blue-900',
                      textColor: 'text-blue-600 dark:text-blue-400'
                    },
                    {
                      step: 4,
                      title: 'Resolution & Protection',
                      description: 'Receive updates on actions taken to protect your data and prevent future unauthorized usage.',
                      icon: 'ri-shield-user-line',
                      bgColor: 'bg-green-100 dark:bg-green-900',
                      textColor: 'text-green-600 dark:text-green-400'
                    }
                  ].map((item) => (
                    <div key={item.step} className="flex items-start">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-8 w-8 rounded-full ${item.bgColor} ${item.textColor}`}>
                          <i className={`${item.icon} text-sm`}></i>
                        </div>
                      </div>
                      <div className="ml-3">
                        <h5 className="text-xs font-medium text-gray-900 dark:text-gray-100">{item.title}</h5>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main dashboard content */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Left Column - Empty for now or other content */}
          <div className="lg:col-span-2 space-y-6">
            {/* This space can be used for other dashboard content */}
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Upcoming Payments */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Upcoming Payments</h3>
                  <Link href="/customer/payments" className="text-sm text-primary hover:text-primary">
                    View all →
                  </Link>
                </div>
                <div className="space-y-4">
                  {upcomingPayments.map((payment) => (
                    <PaymentCard key={payment.id} {...payment} />
                  ))}
                  {upcomingPayments.length === 0 && (
                    <div className="text-center py-6">
                      <div className="w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3">
                        <i className="ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500"></i>
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">No pending payments</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 2FA Setup Popup */}
      <TwoFactorSetupPopup
        isOpen={show2FAPopup}
        onClose={() => setShow2FAPopup(false)}
        isCustomerPortal={true}
        userName={sessionStorage.getItem('user_name') || user?.first_name || 'User'}
      />
    </CustomerLayout>
  );
};

export default CustomerDashboard;