'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { EvaluationLayout, DocumentCard } from '@/components/evaluation';

import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { useEvaluationData } from '@/hooks/useEvaluationData';

interface EvaluateDocumentsPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateDocumentsPage: React.FC<EvaluateDocumentsPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Use evaluation data hook
  const { data, loading, error } = useEvaluationData(applicationId);
  // Dynamic navigation hook - evaluation context
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep,
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'documents',
    licenseCategoryId,
    applicationId,
    context: 'evaluate',
    licenseType
  });

  // Set license category ID when data loads
  useEffect(() => {
    if (data.application?.license_category_id) {
      setLicenseCategoryId(data.application.license_category_id);
    }
  }, [data.application]);

  // Navigation handlers - use dynamic navigation
  const handleNext = () => {
    dynamicHandleNext();
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!data.application && !loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="documents"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
      {/* Documents Information Display */}
      <div className="space-y-6">
        <DocumentCard application={data.application} />
      </div>
      </EvaluationLayout>
  );
};

export default EvaluateDocumentsPage;
