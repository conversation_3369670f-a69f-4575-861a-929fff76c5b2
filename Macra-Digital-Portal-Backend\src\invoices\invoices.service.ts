import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Invoices, InvoiceStatus } from '../entities/invoices.entity';
import { Applications } from '../entities/applications.entity';
import { Applicants } from '../entities/applicant.entity';
import { LicenseCategories } from '../entities/license-categories.entity';
import { Payment } from '../payments/entities/payment.entity';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { ApplicationsService } from '../applications/applications.service';

export interface CreateInvoiceDto {
  client_id: string;
  amount: number;
  entity_type: string;
  entity_id: string;
  description: string;
  due_date?: Date;
  items?: any[];
}

export interface UpdateInvoiceDto {
  amount?: number;
  status?: InvoiceStatus;
  description?: string;
  due_date?: Date;
  items?: any[];
}

export interface InvoiceFilters {
  status?: string;
  entity_type?: string;
  entity_id?: string;
  client_id?: string;
}

@Injectable()
export class InvoicesService {
  constructor(
    @InjectRepository(Invoices)
    private invoicesRepository: Repository<Invoices>,
    @InjectRepository(Applications)
    private applicationsRepository: Repository<Applications>,
    @InjectRepository(Applicants)
    private applicantsRepository: Repository<Applicants>,
    @InjectRepository(LicenseCategories)
    private licenseCategoriesRepository: Repository<LicenseCategories>,
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
    private notificationHelper: NotificationHelperService,
    @Inject(forwardRef(() => ApplicationsService))
    private applicationsService: ApplicationsService,
  ) {}

  async create(createDto: CreateInvoiceDto, userId: string | null): Promise<Invoices> {
    try {
      // Validate userId - should not be null when authentication is enabled
      if (!userId) {
        throw new BadRequestException('User authentication required to create invoice');
      }

      // Generate invoice number
      const invoiceNumber = await this.generateInvoiceNumber();
      const invoice = this.invoicesRepository.create({
        ...createDto,
        invoice_number: invoiceNumber,
        status: InvoiceStatus.PENDING,
        issue_date: new Date(),
        due_date: createDto.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        created_by: userId,
      });

      const savedInvoice = await this.invoicesRepository.save(invoice);
      return savedInvoice;
    } catch (error) {
      throw error;
    }
  }

  async findAll(filters: InvoiceFilters = {}): Promise<Invoices[]> {
    console.log('🔍 InvoicesService.findAll called with filters:', filters);

    const query = this.invoicesRepository.createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.client', 'client')
      .leftJoinAndSelect('invoice.creator', 'creator')
      .leftJoinAndSelect('invoice.updater', 'updater');

    if (filters.status) {
      console.log('🔍 Adding status filter:', filters.status);
      query.andWhere('invoice.status = :status', { status: filters.status });
    }

    if (filters.entity_type) {
      query.andWhere('invoice.entity_type = :entity_type', { entity_type: filters.entity_type });
    }

    if (filters.entity_id) {
      query.andWhere('invoice.entity_id = :entity_id', { entity_id: filters.entity_id });
    }

    if (filters.client_id) {
      query.andWhere('invoice.client_id = :client_id', { client_id: filters.client_id });
    }

    query.orderBy('invoice.created_at', 'DESC');

    const result = await query.getMany();
    return result;
  }

  async findOne(id: string): Promise<any> {
    const invoice = await this.invoicesRepository.findOne({
      where: { invoice_id: id },
      relations: ['client', 'creator', 'updater'],
    });

    if (!invoice) {
      throw new NotFoundException(`Invoice with ID ${id} not found`);
    }

    const amountPaid = await this.getPaidAmountForInvoice(invoice.invoice_id);
    const balance = Math.max(0, Number(invoice.amount) - amountPaid);
    return {...invoice, balance: balance };
  }

  async findByEntity(entityType: string, entityId: string): Promise<Invoices[]> {
    return await this.invoicesRepository.find({
      where: { entity_type: entityType, entity_id: entityId },
      relations: ['client', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(id: string, updateDto: UpdateInvoiceDto, userId: string | null): Promise<Invoices> {
    // Validate userId - should not be null when authentication is enabled
    if (!userId) {
      throw new BadRequestException('User authentication required to update invoice');
    }

    const invoice = await this.findOne(id);

    Object.assign(invoice, updateDto);
    invoice.updated_by = userId;

    return await this.invoicesRepository.save(invoice);
  }

  async remove(id: string): Promise<void> {
    const invoice = await this.findOne(id);
    await this.invoicesRepository.softDelete(id);
  }

  async sendInvoice(id: string, userId: string | null): Promise<Invoices> {
    // Validate userId - should not be null when authentication is enabled
    if (!userId) {
      throw new BadRequestException('User authentication required to send invoice');
    }

    const invoice = await this.findOne(id);

    if (invoice.status !== InvoiceStatus.PENDING) {
      throw new BadRequestException('Only pending invoices can be sent');
    }

    invoice.status = InvoiceStatus.SENT;
    invoice.updated_by = userId;

    return await this.invoicesRepository.save(invoice);
  }

  async markAsPaid(id: string, userId: string | null): Promise<Invoices> {
    // Validate userId - should not be null when authentication is enabled
    if (!userId) {
      throw new BadRequestException('User authentication required to mark invoice as paid');
    }

    const invoice = await this.findOne(id);

    if (invoice.status === InvoiceStatus.PAID) {
      throw new BadRequestException('Invoice is already marked as paid');
    }

    invoice.status = InvoiceStatus.PAID;
    invoice.updated_by = userId;

    return await this.invoicesRepository.save(invoice);
  }

  async generateApplicationInvoice(
    applicationId: string,
    data: { amount: number; description: string; items?: any[] },
    userId: string | null
  ): Promise<Invoices> {
    try {
      // Validate userId - should not be null when authentication is enabled
      if (!userId) {
        throw new BadRequestException('User authentication required to generate invoice');
      }

      // Validate input data
      if (!data || typeof data !== 'object') {
        throw new BadRequestException('Invalid invoice data format');
      }

      if (!data.amount || isNaN(Number(data.amount)) || Number(data.amount) <= 0) {
        throw new BadRequestException(`Invoice amount must be greater than 0, received: ${data.amount}`);
      }

      if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
        throw new BadRequestException('Invoice description is required');
      }

      // Get application with applicant and license category
      const application = await this.applicationsRepository.findOne({
        where: { application_id: applicationId },
        relations: ['applicant', 'license_category'],
      });

      if (!application) {
        throw new NotFoundException(`Application with ID ${applicationId} not found`);
      }

      if (!application.applicant) {
        throw new NotFoundException(`Applicant not found for application ${applicationId}`);
      }

      // Check if invoice already exists for this application
      const existingInvoice = await this.invoicesRepository.findOne({
        where: { entity_type: 'application', entity_id: applicationId },
      });

      let invoice: Invoices;

    if (existingInvoice) {
      // Update existing invoice
      existingInvoice.amount = data.amount;
      existingInvoice.description = data.description;
      existingInvoice.items = data.items;
      existingInvoice.updated_by = userId;

      // If invoice was previously paid or cancelled, reset status to pending
      if (existingInvoice.status === 'paid' || existingInvoice.status === 'cancelled') {
        existingInvoice.status = InvoiceStatus.PENDING;
      }

      invoice = await this.invoicesRepository.save(existingInvoice);
    } else {
      // Create new invoice
      const createInvoiceDto = {
        client_id: application.applicant.applicant_id,
        amount: Number(data.amount),
        entity_type: 'application',
        entity_id: applicationId,
        description: data.description.trim(),
        items: data.items || [],
      };

      try {
        invoice = await this.create(createInvoiceDto, userId);
      } catch (createError) {
        throw new InternalServerErrorException(`Failed to create invoice: ${createError.message}`);
      }
    }

    // Update application status to pending payment
    try {
      if (this.applicationsService && typeof this.applicationsService.updateStatus === 'function') {
        await this.applicationsService.updateStatus(applicationId, 'pending_payment', userId);
      } else {
        console.warn(`⚠️ ApplicationsService not available, skipping status update`);
      }
    } catch (error) {
      console.error(`❌ Failed to update application status for ${applicationId}:`, error);
      // Don't fail invoice creation if status update fails
    }

    // Send invoice email to applicant
    try {
      await this.sendInvoiceEmail(invoice, application);
      console.log(`✅ Invoice email sent to ${application.applicant.email}`);
    } catch (error) {
      console.error(`❌ Failed to send invoice email for ${invoice.invoice_id}:`, error);
      // Don't fail invoice creation if email fails
    }

    return invoice;
    } catch (error) {
      console.error(`❌ Error generating invoice for application ${applicationId}:`, error);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to generate invoice for application ${applicationId}: ${error.message}`);
    }
  }

  private async generateUniqueInvoiceNumber(entityManager?: any): Promise<string> {
    const repository = entityManager ? entityManager.getRepository(Invoices) : this.invoicesRepository;
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const prefix = `INV-${year}${month}`;

    try {
      // Get the highest sequence number for this month
      const latestInvoice = await repository
        .createQueryBuilder('invoice')
        .where('invoice.invoice_number LIKE :pattern', {
          pattern: `${prefix}-%`
        })
        .orderBy('invoice.invoice_number', 'DESC')
        .getOne();

      let nextSequence = 1;

      if (latestInvoice) {
        // Extract sequence number from the latest invoice
        const parts = latestInvoice.invoice_number.split('-');
        if (parts.length === 3) {
          const lastSequence = parseInt(parts[2], 10);
          if (!isNaN(lastSequence)) {
            nextSequence = lastSequence + 1;
          }
        }
      }

      // Generate invoice number and check for uniqueness
      let attempts = 0;
      const maxAttempts = 100; // Prevent infinite loop

      while (attempts < maxAttempts) {
        const sequence = String(nextSequence).padStart(4, '0');
        const invoiceNumber = `${prefix}-${sequence}`;

        // Check if this invoice number already exists
        const existingInvoice = await repository.findOne({
          where: { invoice_number: invoiceNumber }
        });

        if (!existingInvoice) {
          console.log(`✅ Generated unique invoice number: ${invoiceNumber}`);
          return invoiceNumber;
        }

        // If exists, increment and try again
        nextSequence++;
        attempts++;
        console.warn(`⚠️ Invoice number ${invoiceNumber} already exists, trying next sequence: ${nextSequence}`);
      }

      // If we couldn't find a unique number after max attempts, use timestamp fallback
      throw new Error(`Could not generate unique invoice number after ${maxAttempts} attempts`);

    } catch (error) {
      console.error('Error generating invoice number:', error);

      // Fallback to timestamp-based number with uniqueness check
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        const timestamp = Date.now().toString().slice(-6);
        const fallbackNumber = `${prefix}-${timestamp}`;

        const existingInvoice = await repository.findOne({
          where: { invoice_number: fallbackNumber }
        });

        if (!existingInvoice) {
          console.log(`✅ Generated fallback invoice number: ${fallbackNumber}`);
          return fallbackNumber;
        }

        attempts++;
        // Wait a millisecond to get a different timestamp
        await new Promise(resolve => setTimeout(resolve, 1));
      }

      // Final fallback with random component
      const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
      const finalFallback = `${prefix}-${randomSuffix}`;
      console.warn(`⚠️ Using final fallback invoice number: ${finalFallback}`);
      return finalFallback;
    }
  }

  /**
   * Generate invoice number with proper sequencing
   */
  private async generateInvoiceNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const prefix = `INV-${year}${month}`;

    try {
      // Get the highest sequence number for this month
      const latestInvoice = await this.invoicesRepository
        .createQueryBuilder('invoice')
        .where('invoice.invoice_number LIKE :pattern', {
          pattern: `${prefix}-%`
        })
        .orderBy('invoice.invoice_number', 'DESC')
        .getOne();

      let nextSequence = 1;

      if (latestInvoice) {
        // Extract sequence number from the latest invoice
        const parts = latestInvoice.invoice_number.split('-');
        if (parts.length === 3) {
          const lastSequence = parseInt(parts[2], 10);
          if (!isNaN(lastSequence)) {
            nextSequence = lastSequence + 1;
          }
        }
      }

      // Generate invoice number and check for uniqueness (with retry)
      let attempts = 0;
      const maxAttempts = 10; // Reduced for simpler logic

      while (attempts < maxAttempts) {
        const sequence = String(nextSequence).padStart(4, '0');
        const invoiceNumber = `${prefix}-${sequence}`;

        // Check if this invoice number already exists
        const existingInvoice = await this.invoicesRepository.findOne({
          where: { invoice_number: invoiceNumber }
        });

        if (!existingInvoice) {
          return invoiceNumber;
        }

        // If exists, increment and try again
        nextSequence++;
        attempts++;
      }

      // If we couldn't find a unique number, use timestamp fallback
      const timestamp = Date.now().toString().slice(-6);
      const fallbackNumber = `${prefix}-${timestamp}`;
      return fallbackNumber;

    } catch (error) {
      // Final fallback with timestamp
      const timestamp = Date.now().toString().slice(-6);
      const fallbackNumber = `${prefix}-${timestamp}`;
      return fallbackNumber;
    }
  }

  async getApplicationInvoiceStatus(applicationId: string): Promise<{
    hasInvoice: boolean;
    invoice?: Invoices;
    status?: 'paid' | 'pending' | 'overdue' | 'none';
  }> {
    const invoices = await this.findByEntity('application', applicationId);

    if (invoices.length === 0) {
      return { hasInvoice: false, status: 'none' };
    }

    // Get the most recent invoice
    const latestInvoice = invoices[0]; // Already ordered by created_at DESC

    let status: 'paid' | 'pending' | 'overdue' = 'pending';

    if (latestInvoice.status === InvoiceStatus.PAID) {
      status = 'paid';
    } else if (latestInvoice.status === InvoiceStatus.OVERDUE) {
      status = 'overdue';
    } else if (latestInvoice.status === InvoiceStatus.SENT || latestInvoice.status === InvoiceStatus.PENDING) {
      // Check if overdue
      const dueDate = new Date(latestInvoice.due_date);
      const now = new Date();
      if (now > dueDate) {
        status = 'overdue';
        // Update status in database
        await this.update(latestInvoice.invoice_id, { status: InvoiceStatus.OVERDUE }, latestInvoice.created_by);
        latestInvoice.status = InvoiceStatus.OVERDUE;
      } else {
        status = 'pending';
      }
    }

    return {
      hasInvoice: true,
      invoice: latestInvoice,
      status
    };
  }

  async getApplicationDetailsForInvoice(applicationId: string): Promise<{
    application: Applications;
    defaultInvoiceData: {
      amount: number;
      description: string;
      items: any[];
    };
  }> {
    const application = await this.applicationsRepository.findOne({
      where: { application_id: applicationId },
      relations: ['applicant', 'license_category'],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${applicationId} not found`);
    }

    // Parse the fee from license category (assuming it's stored as string)
    const categoryFee = application.license_category.fee || 0;

    const defaultInvoiceData = {
      amount: categoryFee,
      description: `License Application Fee - ${application.license_category.name}`,
      items: [
        {
          item_id: `license_fee_${Date.now()}`,
          description: `${application.license_category.name} License Fee`,
          quantity: 1,
          unit_price: categoryFee
        }
      ]
    };

    return {
      application,
      defaultInvoiceData
    };
  }

  /**
   * Send invoice email to applicant
   */
  private async sendInvoiceEmail(invoice: Invoices, application: Applications): Promise<void> {
    try {
      console.log(`🔧 Preparing to send invoice email for invoice ${invoice.invoice_number}`);

      if (!application) {
        console.error(`❌ Application object is null or undefined`);
        throw new Error('Application object is required');
      }

      if (!application.applicant) {
        console.error(`❌ Applicant not found for application ${application.application_id}`);
        throw new Error('Applicant not found');
      }

      if (!application.applicant.email) {
        console.error(`❌ Applicant email not found for ${application.applicant.applicant_id}`);
        throw new Error('Applicant email not found');
      }

      console.log(`📧 Sending invoice email to ${application.applicant.email}`);

      // Validate invoice data
      if (!invoice.invoice_number) {
        console.error(`❌ Invoice number is missing`);
        throw new Error('Invoice number is required');
      }

      if (!invoice.due_date) {
        console.error(`❌ Invoice due date is missing`);
        throw new Error('Invoice due date is required');
      }

      // Format due date safely
      let dueDateStr = '';
      try {
        dueDateStr = invoice.due_date.toLocaleDateString();
      } catch (dateError) {
        console.warn(`⚠️ Error formatting due date, using string representation:`, dateError);
        dueDateStr = String(invoice.due_date);
      }

      // Send invoice notification email
      try {
        await this.notificationHelper.notifyInvoiceGenerated(
          application.application_id,
          application.applicant.applicant_id,
          application.applicant.email,
          application.application_number,
          invoice.invoice_number,
          Number(invoice.amount),
          dueDateStr,
          invoice.description,
          invoice.created_by,
          application.applicant.name,
          application.license_category?.name || 'License'
        );

        console.log(`✅ Invoice email sent successfully to ${application.applicant.email}`);
      } catch (notificationError) {
        console.error('❌ Failed to send invoice email via notification helper:', notificationError);
        throw notificationError;
      }
    } catch (error) {
      console.error(`❌ Error in sendInvoiceEmail:`, error);
      // Don't rethrow to prevent invoice creation failure
      // Just log the error and continue
    }
  }

  async getPaidAmountForInvoice(invoiceId: string): Promise<number> {
    const paidPayments = await this.paymentsRepository.find({
      where: {
        invoice_id: invoiceId,
        status: 'approved'
      }
    });
    return paidPayments.reduce((sum, payment) => sum + Number(payment.amount), 0);
  }

  /**
   * Get invoices filtered by customer (based on user's applications)
   */
  async getInvoicesByCustomer(userId: string, filters: any): Promise<any> {
    try {
      // First, get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        return {
          data: [],
          meta: {
            itemsPerPage: filters.limit || 10,
            totalItems: 0,
            currentPage: filters.page || 1,
            totalPages: 0,
          },
        };
      }

      const applicationIds = userApplications.map(app => app.application_id);
      console.log(`📋 Found ${applicationIds.length} applications for user ${userId}`);

      // Build query for invoices related to user's applications
      const queryBuilder = this.invoicesRepository
        .createQueryBuilder('invoice')
        .leftJoinAndSelect('invoice.client', 'client')
        .where('invoice.entity_type = :entityType', { entityType: 'application' })
        .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds });

      // Apply filters
      if (filters.status) {
        const statuses = filters.status.split(',').map((s: string) => s.trim());
        queryBuilder.andWhere('invoice.status IN (:...statuses)', { statuses });
      }

      if (filters.search) {
        queryBuilder.andWhere(
          '(invoice.invoice_number LIKE :search OR invoice.description LIKE :search)',
          { search: `%${filters.search}%` }
        );
      }

      // Add pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const offset = (page - 1) * limit;

      queryBuilder
        .orderBy('invoice.created_at', 'DESC')
        .skip(offset)
        .take(limit);

      const [invoices, totalItems] = await queryBuilder.getManyAndCount();

      // Enhance invoices with payment information
      const enhancedInvoices = await Promise.all(
        invoices.map(async (invoice) => {
          try {
            // Get total paid amount for this invoice
            const paidPayments = await this.paymentsRepository
              .createQueryBuilder('payment')
              .where('payment.invoice_id = :invoiceId', { invoiceId: invoice.invoice_id })
              .orWhere('payment.invoice_number = :invoiceNumber', { invoiceNumber: invoice.invoice_number })
              .andWhere('payment.status = :status', { status: 'approved' })
              .getMany();

            const totalPaid = paidPayments.reduce((sum: number, payment) => sum + parseFloat(payment.amount.toString()), 0);
            const balance = Math.max(0, parseFloat(invoice.amount.toString()) - totalPaid);

            return {
              ...invoice,
              paid_amount: totalPaid,
              balance: balance,
              payment_status: balance === 0 ? 'FULLY_PAID' : 'PENDING'
            };
          } catch (error) {
            console.error(`Error calculating payments for invoice ${invoice.invoice_id}:`, error);
            return {
              ...invoice,
              paid_amount: 0,
              balance: parseFloat(invoice.amount.toString()),
              payment_status: 'PENDING'
            };
          }
        })
      );


      return {
        data: enhancedInvoices,
        meta: {
          itemsPerPage: limit,
          totalItems,
          currentPage: page,
          totalPages: Math.ceil(totalItems / limit),
        },
      };
    } catch (error) {
      console.error(`❌ Error getting invoices for customer ${userId}:`, error);
      throw new InternalServerErrorException(`Failed to get customer invoices: ${error.message}`);
    }
  }

  /**
   * Get invoice statistics for a specific customer
   */
  async getInvoiceStatisticsByCustomer(userId: string): Promise<any> {
    try {
      console.log(`📊 Getting invoice statistics for customer: ${userId}`);

      // Get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        return {
          totalInvoices: 0,
          pendingInvoices: 0,
          paidInvoices: 0,
          overdueInvoices: 0,
          totalAmount: 0,
          pendingAmount: 0,
          paidAmount: 0,
          overdueAmount: 0,
        };
      }

      const applicationIds = userApplications.map(app => app.application_id);

      // Get invoice statistics
      const invoices = await this.invoicesRepository
        .createQueryBuilder('invoice')
        .where('invoice.entity_type = :entityType', { entityType: 'application' })
        .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds })
        .getMany();

      const stats = {
        totalInvoices: invoices.length,
        pendingInvoices: invoices.filter(inv => inv.status === 'pending').length,
        paidInvoices: invoices.filter(inv => inv.status === 'paid').length,
        overdueInvoices: invoices.filter(inv => inv.status === 'overdue').length,
        totalAmount: invoices.reduce((sum, inv) => sum + Number(inv.amount), 0),
        pendingAmount: invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + Number(inv.amount), 0),
        paidAmount: invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + Number(inv.amount), 0),
        overdueAmount: invoices.filter(inv => inv.status === 'overdue').reduce((sum, inv) => sum + Number(inv.amount), 0),
      };

      console.log(`✅ Invoice statistics for customer ${userId}:`, stats);
      return stats;
    } catch (error) {
      console.error(`❌ Error getting invoice statistics for customer ${userId}:`, error);
      throw new InternalServerErrorException(`Failed to get customer invoice statistics: ${error.message}`);
    }
  }

  /**
   * Get specific invoice by customer (with ownership validation)
   */
  async getInvoiceByCustomer(invoiceId: string, userId: string): Promise<Invoices> {
    try {
      console.log(`🔍 Getting invoice ${invoiceId} for customer: ${userId}`);

      // Get all applications created by this user
      const userApplications = await this.applicationsRepository.find({
        where: { created_by: userId },
        select: ['application_id'],
      });

      if (userApplications.length === 0) {
        throw new NotFoundException(`Invoice not found or not accessible`);
      }

      const applicationIds = userApplications.map(app => app.application_id);

      // Find the invoice and validate ownership
      const invoice = await this.invoicesRepository
        .createQueryBuilder('invoice')
        .leftJoinAndSelect('invoice.client', 'client')
        .where('invoice.invoice_id = :invoiceId', { invoiceId })
        .andWhere('invoice.entity_type = :entityType', { entityType: 'application' })
        .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds })
        .getOne();

      if (!invoice) {
        throw new NotFoundException(`Invoice not found or not accessible`);
      }

      console.log(`✅ Found invoice ${invoiceId} for customer ${userId}`);
      return invoice;
    } catch (error) {
      console.error(`❌ Error getting invoice ${invoiceId} for customer ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get customer invoice: ${error.message}`);
    }
  }

  /**
   * Get invoices for a specific application by customer (with ownership validation)
   */
  async getApplicationInvoicesByCustomer(applicationId: string, userId: string): Promise<Invoices[]> {
    try {
      console.log(`🔍 Getting invoices for application ${applicationId} by customer: ${userId}`);

      // First, validate that the application belongs to the user
      const application = await this.applicationsRepository.findOne({
        where: {
          application_id: applicationId,
          created_by: userId
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found or not accessible`);
      }

      // Get invoices for this application
      const invoices = await this.invoicesRepository.find({
        where: {
          entity_type: 'application',
          entity_id: applicationId,
        },
        relations: ['client'],
        order: { created_at: 'DESC' },
      });

      console.log(`✅ Found ${invoices.length} invoices for application ${applicationId}`);
      return invoices;
    } catch (error) {
      console.error(`❌ Error getting application invoices for customer ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get application invoices: ${error.message}`);
    }
  }

  /**
   * Get invoice status for a specific application by customer (with ownership validation)
   */
  async getApplicationInvoiceStatusByCustomer(applicationId: string, userId: string): Promise<any> {
    try {
      console.log(`🔍 Getting invoice status for application ${applicationId} by customer: ${userId}`);

      // First, validate that the application belongs to the user
      const application = await this.applicationsRepository.findOne({
        where: {
          application_id: applicationId,
          created_by: userId
        },
      });

      if (!application) {
        throw new NotFoundException(`Application not found or not accessible`);
      }

      // Get invoices for this application
      const invoices = await this.invoicesRepository.find({
        where: {
          entity_type: 'application',
          entity_id: applicationId,
        },
        order: { created_at: 'DESC' },
      });

      if (invoices.length === 0) {
        return { hasInvoice: false, status: 'none' };
      }

      // Get the most recent invoice
      const latestInvoice = invoices[0];
      let status: 'paid' | 'pending' | 'overdue' = 'pending';

      if (latestInvoice.status === 'paid') {
        status = 'paid';
      } else if (latestInvoice.status === 'overdue') {
        status = 'overdue';
      } else if (latestInvoice.status === 'sent' || latestInvoice.status === 'pending') {
        // Check if overdue
        const dueDate = new Date(latestInvoice.due_date);
        const now = new Date();
        if (now > dueDate) {
          status = 'overdue';
        } else {
          status = 'pending';
        }
      }

      console.log(`✅ Invoice status for application ${applicationId}: ${status}`);
      return {
        hasInvoice: true,
        invoice: latestInvoice,
        status,
      };
    } catch (error) {
      console.error(`❌ Error getting application invoice status for customer ${userId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to get application invoice status: ${error.message}`);
    }
  }


}
