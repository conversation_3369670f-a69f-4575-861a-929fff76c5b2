'use client';

import { customerApi } from '@/lib/customer-api';
import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { useDebouncedCallback } from 'use-debounce';
import { Address, SearchPostcodes, PostalCodeLookupResult, CreateAddressDto, EditAddressData, SequentialAddressData } from '@/types/address_types';

// Re-export DTO types for backward compatibility
export type {
  CreateAddressDto as CreateAddressData,
  EditAddressData
} from '@/types/address_types';

// Re-export types from the centralized types file
export type { SearchPostcodes, PostalCodeLookupResult } from '@/types/address_types';

export const initialAddressData: CreateAddressDto = {
  address_type: 'business',
  entity_type: 'applicant',
  entity_id: '',
  address_line_1: '',
  address_line_2: '',
  address_line_3: '',
  postal_code: '',
  country: '',
  city: ''
};

// Address interface is imported from types

// Cache for addresses and postcode suggestions
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class AddressCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 1480 * 60 * 1000; // 24 hours since location data does not frequently change

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const addressCache = new AddressCache();

// Address service using customer API
export const addressService = {
  async createAddress(data: CreateAddressDto): Promise<any> {
    const response = await customerApi.createAddress(data);
    // Invalidate relevant caches after creation
    addressCache.invalidatePattern(`addresses_.*`);
    addressCache.invalidatePattern(`entity_addresses_${data.entity_type}_${data.entity_id}`);
    return response;
  },

  async getAddress(id: string): Promise<any> {
    const cacheKey = `address_${id}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    const response = await customerApi.getAddress(id);
    addressCache.set(cacheKey, response);
    return response;
  },

  async editAddress(data: EditAddressData): Promise<any> {
    const response = await customerApi.editAddress(data);
    // Invalidate relevant caches after update
    addressCache.invalidatePattern(`address_${data.address_id}`);
    addressCache.invalidatePattern(`addresses_.*`);
    if (data.entity_type && data.entity_id) {
      addressCache.invalidatePattern(`entity_addresses_${data.entity_type}_${data.entity_id}`);
    }
    return response;
  },

  async getAddressesByEntity(entityType: string, entityId: string): Promise<any> {
    const cacheKey = `entity_addresses_${entityType}_${entityId}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    const response = await customerApi.getAddressesByEntity(entityType, entityId);
    addressCache.set(cacheKey, response);
    return response;
  },

  async getAllAddresses(): Promise<any> {
    const cacheKey = 'addresses_all';
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    const response = await customerApi.getAddresses();
    addressCache.set(cacheKey, response);
    return response;
  },

  // Prefetch all postal code data for caching with pagination
  async prefetchPostalCodeData(): Promise<any> {
    const cacheKey = 'all_postal_codes';
    const cached = addressCache.get(cacheKey);
    if (cached) {
      console.log('Using cached postal code data');
      return cached;
    }

    console.log('Prefetching all postal code data...');

    // Fetch all postal codes using pagination
    let allPostalCodes: any[] = [];
    let currentPage = 1;
    let hasMorePages = true;
    const limit = 1000; // Fetch 1000 records per page
    let totalRecords = 0;

    while (hasMorePages) {
      try {
        console.log(`Requesting page ${currentPage} with limit ${limit}`);
        const response = await customerApi.getAllPostcodes({
          page: Number(currentPage),
          limit: Number(limit)
        });

        if (response.data && response.data.length > 0) {
          allPostalCodes = [...allPostalCodes, ...response.data];
          console.log(`Fetched page ${currentPage}: ${response.data.length} records`);

          // Use pagination metadata if available, otherwise fallback to data length check
          if (response.meta) {
            totalRecords = response.meta.total;
            hasMorePages = response.meta.hasNext;
            console.log(`Pagination info: ${allPostalCodes.length}/${totalRecords} records, hasNext: ${hasMorePages}`);
          } else {
            // Fallback: assume more pages if we got a full page
            hasMorePages = response.data.length === limit;
          }

          currentPage++;
        } else {
          hasMorePages = false;
        }
      } catch (error: any) {
        console.error(`Failed to fetch page ${currentPage}:`, error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          params: { page: currentPage, limit: limit }
        });
        hasMorePages = false;
      }
    }

    const consolidatedResponse = {
      data: allPostalCodes,
      meta: {
        total: totalRecords || allPostalCodes.length,
        cached: true,
        pages_fetched: currentPage - 1
      }
    };
    addressCache.set(cacheKey, consolidatedResponse, 1480 * 60 * 1000); // Cache for 24 hours
    console.log(`Postal code data prefetched and cached: ${allPostalCodes.length} total records from ${currentPage - 1} pages`);
    return consolidatedResponse;
  },

  // Fetch a single postcode entry by postal_code and return region, district and location to fill edit address modal
  async getPostcodeDetails(postalCode: string): Promise<PostalCodeLookupResult | null> {
    if (!postalCode?.trim()) {
      console.warn('getPostcodeDetails: No postal code provided');
      return null;
    }

    const cacheKey = `postcode_details_${postalCode}`;
    const cached = addressCache.get(cacheKey) as PostalCodeLookupResult | undefined;
    if (cached) {
      console.log(`Postcode details for ${postalCode} retrieved from cache`);
      return cached;
    }

    try {
      console.log(`Fetching postcode details for: ${postalCode}`);
      const response = await customerApi.getPostcodeByPostalCode(postalCode);

      if (response) {
        // Cache the result for 30 minutes
        addressCache.set(cacheKey, response, 30 * 60 * 1000);
        console.log(`Postcode details cached for ${postalCode}:`, {
          region: response.region,
          district: response.district,
          location: response.location,
          postal_code: response.postal_code
        });
        return response;
      } else {
        console.warn(`No postcode details found for: ${postalCode}`);
        return null;
      }
    } catch (error: any) {
      console.error(`Failed to fetch postcode details for ${postalCode}:`, error);

      // Return null on error to allow graceful handling
      return null;
    }
  },

  // Helper function to populate address form with postcode details
  async populateAddressFromPostcode(postalCode: string): Promise<Partial<SequentialAddressData> | null> {
    const postcodeDetails = await this.getPostcodeDetails(postalCode);

    if (!postcodeDetails) {
      return null;
    }

    // Convert PostalCodeLookupResult to SequentialAddressData format
    const addressData: Partial<SequentialAddressData> = {
      country: 'Malawi', // Assuming all postal codes are for Malawi
      region: postcodeDetails.region,
      district: postcodeDetails.district,
      location: postcodeDetails.location,
      postal_code: postcodeDetails.postal_code,
      // Keep existing address lines if any
      address_line_1: '',
      address_line_2: '',
    };

    console.log(`Address data populated from postcode ${postalCode}:`, addressData);
    return addressData;
  },


  async searchPostcodes(searchParams: SearchPostcodes): Promise<any> {
    const cacheKey = `postcodes_${JSON.stringify(searchParams)}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    const response = await customerApi.searchPostcodes(searchParams);
    // Cache postcode searches for longer (24 hours) as they change less frequently
    addressCache.set(cacheKey, response, 1480 * 60 * 1000);
    return response;
  },


  // Enhanced methods for sequential address building using cached data
  async getRegions(): Promise<any> {
    const cacheKey = 'regions_all';
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    // Get all postal code data from cache or fetch if not available
    const allPostalData = await this.prefetchPostalCodeData();

    // Extract unique regions and cache them
    const allRegions = allPostalData.data?.map((pc: any) => pc.region).filter(Boolean) || [];

    const uniqueRegions = [...new Set(allRegions)];

    // Sort regions for consistent ordering
    const sortedRegions = uniqueRegions.sort();

    const regionResponse = { data: sortedRegions };

    addressCache.set(cacheKey, regionResponse, 1480 * 60 * 1000); // 24 hours
    console.log('Regions extracted from cached data:', sortedRegions);
    return regionResponse;
  },

  async getDistrictsByRegion(region: string): Promise<any> {
    const cacheKey = `districts_${region}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    // Get all postal code data from cache
    const allPostalData = await this.prefetchPostalCodeData();

    // Filter by region and extract unique districts
    const filteredData = allPostalData.data?.filter((pc: any) => pc.region === region) || [];
    const allDistricts = filteredData.map((pc: any) => pc.district).filter(Boolean);
    const uniqueDistricts = [...new Set(allDistricts)];

    // Sort districts for consistent ordering
    const sortedDistricts = uniqueDistricts.sort();

    const districtResponse = { data: sortedDistricts };
    addressCache.set(cacheKey, districtResponse, 1480 * 60 * 1000); // 24 hours
    console.log(`Districts for ${region} extracted from cached data:`, sortedDistricts);
    console.log(`Total unique districts found for ${region}:`, sortedDistricts.length);
    return districtResponse;
  },

  async getLocationsByDistrict(region: string, district: string): Promise<any> {
    const cacheKey = `locations_${region}_${district}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    // Get all postal code data from cache
    const allPostalData = await this.prefetchPostalCodeData();

    // Filter by region and district, then extract unique locations
    const filteredData = allPostalData.data?.filter((pc: any) =>
      pc.region === region && pc.district === district
    ) || [];
    const allLocations = filteredData.map((pc: any) => pc.location).filter(Boolean);
    const uniqueLocations = [...new Set(allLocations)];

    // Sort locations for consistent ordering
    const sortedLocations = uniqueLocations.sort();

    const locationResponse = { data: sortedLocations };
    addressCache.set(cacheKey, locationResponse, 1480 * 60 * 1000); // 24 hours
    console.log(`Locations for ${region}/${district} extracted from cached data:`, sortedLocations);
    console.log(`Total unique locations found for ${region}/${district}:`, sortedLocations.length);
    return locationResponse;
  },

  async getPostcodesByLocation(region: string, district: string, location: string): Promise<any> {
    const cacheKey = `postcodes_${region}_${district}_${location}`;
    const cached = addressCache.get(cacheKey);
    if (cached) return cached;

    // Get all postal code data from cache
    const allPostalData = await this.prefetchPostalCodeData();

    // Filter by region, district, and location
    const filteredData = allPostalData.data?.filter((pc: any) =>
      pc.region === region && pc.district === district && pc.location === location
    ) || [];

    // Sort postcodes for consistent ordering
    const sortedPostcodes = filteredData.sort((a: any, b: any) => {
      return a.postal_code.localeCompare(b.postal_code);
    });

    const postcodeResponse = { data: sortedPostcodes };
    addressCache.set(cacheKey, postcodeResponse, 1480 * 60 * 1000); // 24 hours
    console.log(`Postcodes for ${region}/${district}/${location} extracted from cached data:`, sortedPostcodes.length, 'records');
    return postcodeResponse;
  },

  // Cache management methods
  clearCache(): void {
    addressCache.clear();
  },

  invalidateAddressCache(addressId?: string): void {
    if (addressId) {
      addressCache.invalidatePattern(`address_${addressId}`);
    }
    addressCache.invalidatePattern(`addresses_.*`);
  },
}


// Actual hook
export const useAddresses = (initialSearchParams?: SearchPostcodes) => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});

  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);
  const [searching, setSearching] = useState(false);
  const [prefetching, setPrefetching] = useState(false);

  // Track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Fetch address list when searchParams change
  useEffect(() => {
    const fetchAddresses = async () => {
      setLoading(true);
      setError(null);
      try {
        // Use getAllAddresses for general listing, searchPostcodes for specific searches
        const response = Object.keys(searchParams).length > 0
          ? await addressService.searchPostcodes(searchParams)
          : await addressService.getAllAddresses();

        if (isMountedRef.current) {
          setAddresses(response.data || []);
        }
      } catch (err: any) {
        console.error('Address fetch error:', err);
        if (isMountedRef.current) {
          setError(err.message || 'Failed to fetch addresses');
          toast.error('Failed to fetch addresses');
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false);
        }
      }
    };

    fetchAddresses();
  }, [searchParams]);

  // Postcode suggestions (live lookup, debounced)
  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {
    if (!isMountedRef.current) return;

    setSearching(true);
    try {
      const response = await addressService.searchPostcodes(params);
      if (isMountedRef.current) {
        setPostcodeSuggestions(response.data || []);
      }
    } catch (err) {
      console.error('Postcode search failed:', err);
      if (isMountedRef.current) {
        setPostcodeSuggestions([]);
      }
    } finally {
      if (isMountedRef.current) {
        setSearching(false);
      }
    }
  }, 500); // debounce for 500ms

  // Manual search trigger to update addresses based on params
  const searchAddresses = (params: SearchPostcodes) => {
    setSearchParams(params);
  };

  // Create new address with enhanced caching
  const createAddress = useCallback(async (data: CreateAddressDto) => {
    setLoading(true);
    setError(null);
    try {
      const response = await addressService.createAddress(data);
      const newAddress = response.data || response;

      if (isMountedRef.current) {
        setAddresses(prev => [newAddress, ...prev]);
        toast.success('Address created successfully');
      }
      return response;
    } catch (err: any) {
      console.error('Address create error:', err);
      if (isMountedRef.current) {
        setError(err.message || 'Failed to create address');
        toast.error('Failed to create address');
      }
      throw err;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  // Edit existing address with enhanced caching
  const editAddress = useCallback(async (data: EditAddressData) => {
    setLoading(true);
    setError(null);
    try {
      const response = await addressService.editAddress(data);
      const updatedAddress = response.data || response;

      if (isMountedRef.current) {
        setAddresses(prev =>
          prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))
        );
        toast.success('Address updated successfully');
      }
      return response;
    } catch (err: any) {
      console.error('Address edit error:', err);
      if (isMountedRef.current) {
        setError(err.message || 'Failed to update address');
        toast.error('Failed to update address');
      }
      throw err;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  // Get addresses by entity with caching
  const getAddressesByEntity = useCallback(async (entityType: string, entityId: string) => {
    try {
      const response = await addressService.getAddressesByEntity(entityType, entityId);
      return response;
    } catch (error: any) {
      console.error('Get addresses by entity error:', error);
      const message = error.response?.data?.message || error.message || 'Failed to fetch addresses';
      toast.error(message);
      throw error;
    }
  }, []);

  // Clear cache manually
  const clearAddressCache = useCallback(() => {
    addressService.clearCache();
    toast.success('Address cache cleared');
  }, []);

  // Prefetch postal code data
  const prefetchPostalCodeData = useCallback(async () => {
    if (prefetching) return; // Prevent multiple simultaneous prefetch calls

    setPrefetching(true);
    try {
      await addressService.prefetchPostalCodeData();
    } catch (error) {
      console.error('Failed to prefetch postal code data:', error);
    } finally {
      if (isMountedRef.current) {
        setPrefetching(false);
      }
    }
  }, [prefetching]);

  
  return {
    // State
    addresses,
    postcodeSuggestions,
    searching,
    prefetching,
    loading,
    error,
    searchParams,

    // Setters / Triggers
    setSearchParams,
    debouncedSearchPostcodes,
    searchAddresses,

    // CRUD operations
    createAddress,
    editAddress,
    getAddressesByEntity,

    // Cache management
    clearAddressCache,
    prefetchPostalCodeData,

    // Sequential address building helpers
    getRegions: addressService.getRegions,
    getDistrictsByRegion: addressService.getDistrictsByRegion,
    getLocationsByDistrict: addressService.getLocationsByDistrict,
    getPostcodesByLocation: addressService.getPostcodesByLocation,

    // Postcode lookup helpers
    getPostcodeDetails: addressService.getPostcodeDetails,
    populateAddressFromPostcode: addressService.populateAddressFromPostcode,

    // Raw service (if needed)
    addressService,
  };
};