'use client';

import { useState, useEffect } from 'react';
import { taskService } from '@/services/task-assignment';
import { useAuth } from '@/contexts/AuthContext';

interface UseTaskCountReturn {
  pendingCount: number;
  loading: boolean;
  error: string | null;
  refreshCount: () => Promise<void>;
}

export const useTaskCount = (): UseTaskCountReturn => {
  const { user } = useAuth();
  const [pendingCount, setPendingCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTaskCount = async () => {
    if (!user) {
      setPendingCount(0);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get tasks assigned to current user with pending status
      const response = await taskService.getMyTasks({
        status: 'pending',
        limit: 1, // We only need the count, not the actual tasks
        page: 1
      });

      // The total count should be in the response metadata
      setPendingCount(response.meta?.total || 0);
    } catch (err: any) {
      console.error('Error fetching task count:', err);
      setError(err.message || 'Failed to fetch task count');
      setPendingCount(0);
    } finally {
      setLoading(false);
    }
  };

  const refreshCount = async () => {
    await fetchTaskCount();
  };

  // Initial fetch
  useEffect(() => {
    fetchTaskCount();
  }, [user]);

  // Poll for updates every 60 seconds (reduced frequency to avoid too many API calls)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(fetchTaskCount, 60000);
    return () => clearInterval(interval);
  }, [user]);

  // Listen for task updates via custom events
  useEffect(() => {
    const handleTaskUpdate = () => {
      fetchTaskCount();
    };

    // Listen for custom events that indicate task updates
    window.addEventListener('taskUpdated', handleTaskUpdate);
    window.addEventListener('taskCompleted', handleTaskUpdate);
    window.addEventListener('taskAssigned', handleTaskUpdate);
    window.addEventListener('taskRefresh', handleTaskUpdate);

    return () => {
      window.removeEventListener('taskUpdated', handleTaskUpdate);
      window.removeEventListener('taskCompleted', handleTaskUpdate);
      window.removeEventListener('taskAssigned', handleTaskUpdate);
      window.removeEventListener('taskRefresh', handleTaskUpdate);
    };
  }, []);

  return {
    pendingCount,
    loading,
    error,
    refreshCount
  };
};
