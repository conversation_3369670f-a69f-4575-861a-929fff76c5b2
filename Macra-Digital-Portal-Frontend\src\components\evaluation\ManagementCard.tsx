import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { stakeholderService } from '@/services/stakeholderService';

interface ManagementCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const ManagementCard: React.FC<ManagementCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [stakeholders, setStakeholders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchStakeholders = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        const stakeholderResponse = await stakeholderService.getStakeholdersByApplication(application.application_id);
        const stakeholderData = stakeholderResponse?.data || stakeholderResponse || [];
        setStakeholders(stakeholderData);
      } catch (err) {
        console.warn('Could not load stakeholders:', err);
        setStakeholders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStakeholders();
  }, [application?.application_id]);

  // Return empty fragment if no application or no stakeholders
  if (!application || loading || !stakeholders || stakeholders.length === 0) {
    return <></>;
  }

  return (
    <>
      {stakeholders.map((member, index) => (
        <DataDisplayCard
          key={index}
          title={`${member.stakeholder_type || 'Management'} - ${member.full_name || 'Unnamed Member'}`}
          icon="ri-user-star-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          fields={[
            {
              label: 'Full Name',
              value: member.full_name,
              icon: 'ri-user-3-line'
            },
            {
              label: 'Position/Title',
              value: member.position,
              icon: 'ri-briefcase-line'
            },
            {
              label: 'Stakeholder Type',
              value: member.stakeholder_type,
              icon: 'ri-vip-crown-line'
            },
            {
              label: 'Email Address',
              value: member.email,
              type: 'email' as const,
              icon: 'ri-mail-line'
            },
            {
              label: 'Phone Number',
              value: member.phone,
              type: 'phone' as const,
              icon: 'ri-phone-line'
            },
            {
              label: 'Ownership Percentage',
              value: member.ownership_percentage ? `${member.ownership_percentage}%` : null,
              icon: 'ri-pie-chart-line'
            },
            {
              label: 'Date of Birth',
              value: member.date_of_birth,
              type: 'date' as const,
              icon: 'ri-calendar-line'
            },
            {
              label: 'Nationality',
              value: member.nationality,
              icon: 'ri-earth-line'
            },
            {
              label: 'Qualifications',
              value: member.qualifications,
              icon: 'ri-medal-line',
              fullWidth: true
            },
            {
              label: 'Experience',
              value: member.experience,
              icon: 'ri-time-line',
              fullWidth: true
            },
            ...(member.notes ? [{
              label: 'Additional Notes',
              value: member.notes,
              icon: 'ri-sticky-note-line',
              fullWidth: true
            }] : [])
          ]}
        />
      ))}
    </>
  );
};

export default ManagementCard;
