import React from 'react';

interface CalendarProps {
  mode?: 'single';
  selected?: Date;
  onSelect?: (date: Date | undefined) => void;
  disabled?: (date: Date) => boolean;
  initialFocus?: boolean;
}

const Calendar: React.FC<CalendarProps> = ({ 
  selected, 
  onSelect, 
  disabled,
  initialFocus 
}) => {
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();

  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();

  const days = [];
  
  // Add empty cells for days before the first day of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    days.push(<div key={`empty-${i}`} className="p-2"></div>);
  }

  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(currentYear, currentMonth, day);
    const isSelected = selected && 
      date.getDate() === selected.getDate() &&
      date.getMonth() === selected.getMonth() &&
      date.getFullYear() === selected.getFullYear();
    const isDisabled = disabled?.(date);

    days.push(
      <button
        key={day}
        type="button"
        className={`p-2 text-sm rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 ${
          isSelected 
            ? 'bg-red-600 text-white' 
            : 'text-gray-900 dark:text-gray-100'
        } ${
          isDisabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'cursor-pointer'
        }`}
        onClick={() => !isDisabled && onSelect?.(date)}
        disabled={isDisabled}
      >
        {day}
      </button>
    );
  }

  return (
    <div className="p-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
      <div className="grid grid-cols-7 gap-1 text-center">
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Su</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Mo</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Tu</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">We</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Th</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Fr</div>
        <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400">Sa</div>
        {days}
      </div>
    </div>
  );
};

export { Calendar };
