import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { scopeOfServiceService } from '@/services/scopeOfServiceService';

interface ServiceScopeCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const ServiceScopeCard: React.FC<ServiceScopeCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [scopeOfServices, setScopeOfServices] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchScopeOfServices = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        const scopeOfService = await scopeOfServiceService.getScopeOfServiceByApplication(application.application_id);
        setScopeOfServices(scopeOfService ? [scopeOfService] : []);
      } catch (err) {
        console.warn('Could not load scope of services:', err);
        setScopeOfServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchScopeOfServices();
  }, [application?.application_id]);

  // Return empty fragment if no application or no scope of services
  if (!application || loading || !scopeOfServices || scopeOfServices.length === 0) {
    return <></>;
  }

  return (
    <>
      {scopeOfServices.map((scopeData, index) => (
        <DataDisplayCard
          key={index}
          title={`Service Scope Information ${scopeOfServices.length > 1 ? `#${index + 1}` : ''}`}
          icon="ri-service-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          fields={[
            {
              label: 'Nature of Service',
              value: scopeData.nature_of_service,
              icon: 'ri-service-line'
            },
            {
              label: 'Premises',
              value: scopeData.premises,
              icon: 'ri-building-line'
            },
            {
              label: 'Transport Type',
              value: scopeData.transport_type,
              icon: 'ri-truck-line'
            },
            {
              label: 'Customer Assistance',
              value: scopeData.customer_assistance,
              icon: 'ri-customer-service-line'
            },
            {
              label: 'Created Date',
              value: scopeData.created_at ? new Date(scopeData.created_at).toLocaleDateString() : null,
              type: 'date' as const,
              icon: 'ri-calendar-line'
            },
            ...(scopeData.description ? [{
              label: 'Description',
              value: scopeData.description,
              icon: 'ri-information-line',
              fullWidth: true
            }] : [])
          ]}
        />
      ))}
    </>
  );
};

export default ServiceScopeCard;
