'use client';

import React, { useState, useEffect, useRef } from 'react';
import { BarChart3, TrendingUp, Shield, AlertTriangle, Clock, RefreshCw } from 'lucide-react';
import PublicLoader from '@/components/public/PublicLoader';
import ErrorState from '@/components/public/ErrorState';
import { StatsSkeleton, ChartSkeleton } from '@/components/public/SkeletonLoader';
import PageTransition, { StaggeredAnimation } from '@/components/public/PageTransition';
import { publicApi } from '@/lib/customer-api';
import { VerificationStats, VerificationStatsResponse, LicenseStatsChartData } from '@/types/public';

// Animated counter component
const AnimatedCounter: React.FC<{ 
  value: number; 
  duration?: number; 
  className?: string;
  prefix?: string;
  suffix?: string;
}> = ({ value, duration = 2000, className = '', prefix = '', suffix = '' }) => {
  const [displayValue, setDisplayValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setDisplayValue(Math.floor(value * easeOutQuart));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration, isVisible]);

  return (
    <span ref={counterRef} className={className}>
      {prefix}{displayValue.toLocaleString()}{suffix}
    </span>
  );
};

export default function StatisticsClient() {
  const [stats, setStats] = useState<VerificationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch statistics
  const fetchStats = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }
    setError(null);

    try {
      const response: VerificationStatsResponse = await publicApi.getVerificationStats();
      
      if (response.success) {
        setStats(response.data);
        setLastUpdated(new Date());
        setRetryCount(0);
      } else {
        setError(response.message || 'Failed to load statistics');
        setRetryCount(prev => prev + 1);
      }
    } catch (err: unknown) {
      console.error('Statistics error:', err);
      setError('Failed to load statistics. Please try again later.');
      setRetryCount(prev => prev + 1);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load statistics on component mount
  useEffect(() => {
    fetchStats();
  }, []);

  // Prepare chart data
  const getChartData = (stats: VerificationStats): LicenseStatsChartData[] => {
    return [
      {
        name: 'Active',
        value: stats.activeLicenses,
        color: 'bg-green-500'
      },
      {
        name: 'Expired',
        value: stats.expiredLicenses,
        color: 'bg-red-500'
      },
      {
        name: 'Suspended',
        value: stats.suspendedLicenses,
        color: 'bg-yellow-500'
      }
    ];
  };

  // Calculate percentages
  const getPercentage = (value: number, total: number): number => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  const handleRetry = () => {
    fetchStats();
  };

  const handleRefresh = () => {
    fetchStats(true);
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header Skeleton */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto animate-pulse" />
          <div className="h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse" />
          <div className="h-4 w-96 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse" />
        </div>

        {/* Last Updated Skeleton */}
        <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" />

        {/* Stats Cards Skeleton */}
        <StatsSkeleton count={4} />

        {/* Chart Skeleton */}
        <ChartSkeleton />

        {/* Info Section Skeleton */}
        <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorState
        type="server"
        title="Error Loading Statistics"
        message={error}
        onRetry={handleRetry}
        retryCount={retryCount}
        maxRetries={3}
        className="mt-12"
      />
    );
  }

  if (!stats) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="text-center py-12">
          <p className="text-gray-600 dark:text-gray-400">No statistics available</p>
        </div>
      </div>
    );
  }

  const chartData = getChartData(stats);
  const totalLicenses = stats.totalLicenses;

  return (
    <PageTransition>
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Page Header */}
        <StaggeredAnimation delay={100}>
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-primary/10 p-3 rounded-full animate-pulse">
                <BarChart3 className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              License Statistics
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Public statistics about licenses in the MACRA system
            </p>
          </div>
        </StaggeredAnimation>

        {/* Last Updated */}
        <StaggeredAnimation delay={200}>
          <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
              <Clock className="h-4 w-4" />
              <span className="text-sm">
                Last updated: {lastUpdated?.toLocaleString() || 'Unknown'}
              </span>
            </div>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 text-primary hover:text-red-700 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span className="text-sm">{refreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>
        </StaggeredAnimation>

        {/* Statistics Cards */}
        <StaggeredAnimation delay={300}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Licenses */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Licenses</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    <AnimatedCounter value={totalLicenses} />
                  </p>
                </div>
                <div className="bg-primary/10 p-3 rounded-full">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
              </div>
            </div>

            {/* Active Licenses */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Licenses</p>
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                    <AnimatedCounter value={stats.activeLicenses} />
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <AnimatedCounter 
                      value={getPercentage(stats.activeLicenses, totalLicenses)} 
                      suffix="% of total"
                      duration={2500}
                    />
                  </p>
                </div>
                <div className="bg-green-100 dark:bg-green-900/20 p-3 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            {/* Expired Licenses */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Expired Licenses</p>
                  <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                    <AnimatedCounter value={stats.expiredLicenses} />
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <AnimatedCounter 
                      value={getPercentage(stats.expiredLicenses, totalLicenses)} 
                      suffix="% of total"
                      duration={2500}
                    />
                  </p>
                </div>
                <div className="bg-red-100 dark:bg-red-900/20 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
              </div>
            </div>

            {/* Suspended Licenses */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Suspended Licenses</p>
                  <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                    <AnimatedCounter value={stats.suspendedLicenses} />
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    <AnimatedCounter 
                      value={getPercentage(stats.suspendedLicenses, totalLicenses)} 
                      suffix="% of total"
                      duration={2500}
                    />
                  </p>
                </div>
                <div className="bg-yellow-100 dark:bg-yellow-900/20 p-3 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>
            </div>
          </div>
        </StaggeredAnimation>

        {/* Visual Chart */}
        <StaggeredAnimation delay={400}>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">License Status Distribution</h3>

            {/* Bar Chart */}
            <div className="space-y-4">
              {chartData.map((item, index) => (
                <div key={item.name} className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.name}
                  </div>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-6 relative overflow-hidden">
                    <div
                      className={`${item.color} h-6 rounded-full transition-all duration-1000 ease-out`}
                      style={{
                        width: `${getPercentage(item.value, totalLicenses)}%`,
                        animationDelay: `${index * 200}ms`
                      }}
                    ></div>
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                      <AnimatedCounter
                        value={item.value}
                        duration={2000 + (index * 200)}
                      /> ({<AnimatedCounter
                        value={getPercentage(item.value, totalLicenses)}
                        duration={2000 + (index * 200)}
                        suffix="%"
                      />})
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </StaggeredAnimation>

        {/* Information Section */}
        <StaggeredAnimation delay={500}>
          <div className="bg-primary/5 dark:bg-primary/10 rounded-xl p-6 border border-primary/20">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
              About These Statistics
            </h3>
            <div className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <p>
                • These statistics represent the current state of licenses in the MACRA system
              </p>
              <p>
                • Data is updated in real-time and reflects the most current information available
              </p>
              <p>
                • Active licenses are currently valid and in good standing
              </p>
              <p>
                • Expired licenses have passed their expiration date and require renewal
              </p>
              <p>
                • Suspended licenses are temporarily inactive due to regulatory actions
              </p>
            </div>
          </div>
        </StaggeredAnimation>
      </div>
    </PageTransition>
  );
}
