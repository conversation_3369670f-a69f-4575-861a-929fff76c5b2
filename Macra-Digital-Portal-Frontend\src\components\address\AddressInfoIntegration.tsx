'use client';

import React, { useState } from 'react';
import SequentialAddressBuilder from './SequentialAddressBuilder';

interface SequentialAddressData {
  country: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  address_line_1: string;
  address_line_2: string;
}

interface AddressInfoIntegrationProps {
  onAddressSave: (address: SequentialAddressData) => Promise<void>;
  initialAddress?: Partial<SequentialAddressData>;
  disabled?: boolean;
}

/**
 * Integration component showing how to use SequentialAddressBuilder
 * in the address-info page context
 */
const AddressInfoIntegration: React.FC<AddressInfoIntegrationProps> = ({
  onAddressSave,
  initialAddress,
  disabled = false
}) => {
  const [currentAddress, setCurrentAddress] = useState<Partial<SequentialAddressData>>(initialAddress || {});
  const [isComplete, setIsComplete] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [useSequentialBuilder, setUseSequentialBuilder] = useState(false);

  const handleAddressComplete = (address: SequentialAddressData) => {
    setCurrentAddress(address);
    setIsComplete(true);
  };

  const handleAddressChange = (address: Partial<SequentialAddressData>) => {
    setCurrentAddress(address);
    setIsComplete(false);
  };

  const handleSaveAddress = async () => {
    if (!isComplete || !currentAddress.address_line_1) return;

    setIsSaving(true);
    try {
      await onAddressSave(currentAddress as SequentialAddressData);
    } catch (error) {
      console.error('Failed to save address:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const toggleBuilder = () => {
    setUseSequentialBuilder(!useSequentialBuilder);
    // Reset state when switching modes
    if (!useSequentialBuilder) {
      setCurrentAddress(initialAddress || {});
      setIsComplete(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Mode Toggle */}
      <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <div>
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Address Input Mode
          </h3>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Choose between guided step-by-step or traditional form input
          </p>
        </div>
        <button
          type="button"
          onClick={toggleBuilder}
          disabled={disabled || isSaving}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ${
            useSequentialBuilder ? 'bg-red-600' : 'bg-gray-200 dark:bg-gray-700'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              useSequentialBuilder ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Sequential Builder Mode */}
      {useSequentialBuilder ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              <i className="ri-route-line mr-2"></i>
              Guided Address Builder
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Follow the step-by-step process to build your address. For Malawi addresses, 
              we'll guide you through region, district, and location selection.
            </p>
          </div>

          <SequentialAddressBuilder
            onAddressComplete={handleAddressComplete}
            onAddressChange={handleAddressChange}
            initialData={currentAddress}
            disabled={disabled || isSaving}
          />

          {/* Save Button */}
          {isComplete && (
            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={handleSaveAddress}
                disabled={disabled || isSaving || !isComplete}
                className="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Saving Address...
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-2"></i>
                    Save Address
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      ) : (
        /* Traditional Form Mode */
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              <i className="ri-edit-line mr-2"></i>
              Traditional Form Input
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Enter your address information using the standard form fields.
            </p>
          </div>

          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <i className="ri-file-edit-line text-4xl mb-2"></i>
            <p>Traditional form would be rendered here</p>
            <p className="text-sm mt-2">
              This would contain the existing TextInput components for manual address entry
            </p>
          </div>
        </div>
      )}

      {/* Current Address Preview */}
      {Object.keys(currentAddress).length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3">
            <i className="ri-eye-line mr-2"></i>
            Current Address Data
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {Object.entries(currentAddress).map(([key, value]) => (
              value && (
                <div key={key} className="flex justify-between">
                  <span className="text-blue-600 dark:text-blue-400 capitalize">
                    {key.replace(/_/g, ' ')}:
                  </span>
                  <span className="text-blue-800 dark:text-blue-200 font-medium">
                    {value}
                  </span>
                </div>
              )
            ))}
          </div>
          {isComplete && (
            <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-800">
              <div className="flex items-center text-sm text-green-600 dark:text-green-400">
                <i className="ri-check-circle-line mr-2"></i>
                Address is complete and ready to save
              </div>
            </div>
          )}
        </div>
      )}

      {/* Integration Notes */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
        <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">
          <i className="ri-lightbulb-line mr-2"></i>
          Integration Notes
        </h4>
        <ul className="text-sm text-yellow-700 dark:text-yellow-400 space-y-1">
          <li>• The sequential builder can be toggled on/off based on user preference</li>
          <li>• Both modes can share the same save logic and validation</li>
          <li>• Initial data can be loaded from existing address records</li>
          <li>• The component handles both Malawi and international addresses</li>
          <li>• Progress is tracked and displayed in real-time</li>
        </ul>
      </div>
    </div>
  );
};

export default AddressInfoIntegration;
