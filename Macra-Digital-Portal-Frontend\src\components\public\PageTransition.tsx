'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import PublicLoader from './PublicLoader';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  showLoader?: boolean;
  loaderDelay?: number;
  transitionDuration?: number;
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  showLoader = true,
  loaderDelay = 100,
  transitionDuration = 300
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Start loading state
    setIsLoading(true);
    setIsVisible(false);

    // Simulate loading time for smooth transition
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
      
      // Small delay before showing content for smooth transition
      const visibilityTimer = setTimeout(() => {
        setIsVisible(true);
      }, 50);

      return () => clearTimeout(visibilityTimer);
    }, loaderDelay);

    return () => clearTimeout(loadingTimer);
  }, [pathname, loaderDelay]);

  const getPageType = () => {
    if (pathname.includes('/verify')) return 'verification';
    if (pathname.includes('/statistics')) return 'statistics';
    if (pathname.includes('/help')) return 'general';
    return 'page-load';
  };

  const getLoadingMessage = () => {
    if (pathname.includes('/verify')) return 'Loading verification portal...';
    if (pathname.includes('/statistics')) return 'Loading statistics dashboard...';
    if (pathname.includes('/help')) return 'Loading help center...';
    return 'Loading page...';
  };

  if (isLoading && showLoader) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <PublicLoader
          type={getPageType()}
          message={getLoadingMessage()}
          size="lg"
          showIcon={true}
          className="animate-fadeIn"
        />
      </div>
    );
  }

  return (
    <div 
      className={`transition-all duration-${transitionDuration} ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
      } ${className}`}
      style={{
        transitionDuration: `${transitionDuration}ms`
      }}
    >
      {children}
    </div>
  );
};

// Higher-order component for wrapping pages with transitions
export const withPageTransition = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    showLoader?: boolean;
    loaderDelay?: number;
    transitionDuration?: number;
  }
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    return (
      <PageTransition {...options}>
        <Component {...props} />
      </PageTransition>
    );
  };

  WrappedComponent.displayName = `withPageTransition(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Staggered animation for child elements
export const StaggeredAnimation: React.FC<{
  children: React.ReactNode;
  delay?: number;
  className?: string;
}> = ({ children, delay = 100, className = '' }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div 
      className={`transition-all duration-500 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      } ${className}`}
    >
      {children}
    </div>
  );
};

// Component for animating lists with staggered delays
export const StaggeredList: React.FC<{
  children: React.ReactNode[];
  staggerDelay?: number;
  className?: string;
}> = ({ children, staggerDelay = 100, className = '' }) => {
  return (
    <div className={className}>
      {React.Children.map(children, (child, index) => (
        <StaggeredAnimation delay={index * staggerDelay}>
          {child}
        </StaggeredAnimation>
      ))}
    </div>
  );
};

export default PageTransition;
