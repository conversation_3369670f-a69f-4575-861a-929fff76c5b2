'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';

interface TariffCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  tariffs: TariffItem[];
}

interface TariffItem {
  id: string;
  name: string;
  description: string;
  amount: string;
  currency: string;
  frequency: string;
  effectiveDate: string;
  category: string;
}

const TariffsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const tariffCategories: TariffCategory[] = [
    {
      id: 'licensing',
      name: 'Licensing Fees',
      description: 'Application and annual fees for various telecommunications licenses',
      icon: 'ri-award-line',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      tariffs: [
        {
          id: 'isp-license',
          name: 'Internet Service Provider License',
          description: 'Annual license fee for ISP operations',
          amount: '50,000',
          currency: 'MWK',
          frequency: 'Annual',
          effectiveDate: '2025-01-01',
          category: 'licensing'
        },
        {
          id: 'mobile-license',
          name: 'Mobile Network License',
          description: 'Annual license fee for mobile network operations',
          amount: '200,000',
          currency: 'MWK',
          frequency: 'Annual',
          effectiveDate: '2025-01-01',
          category: 'licensing'
        }
      ]
    },
    {
      id: 'spectrum',
      name: 'Spectrum Fees',
      description: 'Fees for spectrum allocation and usage rights',
      icon: 'ri-radio-line',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      tariffs: [
        {
          id: 'spectrum-2g',
          name: '2G Spectrum Allocation',
          description: 'Annual fee per MHz for 2G spectrum',
          amount: '5,000',
          currency: 'MWK',
          frequency: 'Annual per MHz',
          effectiveDate: '2025-01-01',
          category: 'spectrum'
        },
        {
          id: 'spectrum-4g',
          name: '4G/LTE Spectrum Allocation',
          description: 'Annual fee per MHz for 4G/LTE spectrum',
          amount: '15,000',
          currency: 'MWK',
          frequency: 'Annual per MHz',
          effectiveDate: '2025-01-01',
          category: 'spectrum'
        }
      ]
    },
    {
      id: 'compliance',
      name: 'Compliance & Standards',
      description: 'Fees for equipment certification and compliance testing',
      icon: 'ri-shield-check-line',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      tariffs: [
        {
          id: 'type-approval',
          name: 'Type Approval Certificate',
          description: 'Equipment type approval and certification',
          amount: '25,000',
          currency: 'MWK',
          frequency: 'One-time',
          effectiveDate: '2025-01-01',
          category: 'compliance'
        },
        {
          id: 'testing-fee',
          name: 'Equipment Testing Fee',
          description: 'Laboratory testing and evaluation',
          amount: '10,000',
          currency: 'MWK',
          frequency: 'Per device',
          effectiveDate: '2025-01-01',
          category: 'compliance'
        }
      ]
    },
    {
      id: 'postal',
      name: 'Postal Services',
      description: 'Fees for postal and courier service licenses',
      icon: 'ri-mail-line',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      tariffs: [
        {
          id: 'postal-license',
          name: 'Postal Service License',
          description: 'Annual license for postal operations',
          amount: '30,000',
          currency: 'MWK',
          frequency: 'Annual',
          effectiveDate: '2025-01-01',
          category: 'postal'
        },
        {
          id: 'courier-license',
          name: 'Courier Service License',
          description: 'Annual license for courier operations',
          amount: '20,000',
          currency: 'MWK',
          frequency: 'Annual',
          effectiveDate: '2025-01-01',
          category: 'postal'
        }
      ]
    }
  ];

  const allTariffs = tariffCategories.flatMap(category => category.tariffs);

  const filteredTariffs = allTariffs.filter(tariff => {
    const matchesCategory = selectedCategory === 'all' || tariff.category === selectedCategory;
    const matchesSearch = tariff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tariff.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-blue-600">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <Link href="/customer/resources" className="hover:text-blue-600">Resources</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Tariffs</span>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Tariffs & Fee Schedule
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Current pricing structure for MACRA services, licenses, and regulatory fees.
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                <i className="ri-download-line mr-2"></i>
                Download PDF
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400"></i>
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search tariffs..."
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Categories</option>
              {tariffCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {tariffCategories.map(category => (
            <div
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                selectedCategory === category.id
                  ? `${category.bgColor} border-current ${category.color}`
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg ${category.bgColor} flex items-center justify-center`}>
                  <i className={`${category.icon} text-lg ${category.color}`}></i>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {category.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {category.tariffs.length} items
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Tariffs Table */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {selectedCategory === 'all' ? 'All Tariffs' : tariffCategories.find(c => c.id === selectedCategory)?.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {filteredTariffs.length} tariff{filteredTariffs.length !== 1 ? 's' : ''} found
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Frequency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Effective Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredTariffs.map((tariff) => (
                  <tr key={tariff.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {tariff.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {tariff.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {tariff.currency} {tariff.amount}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {tariff.frequency}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(tariff.effectiveDate).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredTariffs.length === 0 && (
            <div className="text-center py-12">
              <i className="ri-file-list-line text-4xl text-gray-400 mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No tariffs found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search terms or category filter.
              </p>
            </div>
          )}
        </div>

        {/* Important Notice */}
        <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-information-line text-yellow-600 dark:text-yellow-400 text-xl"></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Important Notice
              </h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>
                  All fees are subject to change with prior notice. For the most current tariff information, 
                  please contact MACRA directly or download the official tariff schedule.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default TariffsPage;
