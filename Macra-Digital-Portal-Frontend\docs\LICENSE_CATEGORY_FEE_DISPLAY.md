# License Category Fee Display

This document explains the custom fee display logic for license categories, specifically the "Short Code Allocation" category.

## Overview

The system has been modified to display "Free" instead of the fee amount for the "Short Code Allocation" license category when the fee is 0.

## Implementation

### Custom Fee Formatting Function

A new utility function `formatLicenseCategoryFee` has been created in `src/utils/formatters.ts`:

```typescript
export const formatLicenseCategoryFee = (
  fee: string | number,
  categoryName: string,
  currency: string = 'MWK'
): string => {
  // Check if fee is 0 or empty
  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {
    // Show "Free" for Short Code Allocation with 0 fee, otherwise show "Contact MACRA"
    return categoryName === 'Short Code Allocation' ? 'Free' : 'Contact MACRA';
  }
  
  // Format as currency for non-zero fees
  return formatCurrency(fee, currency);
};
```

### Display Logic

The function implements the following logic:

1. **For "Short Code Allocation" category:**
   - If fee is 0, empty, or null → Display "Free"
   - If fee is greater than 0 → Display formatted currency amount

2. **For all other categories:**
   - If fee is 0, empty, or null → Display "Contact MACRA"
   - If fee is greater than 0 → Display formatted currency amount

### Updated Components

The following components have been updated to use the new fee formatting:

1. **License Category Selection Page** (`src/app/customer/applications/[licenseTypeId]/page.tsx`)
   - Customer-facing page where users select license categories
   - Shows "Free" for Short Code Allocation with 0 fee

2. **License Categories Management Tab** (`src/components/settings/LicenseCategoriesTab.tsx`)
   - Admin interface for managing license categories
   - Consistent display of "Free" for Short Code Allocation

## Usage Examples

### Before the Change
- Short Code Allocation with fee 0: "MWK 0.00"
- Other License with fee 0: "Contact MACRA"

### After the Change
- Short Code Allocation with fee 0: "Free"
- Other License with fee 0: "Contact MACRA"
- Short Code Allocation with fee 1000: "MWK 1,000.00"

## Testing

Unit tests have been created in `src/utils/__tests__/formatLicenseCategoryFee.test.ts` to verify:

- Correct "Free" display for Short Code Allocation with 0 fee
- Correct "Contact MACRA" display for other categories with 0 fee
- Proper currency formatting for non-zero fees
- Edge cases (null, undefined, empty string values)
- Case sensitivity handling

## Benefits

1. **User Experience**: Clear indication that Short Code Allocation is free
2. **Consistency**: Uniform display across all interfaces
3. **Maintainability**: Centralized logic in a reusable utility function
4. **Flexibility**: Easy to extend for other special categories in the future

## Future Enhancements

The system can be easily extended to handle other special categories by modifying the `formatLicenseCategoryFee` function. For example:

```typescript
// Example extension for multiple special categories
const specialCategories = {
  'Short Code Allocation': 'Free',
  'Emergency Services': 'No Charge',
  'Government License': 'Waived'
};

if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {
  return specialCategories[categoryName] || 'Contact MACRA';
}
```

## Configuration

No additional configuration is required. The system automatically detects the "Short Code Allocation" category by exact name match and applies the special display logic.

## Troubleshooting

### Common Issues

1. **"Free" not showing**: Verify the category name is exactly "Short Code Allocation" (case-sensitive)
2. **Still showing currency**: Check that the fee value is actually 0, '0', null, or empty
3. **Inconsistent display**: Ensure all components are using the `formatLicenseCategoryFee` function

### Debug Steps

1. Check the category name in the database
2. Verify the fee value and type
3. Test with the unit tests to ensure the function works correctly
4. Check browser console for any JavaScript errors
