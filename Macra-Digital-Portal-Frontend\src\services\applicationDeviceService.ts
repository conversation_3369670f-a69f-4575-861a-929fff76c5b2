import { apiClient, processApiResponse } from '@/lib';
import { PaginatedResponse } from '@/types';

export interface ApplicationDevice {
  device_id: string;
  application_id: string;

  // Manufacturer Information (embedded)
  manufacturer_name: string;
  manufacturer_address?: string;
  manufacturer_country: string;

  // Equipment Information (embedded)
  brand_trade_name?: string;
  product_type_name?: string;

  equipment_category_id?: string;
  equipment_model?: string;
  imei: string;
  device_type: string;
  model_name: string;
  device_serial_number: string;
  approval_status: string;
  device_approval_number?: string;
  device_approval_date?: string;
  approval_notes?: string;
  created_at: string;
  updated_at: string;

  equipment_category?: {
    category_id: string;
    name: string;
    description: string;
  };
}

export interface CreateApplicationDeviceDto {
  application_id: string;

  // Manufacturer Information (embedded)
  manufacturer_name: string;
  manufacturer_address?: string;
  manufacturer_country: string;

  // Equipment Information (embedded)
  brand_trade_name?: string;
  product_type_name?: string;

  equipment_category_id?: string;
  imei?: string;
  approval_status?: string;
  device_approval_number?: string;
  device_approval_date?: string;
  approval_notes?: string;
}

export interface UpdateApplicationDeviceDto {
  // Manufacturer Information (embedded)
  manufacturer_name?: string;
  manufacturer_address?: string;
  manufacturer_country?: string;

  // Equipment Information (embedded)
  brand_trade_name?: string;
  product_type_name?: string;

  equipment_category_id?: string;
  imei?: string;
  device_type?: string;
  model_name?: string;
  device_serial_number?: string;
  approval_status?: string;
  device_approval_number?: string;
  device_approval_date?: string;
  approval_notes?: string;
}

class ApplicationDeviceService {
  private baseUrl = '/devices';

  /**
   * Clean device payload by converting empty strings to undefined for optional fields
   */
  private cleanDevicePayload(payload: any): any {
    const cleaned = { ...payload };

    // Convert empty strings to undefined for optional fields
    const optionalFields = [
      'manufacturer_address',
      'brand_trade_name',
      'product_type_name',
      'equipment_category',
      'approval_notes',
      'device_approval_number',
      'device_approval_date'
    ];

    optionalFields.forEach(field => {
      if (cleaned[field] === '') {
        cleaned[field] = undefined;
      }
    });

    return cleaned;
  }

  /**
   * Get devices for a specific application
   */
  async getDevicesByApplication(applicationId: string): Promise<PaginatedResponse<any>> {
    try {
      // Use the new backend endpoint that queries by application_id directly
      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error fetching application devices:', error);
      throw error;
    }
  }

  /**
   * Get a specific device by ID
   */
  async getDevice(deviceId: string): Promise<ApplicationDevice> {
    try {
      console.log('🔍 Fetching device:', deviceId);
      
      const response = await apiClient.get(`${this.baseUrl}/${deviceId}`);
      const device = processApiResponse(response);
      
      console.log('✅ Found device:', device);
      return device;
    } catch (error: any) {
      console.error('❌ Error fetching device:', error);
      throw error;
    }
  }

  /**
   * Create a new device for an application
   */
  async createDevice(deviceData: CreateApplicationDeviceDto): Promise<ApplicationDevice> {
    try {
      console.log('🔍 Creating device for application:', deviceData.application_id);
      console.log('📝 Device data:', deviceData);
      
      const cleanedData = this.cleanDevicePayload({
        ...deviceData,
        approval_status: deviceData.approval_status || 'pending'
      });

      const response = await apiClient.post(this.baseUrl, cleanedData);
      
      const createdDevice = processApiResponse(response);
      console.log('✅ Device created:', createdDevice);
      
      return createdDevice;
    } catch (error: any) {
      console.error('❌ Error creating device:', error);
      
      // Handle specific error cases
      if (error.response?.status === 409) {
        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';
        throw new Error(message);
      }
      
      if (error.response?.status === 400) {
        const message = error.response?.data?.message || 'Invalid device data provided';
        throw new Error(message);
      }
      
      throw error;
    }
  }

  /**
   * Update an existing device
   */
  async updateDevice(deviceId: string, deviceData: UpdateApplicationDeviceDto): Promise<ApplicationDevice> {
    try {
      console.log('🔍 Updating device:', deviceId);
      console.log('📝 Update data:', deviceData);
      
      const cleanedData = this.cleanDevicePayload(deviceData);
      const response = await apiClient.put(`${this.baseUrl}/${deviceId}`, cleanedData);
      const updatedDevice = processApiResponse(response);
      
      console.log('✅ Device updated:', updatedDevice);
      return updatedDevice;
    } catch (error: any) {
      console.error('❌ Error updating device:', error);
      
      // Handle specific error cases
      if (error.response?.status === 409) {
        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';
        throw new Error(message);
      }
      
      if (error.response?.status === 404) {
        throw new Error('Device not found');
      }
      
      throw error;
    }
  }

  /**
   * Delete a device
   */
  async deleteDevice(deviceId: string): Promise<void> {
    try {
      console.log('🔍 Deleting device:', deviceId);
      
      await apiClient.delete(`${this.baseUrl}/${deviceId}`);
      console.log('✅ Device deleted successfully');
    } catch (error: any) {
      console.error('❌ Error deleting device:', error);
      
      if (error.response?.status === 404) {
        throw new Error('Device not found');
      }
      
      throw error;
    }
  }

  /**
   * Get device by IMEI
   */
  async getDeviceByImei(imei: string): Promise<ApplicationDevice | null> {
    try {
      console.log('🔍 Fetching device by IMEI:', imei);
      
      const response = await apiClient.get(`${this.baseUrl}/imei/${imei}`);
      const device = processApiResponse(response);
      
      console.log('✅ Found device by IMEI:', device);
      return device;
    } catch (error: any) {
      console.error('❌ Error fetching device by IMEI:', error);
      
      // Return null if device not found
      if (error.response?.status === 404) {
        return null;
      }
      
      throw error;
    }
  }

  /**
   * Validate IMEI and get device information if exists
   */
  async validateImei(imei: string): Promise<{
    isValid: boolean;
    exists: boolean;
    device?: ApplicationDevice;
    message: string;
  }> {
    try {
      console.log('🔍 Validating IMEI:', imei);
      
      // Clean IMEI (remove any non-digit characters)
      const cleanImei = imei.replace(/\D/g, '');
      
      if (cleanImei.length !== 15) {
        return {
          isValid: false,
          exists: false,
          message: 'IMEI must be exactly 15 digits'
        };
      }
      
      // Try to find device by IMEI
      const device = await this.getDeviceByImei(cleanImei);
      
      if (device) {
        return {
          isValid: true,
          exists: true,
          device,
          message: 'IMEI found in database'
        };
      } else {
        return {
          isValid: true,
          exists: false,
          message: 'IMEI is valid but not found in database'
        };
      }
    } catch (error: any) {
      console.error('❌ Error validating IMEI:', error);
      
      return {
        isValid: false,
        exists: false,
        message: 'Error validating IMEI'
      };
    }
  }
}

export const applicationDeviceService = new ApplicationDeviceService();
