import React, { useState } from 'react';
import { TaskAssignmentModal } from '@/components/shared/TaskAssignmentModal';
import { useTaskAssignment } from '@/hooks/useTaskAssignment';
import { useTaskNavigation } from '@/hooks/useTaskNavigation';

interface Task {
  task_id: string;
  task_number: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  assigned_to?: string;
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  due_date?: string;
  created_at: string;
  entity_type?: string;
  entity_id?: string;
}

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  role?: {
    name: string;
  };
}

interface TasksTableProps {
  tasks: Task[];
  users: User[];
  onRefresh: () => void;
  loading?: boolean;
}

export const TasksTable: React.FC<TasksTableProps> = ({
  tasks,
  users,
  onRefresh,
  loading = false,
}) => {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { assignTask, isAssigning } = useTaskAssignment({
    onSuccess: () => {
      onRefresh();
      setIsAssignModalOpen(false);
      setSelectedTask(null);
    },
  });

  const { openTaskViewInNewTab, isLoading: isNavigating } = useTaskNavigation();

  const handleAssignClick = (task: Task) => {
    setSelectedTask(task);
    setIsAssignModalOpen(true);
  };

  const handleAssignment = async (assignmentData: any) => {
    if (!selectedTask) return;
    await assignTask(selectedTask.task_id, assignmentData);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { className: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Pending' },
      in_progress: { className: 'bg-blue-100 text-blue-800 border-blue-200', label: 'In Progress' },
      completed: { className: 'bg-green-100 text-green-800 border-green-200', label: 'Completed' },
      cancelled: { className: 'bg-red-100 text-red-800 border-red-200', label: 'Cancelled' },
      on_hold: { className: 'bg-gray-100 text-gray-800 border-gray-200', label: 'On Hold' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
        {config.label}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { className: 'bg-green-100 text-green-800 border-green-200', label: 'Low' },
      medium: { className: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Medium' },
      high: { className: 'bg-orange-100 text-orange-800 border-orange-200', label: 'High' },
      urgent: { className: 'bg-red-100 text-red-800 border-red-200', label: 'Urgent' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
        {config.label}
      </span>
    );
  };

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  const canAssignOrReassign = (task: Task) => {
    return task.status !== 'completed' && task.status !== 'cancelled';
  };

  return (
    <>
      <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Task #</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Priority</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Assigned To</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Due Date</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Created</th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {tasks.map((task) => (
                <tr key={task.task_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    {task.task_number}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div>
                      <div className="font-medium">{task.title}</div>
                      {task.description && (
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {task.description}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    {getStatusBadge(task.status)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    {getPriorityBadge(task.priority)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    {task.assignee ? (
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400">👤</span>
                        <div>
                          <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                            {task.assignee.first_name} {task.assignee.last_name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {task.assignee.email}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">Unassigned</span>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    {task.due_date ? (
                      <div className={`flex items-center gap-1 text-sm ${
                        isOverdue(task.due_date) ? 'text-red-600' : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        📅 {new Date(task.due_date).toLocaleDateString()}
                        {isOverdue(task.due_date) && (
                          <span className="ml-1 text-xs bg-red-100 text-red-800 px-1.5 py-0.5 rounded-full">
                            Overdue
                          </span>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">No due date</span>
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(task.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <button
                        className="border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 px-3 py-1 rounded-md text-sm font-medium transition-colors"
                        onClick={() => openTaskViewInNewTab(task.task_id)}
                        disabled={isNavigating}
                      >
                        👁️ {isNavigating ? 'Loading...' : 'View'}
                      </button>

                      <button
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-md transition-colors"
                        onClick={() => openTaskViewInNewTab(task.task_id)}
                        disabled={isNavigating}
                        title="Open in new tab"
                      >
                        🔗
                      </button>

                      {canAssignOrReassign(task) && (
                        <button
                          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                            task.assigned_to
                              ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700'
                              : 'bg-red-600 text-white hover:bg-red-700'
                          }`}
                          onClick={() => handleAssignClick(task)}
                          disabled={isAssigning}
                        >
                          👥 {task.assigned_to ? 'Reassign' : 'Assign'}
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={() => {
          setIsAssignModalOpen(false);
          setSelectedTask(null);
        }}
        onAssign={handleAssignment}
        task={selectedTask || undefined}
        users={users}
        loading={isAssigning}
      />
    </>
  );
};
