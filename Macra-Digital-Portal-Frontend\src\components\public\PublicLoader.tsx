'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Shield, CheckCircle, Clock, Wifi } from 'lucide-react';

interface PublicLoaderProps {
  message?: string;
  submessage?: string;
  showProgress?: boolean;
  progress?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  dynamicMessages?: string[];
  messageInterval?: number;
  type?: 'verification' | 'statistics' | 'general' | 'page-load';
  showIcon?: boolean;
}

const PublicLoader: React.FC<PublicLoaderProps> = ({
  message = 'Loading...',
  submessage,
  showProgress = false,
  progress = 0,
  size = 'md',
  className = '',
  dynamicMessages = [],
  messageInterval = 2500,
  type = 'general',
  showIcon = true
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [displayMessage, setDisplayMessage] = useState(message);
  const [dots, setDots] = useState('');

  // Handle dynamic message rotation
  useEffect(() => {
    if (dynamicMessages.length > 0) {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);
      }, messageInterval);

      return () => clearInterval(interval);
    }
  }, [dynamicMessages, messageInterval]);

  // Update display message when dynamic messages change
  useEffect(() => {
    if (dynamicMessages.length > 0) {
      setDisplayMessage(dynamicMessages[currentMessageIndex]);
    } else {
      setDisplayMessage(message);
    }
  }, [currentMessageIndex, dynamicMessages, message]);

  // Animated dots for loading text
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'w-12 h-12',
          logo: 'h-6 w-6',
          text: 'text-sm',
          icon: 'h-4 w-4'
        };
      case 'lg':
        return {
          container: 'w-28 h-28',
          logo: 'h-14 w-14',
          text: 'text-xl',
          icon: 'h-8 w-8'
        };
      case 'md':
      default:
        return {
          container: 'w-20 h-20',
          logo: 'h-10 w-10',
          text: 'text-base',
          icon: 'h-6 w-6'
        };
    }
  };

  const getTypeIcon = () => {
    switch (type) {
      case 'verification':
        return <Shield className={`${sizeClasses.icon} text-primary`} />;
      case 'statistics':
        return <CheckCircle className={`${sizeClasses.icon} text-green-600`} />;
      case 'page-load':
        return <Wifi className={`${sizeClasses.icon} text-blue-600`} />;
      default:
        return <Clock className={`${sizeClasses.icon} text-gray-600`} />;
    }
  };

  const getDefaultMessages = () => {
    switch (type) {
      case 'verification':
        return [
          'Verifying license authenticity...',
          'Checking MACRA database...',
          'Validating license details...',
          'Processing verification...'
        ];
      case 'statistics':
        return [
          'Loading license statistics...',
          'Fetching latest data...',
          'Calculating metrics...',
          'Preparing charts...'
        ];
      case 'page-load':
        return [
          'Loading page content...',
          'Preparing interface...',
          'Almost ready...'
        ];
      default:
        return [
          'Loading...',
          'Please wait...',
          'Processing...'
        ];
    }
  };

  const sizeClasses = getSizeClasses();
  const messages = dynamicMessages.length > 0 ? dynamicMessages : getDefaultMessages();

  // Use dynamic messages if none provided
  useEffect(() => {
    if (dynamicMessages.length === 0) {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
      }, messageInterval);

      return () => clearInterval(interval);
    }
  }, [dynamicMessages.length, messages.length, messageInterval]);

  useEffect(() => {
    setDisplayMessage(messages[currentMessageIndex]);
  }, [currentMessageIndex, messages]);

  return (
    <div className={`text-center ${className}`}>
      {/* Loading Spinner with Logo */}
      <div className={`relative ${sizeClasses.container} mx-auto mb-4`}>
        {/* Animated Spinner */}
        <svg
          className="absolute inset-0 animate-spin"
          viewBox="0 0 50 50"
          fill="none"
        >
          <defs>
            <linearGradient id={`fadeGradient-${type}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#dc2626" stopOpacity="0" />
              <stop offset="20%" stopColor="#dc2626" stopOpacity="0.8" />
              <stop offset="80%" stopColor="#dc2626" stopOpacity="1" />
              <stop offset="100%" stopColor="#dc2626" stopOpacity="0" />
            </linearGradient>
          </defs>

          <circle
            cx="25"
            cy="25"
            r="20"
            stroke="rgba(220, 38, 38, 0.1)"
            strokeWidth="2"
          />
          <circle
            cx="25"
            cy="25"
            r="20"
            stroke={`url(#fadeGradient-${type})`}
            strokeWidth="2"
            strokeDasharray="70"
            strokeDashoffset="10"
            fill="none"
            className="animate-pulse"
          />
        </svg>

        {/* MACRA Logo */}
        <Image
          src="/images/macra-logo.png"
          alt="MACRA Logo"
          width={40}
          height={40}
          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-fadeLoop`}
          priority
        />
      </div>

      {/* Type Icon */}
      {showIcon && (
        <div className="flex justify-center mb-3">
          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-full">
            {getTypeIcon()}
          </div>
        </div>
      )}

      {/* Progress Bar */}
      {showProgress && (
        <div className="mb-4 w-full max-w-xs mx-auto">
          <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
            <div
              className="bg-gradient-to-r from-primary to-red-700 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {Math.round(progress)}% complete
          </p>
        </div>
      )}

      {/* Loading Message */}
      <div className="space-y-2">
        <p className={`text-gray-700 dark:text-gray-300 font-medium ${sizeClasses.text} transition-all duration-300`}>
          {displayMessage}{dots}
        </p>
        {submessage && (
          <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
            {submessage}
          </p>
        )}
      </div>

      {/* Loading Animation Indicator */}
      <div className="flex justify-center mt-4 space-x-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-2 h-2 bg-primary rounded-full animate-pulse"
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1s'
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default PublicLoader;
