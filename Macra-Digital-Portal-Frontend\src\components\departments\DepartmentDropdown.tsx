import { useState, useRef, useEffect } from 'react';
import { Department } from '@/types/department';

interface DepartmentsDropdownProps {
  departments: Department[];
  selectedDepartmentId: string | null;
  onSelect: (id: string) => void;
}

function DepartmentsDropdown({ departments, selectedDepartmentId, onSelect }: DepartmentsDropdownProps) {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selected = departments.find((d) => d.department_id === selectedDepartmentId);

  return (
    <div className="w-full" ref={dropdownRef}>
      <label className="block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm">
        Department
      </label>

      <div className="relative">
        <button
          type="button"
          onClick={() => setOpen(!open)}
          className={`w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary
            ${open ? 'border-red-500 ring-2 ring-red-500' : 'border-gray-300 dark:border-gray-600'}
          `}
          aria-haspopup="listbox"
          aria-expanded={open}
        >
          <span className="block truncate">
            {selected ? selected.name : 'Select department...'}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {open && (
          <ul
            className="absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto"
            role="listbox"
          >
            {departments.length > 0 ? (
              departments.map((dept) => (
                <li
                  key={dept.department_id}
                  onClick={() => {
                    onSelect(dept.department_id);
                    setOpen(false);
                  }}
                  className={`cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ${
                    selectedDepartmentId === dept.department_id
                      ? 'font-semibold text-red-600 dark:text-red-300'
                      : 'text-gray-900 dark:text-gray-100'
                  }`}
                  role="option"
                  aria-selected={selectedDepartmentId === dept.department_id}
                >
                  {dept.name}
                  {selectedDepartmentId === dept.department_id && (
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                  )}
                </li>
              ))
            ) : (
              <li className="text-gray-500 dark:text-gray-400 px-4 py-2">
                No departments found
              </li>
            )}
          </ul>
        )}
      </div>
    </div>
  );
}

export default DepartmentsDropdown;
