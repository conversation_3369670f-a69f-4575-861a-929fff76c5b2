{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/SettingsTabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode } from 'react';\r\n\r\ninterface TabItem {\r\n  id: string;\r\n  label: string;\r\n  content: ReactNode;\r\n}\r\n\r\ninterface SettingsTabsProps {\r\n  tabs: TabItem[];\r\n  activeTab: string;\r\n  onTabChange: (tabId: string) => void;\r\n}\r\n\r\nconst SettingsTabs = ({ tabs, activeTab, onTabChange }: SettingsTabsProps) => {\r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Tab Navigation */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n        <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\r\n          {tabs.map((tab) => (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => onTabChange(tab.id)}\r\n              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${\r\n                activeTab === tab.id\r\n                  ? 'border-red-500 text-red-600 dark:text-red-400'\r\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'\r\n              }`}\r\n              aria-current={activeTab === tab.id ? 'page' : undefined}\r\n            >\r\n              {tab.label}\r\n            </button>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"mt-6\">\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.id}\r\n            className={`tab-content ${activeTab === tab.id ? '' : 'hidden'}`}\r\n          >\r\n            {tab.content}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsTabs;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAgBA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAqB;IACvE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAA6B,cAAW;8BACpD,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,YAAY,IAAI,EAAE;4BACjC,WAAW,CAAC,0FAA0F,EACpG,cAAc,IAAI,EAAE,GAChB,kDACA,qJACJ;4BACF,gBAAc,cAAc,IAAI,EAAE,GAAG,SAAS;sCAE7C,IAAI,KAAK;2BATL,IAAI,EAAE;;;;;;;;;;;;;;;0BAgBnB,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wBAEC,WAAW,CAAC,YAAY,EAAE,cAAc,IAAI,EAAE,GAAG,KAAK,UAAU;kCAE/D,IAAI,OAAO;uBAHP,IAAI,EAAE;;;;;;;;;;;;;;;;AASvB;uCAEe", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,8OAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,8OAAC;wBAAI,WAAU;;4BAA2C;0CAChD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,8OAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,8OAAC;gCAAK,WAAU;0CAAgK;;;;;yFAIhL,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginateQuery } from '../../types';\r\nimport Pagination from './Pagination';\r\nimport '../../styles/DataTable.css';\r\n\r\n// Generic paginated response interface to handle different response types\r\ninterface GenericPaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select?: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: unknown, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: GenericPaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n  emptyStateIcon?: string;\r\n  emptyStateMessage?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, unknown>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n  emptyStateIcon = \"ri-inbox-line\",\r\n  emptyStateMessage = \"No data found\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput, query.search, handleSearch]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto data-table-container\">\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex flex-col items-center justify-center py-8\">\r\n                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>\r\n                    <p>{emptyStateMessage}</p>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (\r\n        <Pagination\r\n          meta={{\r\n            ...data.meta,\r\n            totalItems: data.meta.totalItems,\r\n            currentPage: data.meta.currentPage,\r\n            totalPages: data.meta.totalPages,\r\n          }}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;;AAkDe,SAAS,UAA6C,EACnE,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACd,iBAAiB,eAAe,EAChC,oBAAoB,eAAe,EACjB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI;YACF,MAAM,WAAW;gBAAE,GAAG,KAAK;gBAAE;gBAAQ,MAAM;YAAE;YAC7C,SAAS;YACT,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;gBAChC,aAAa;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa,MAAM,MAAM;QAAE;KAAa;IAE5C,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,8OAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,8OAAC;4BAAM,WAAU;sCACd,wBACC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,GAAG,eAAe,cAAc,CAAC;;;;;;0DAC/C,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;uCAKV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,QAAQ,KAAK,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,2BACnH,8OAAC,0IAAA,CAAA,UAAU;gBACT,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU;oBAChC,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,YAAY,KAAK,IAAI,CAAC,UAAU;gBAClC;gBACA,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'MWK',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n\r\n  if(currency == '$') currency = 'USD';\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED AMOUNT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format amount with currency (alternative to formatCurrency for consistency)\r\n * @param amount - The amount to format\r\n * @param currency - Currency code (default: 'MWK')\r\n * @param locale - Locale for formatting (default: 'en-US')\r\n */\r\nexport const formatAmount = (\r\n  amount: number | string,\r\n  currency: string = 'MWK',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return `${currency} 0.00`;\r\n\r\n  return `${currency} ${numAmount.toLocaleString(locale, {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  })}`;\r\n};\r\n\r\n/**\r\n * Format amount without currency symbol\r\n * @param amount - The amount to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatNumber = (amount: number | string, decimals: number = 2): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return '0.00';\r\n\r\n  return numAmount.toLocaleString('en-US', {\r\n    minimumFractionDigits: decimals,\r\n    maximumFractionDigits: decimals\r\n  });\r\n};\r\n\r\n/**\r\n * Format license category fee with special handling for \"Short Code Allocation\"\r\n * @param fee - The fee amount (string or number)\r\n * @param categoryName - The name of the license category\r\n * @param currency - Currency code (default: 'MWK')\r\n */\r\nexport const formatLicenseCategoryFee = (\r\n  fee: string | number,\r\n  categoryName: string,\r\n): string => {\r\n  // Check if fee is 0 or empty\r\n  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {\r\n    // Show \"Free\" for Short Code Allocation with 0 fee, otherwise show \"Contact MACRA\"\r\n    return categoryName + \" is free\";\r\n  }\r\n\r\n  // Format as currency for non-zero fees\r\n  return formatCurrency(fee);\r\n};\r\n\r\n/**\r\n * Format percentage\r\n * @param value - The value to format as percentage\r\n * @param decimals - Number of decimal places (default: 1)\r\n */\r\nexport const formatPercentage = (value: number | string, decimals: number = 1): string => {\r\n  const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n  if (isNaN(numValue)) return '0%';\r\n\r\n  return `${numValue.toFixed(decimals)}%`;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED DATE & TIME FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n *\r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string | Date,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  }\r\n): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};\r\n\r\n/**\r\n * Format date in long format (e.g., \"January 15, 2024\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateLong = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric'\r\n  });\r\n};\r\n\r\n/**\r\n * Format time (e.g., \"2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid time';\r\n\r\n  return date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n/**\r\n * Format datetime (e.g., \"Jan 15, 2024 at 2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid datetime';\r\n\r\n  return `${formatDate(date)} at ${formatTime(date)}`;\r\n};\r\n\r\n/**\r\n * Format relative time (e.g., \"2 hours ago\", \"in 3 days\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatRelativeTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) return 'Just now';\r\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\r\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\r\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\r\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\r\n\r\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\r\n};\r\n\r\n/**\r\n * Format date for input fields (YYYY-MM-DD)\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateForInput = (dateString: string | Date): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return '';\r\n\r\n  return date.toISOString().split('T')[0];\r\n};\r\n\r\n// ============================================================================\r\n// STRING CASE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Convert string to camelCase\r\n * @param str - String to convert\r\n */\r\nexport const toCamelCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => {\r\n      return index === 0 ? word.toLowerCase() : word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to PascalCase\r\n * @param str - String to convert\r\n */\r\nexport const toPascalCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word) => {\r\n      return word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to kebab-case\r\n * @param str - String to convert\r\n */\r\nexport const toKebabCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n    .replace(/[\\s_]+/g, '-')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to snake_case\r\n * @param str - String to convert\r\n */\r\nexport const toSnakeCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1_$2')\r\n    .replace(/[\\s-]+/g, '_')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to Title Case\r\n * @param str - String to convert\r\n */\r\nexport const toTitleCase = (str: string): string => {\r\n  return str.replace(/\\w\\S*/g, (txt) => {\r\n    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();\r\n  });\r\n};\r\n\r\n/**\r\n * Convert string to Sentence case\r\n * @param str - String to convert\r\n */\r\nexport const toSentenceCase = (str: string): string => {\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\n// ============================================================================\r\n// TEXT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n * @param text - Text to truncate\r\n * @param maxLength - Maximum length before truncation\r\n * @param suffix - Suffix to add (default: '...')\r\n */\r\nexport const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {\r\n  if (!text || text.length <= maxLength) return text || '';\r\n  return text.substring(0, maxLength - suffix.length) + suffix;\r\n};\r\n\r\n/**\r\n * Capitalize first letter of each word\r\n * @param str - String to capitalize\r\n */\r\nexport const capitalizeWords = (str: string): string => {\r\n  return str.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Remove extra whitespace and normalize spacing\r\n * @param str - String to normalize\r\n */\r\nexport const normalizeWhitespace = (str: string): string => {\r\n  return str.replace(/\\s+/g, ' ').trim();\r\n};\r\n\r\n/**\r\n * Extract initials from a name\r\n * @param name - Full name\r\n * @param maxInitials - Maximum number of initials (default: 2)\r\n */\r\nexport const getInitials = (name: string, maxInitials: number = 2): string => {\r\n  if (!name) return '';\r\n\r\n  return name\r\n    .split(' ')\r\n    .filter(word => word.length > 0)\r\n    .slice(0, maxInitials)\r\n    .map(word => word.charAt(0).toUpperCase())\r\n    .join('');\r\n};\r\n\r\n/**\r\n * Convert text to slug format (URL-friendly)\r\n * @param text - Text to convert\r\n */\r\nexport const toSlug = (text: string): string => {\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\r\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\r\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\r\n};\r\n\r\n/**\r\n * Highlight search terms in text\r\n * @param text - Text to highlight\r\n * @param searchTerm - Term to highlight\r\n * @param className - CSS class for highlighting (default: 'highlight')\r\n */\r\nexport const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, `<span class=\"${className}\">$1</span>`);\r\n};\r\n\r\n// ============================================================================\r\n// PHONE & EMAIL FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format phone number\r\n * @param phone - Phone number to format\r\n * @param format - Format type ('international' | 'national' | 'minimal')\r\n */\r\nexport const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {\r\n  if (!phone) return '';\r\n\r\n  // Remove all non-digit characters\r\n  const digits = phone.replace(/\\D/g, '');\r\n\r\n  if (digits.length < 10) return phone; // Return original if too short\r\n\r\n  switch (format) {\r\n    case 'international':\r\n      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;\r\n    case 'national':\r\n      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;\r\n    case 'minimal':\r\n      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;\r\n    default:\r\n      return phone;\r\n  }\r\n};\r\n\r\n/**\r\n * Mask email for privacy (e.g., \"j***@example.com\")\r\n * @param email - Email to mask\r\n */\r\nexport const maskEmail = (email: string): string => {\r\n  if (!email || !email.includes('@')) return email;\r\n\r\n  const [username, domain] = email.split('@');\r\n  if (username.length <= 2) return email;\r\n\r\n  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\r\n  return `${maskedUsername}@${domain}`;\r\n};\r\n\r\n/**\r\n * Validate email format\r\n * @param email - Email to validate\r\n */\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\n// ============================================================================\r\n// ID & REFERENCE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format application number with prefix\r\n * @param number - Application number\r\n * @param prefix - Prefix to add (default: 'APP')\r\n */\r\nexport const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format invoice number with prefix\r\n * @param number - Invoice number\r\n * @param prefix - Prefix to add (default: 'INV')\r\n */\r\nexport const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format task number with prefix\r\n * @param number - Task number\r\n * @param prefix - Prefix to add (default: 'TASK')\r\n */\r\nexport const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Generate a random reference ID\r\n * @param length - Length of the ID (default: 8)\r\n * @param prefix - Optional prefix\r\n */\r\nexport const generateReferenceId = (length: number = 8, prefix?: string): string => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n\r\n  return prefix ? `${prefix}-${result}` : result;\r\n};\r\n\r\n/**\r\n * Convert UUID to user-friendly reference ID for customers\r\n * @param uuid - The UUID to convert\r\n * @param prefix - Optional prefix (default: 'REF')\r\n */\r\nexport const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {\r\n  if (!uuid) return '';\r\n\r\n  // Take first 8 characters of UUID (without hyphens) and convert to uppercase\r\n  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();\r\n  const shortId = cleanUuid.substring(0, 8);\r\n\r\n  return `${prefix}-${shortId}`;\r\n};\r\n\r\n/**\r\n * Mask sensitive ID (show only first and last 2 characters)\r\n * @param id - ID to mask\r\n */\r\nexport const maskId = (id: string): string => {\r\n  if (!id || id.length <= 4) return id;\r\n\r\n  const start = id.slice(0, 2);\r\n  const end = id.slice(-2);\r\n  const middle = '*'.repeat(id.length - 4);\r\n\r\n  return `${start}${middle}${end}`;\r\n};\r\n\r\n// ============================================================================\r\n// STATUS & BADGE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format status text for display\r\n * @param status - Status to format\r\n */\r\nexport const formatStatus = (status: string): string => {\r\n  if (!status) return '';\r\n\r\n  return status\r\n    .replace(/_/g, ' ')\r\n    .replace(/\\b\\w/g, char => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Get status color class\r\n * @param status - Status to get color for\r\n */\r\nexport const getStatusColor = (status: string): string => {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  switch (statusLower) {\r\n    case 'active':\r\n    case 'approved':\r\n    case 'completed':\r\n    case 'paid':\r\n    case 'success':\r\n      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n\r\n    case 'pending':\r\n    case 'in_progress':\r\n    case 'processing':\r\n    case 'review':\r\n      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';\r\n\r\n    case 'rejected':\r\n    case 'failed':\r\n    case 'error':\r\n    case 'overdue':\r\n    case 'cancelled':\r\n      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n\r\n    case 'draft':\r\n    case 'inactive':\r\n    case 'disabled':\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n\r\n    case 'warning':\r\n    case 'attention':\r\n      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n\r\n    default:\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// FILE SIZE & VALIDATION FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format file size in human readable format\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 2): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Get file extension from filename\r\n * @param filename - Filename to extract extension from\r\n */\r\nexport const getFileExtension = (filename: string): string => {\r\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\r\n};\r\n\r\n/**\r\n * Get file type icon class based on extension\r\n * @param filename - Filename to get icon for\r\n */\r\nexport const getFileTypeIcon = (filename: string): string => {\r\n  const extension = getFileExtension(filename).toLowerCase();\r\n\r\n  switch (extension) {\r\n    case 'pdf':\r\n      return 'ri-file-pdf-line text-red-500';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'ri-file-word-line text-blue-500';\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'ri-file-excel-line text-green-500';\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'ri-file-ppt-line text-orange-500';\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'png':\r\n    case 'gif':\r\n    case 'bmp':\r\n      return 'ri-image-line text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n      return 'ri-file-zip-line text-yellow-500';\r\n    case 'txt':\r\n      return 'ri-file-text-line text-gray-500';\r\n    default:\r\n      return 'ri-file-line text-gray-500';\r\n  }\r\n};"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAC5B,QACA,WAAmB,KAAK,EACxB,wBAAgC,CAAC;IAEjC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,IAAG,YAAY,KAAK,WAAW;IAE/B,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AAYO,MAAM,eAAe,CAC1B,QACA,WAAmB,KAAK,EACxB,SAAiB,OAAO;IAExB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO,GAAG,SAAS,KAAK,CAAC;IAE/C,OAAO,GAAG,SAAS,CAAC,EAAE,UAAU,cAAc,CAAC,QAAQ;QACrD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAOO,MAAM,eAAe,CAAC,QAAyB,WAAmB,CAAC;IACxE,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO;IAE7B,OAAO,UAAU,cAAc,CAAC,SAAS;QACvC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAQO,MAAM,2BAA2B,CACtC,KACA;IAEA,6BAA6B;IAC7B,IAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,IAAI,QAAQ,QAAQ,GAAG;QAC3D,mFAAmF;QACnF,OAAO,eAAe;IACxB;IAEA,uCAAuC;IACvC,OAAO,eAAe;AACxB;AAOO,MAAM,mBAAmB,CAAC,OAAwB,WAAmB,CAAC;IAC3E,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;IACjE,IAAI,MAAM,WAAW,OAAO;IAE5B,OAAO,GAAG,SAAS,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC;AAaO,MAAM,aAAa,CACxB,YACA,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,GAAG,WAAW,MAAM,IAAI,EAAE,WAAW,OAAO;AACrD;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IACnF,IAAI,gBAAgB,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,SAAS,WAAW,CAAC;IAExF,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,UAAU,UAAU,CAAC;AAC5D;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAUO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC,MAAM;QACrC,OAAO,UAAU,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAC5D,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC;QAC/B,OAAO,KAAK,WAAW;IACzB,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;QAC5B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAYO,MAAM,eAAe,CAAC,MAAc,WAAmB,SAAiB,KAAK;IAClF,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW,OAAO,QAAQ;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACxD;AAMO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AACxD;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;AACtC;AAOO,MAAM,cAAc,CAAC,MAAc,cAAsB,CAAC;IAC/D,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC;AACV;AAMO,MAAM,SAAS,CAAC;IACrB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAQO,MAAM,gBAAgB,CAAC,MAAc,YAAoB,YAAoB,WAAW;IAC7F,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,CAAC;AACnE;AAWO,MAAM,cAAc,CAAC,OAAe,SAAmD,UAAU;IACtG,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO,OAAO,+BAA+B;IAErE,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI;QAChG,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QACjF,KAAK;YACH,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QAC/E;YACE,OAAO;IACX;AACF;AAMO,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAE3C,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;IACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;IAEjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;IAChH,OAAO,GAAG,eAAe,CAAC,EAAE,QAAQ;AACtC;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAWO,MAAM,0BAA0B,CAAC,QAAyB,SAAiB,KAAK;IACrF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,QAAyB,SAAiB,KAAK;IACjF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,mBAAmB,CAAC,QAAyB,SAAiB,MAAM;IAC/E,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,SAAiB,CAAC,EAAE;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IAEA,OAAO,SAAS,GAAG,OAAO,CAAC,EAAE,QAAQ,GAAG;AAC1C;AAOO,MAAM,4BAA4B,CAAC,MAAc,SAAiB,KAAK;IAC5E,IAAI,CAAC,MAAM,OAAO;IAElB,6EAA6E;IAC7E,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,WAAW;IACpD,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;IAEvC,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS;AAC/B;AAMO,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG;IAEtC,OAAO,GAAG,QAAQ,SAAS,KAAK;AAClC;AAUO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AAC9C;AAMO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,WAAW;IAEtC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAWO,MAAM,iBAAiB,CAAC,OAAe,WAAmB,CAAC;IAChE,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAMO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAMO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseTypesTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { licenseTypeService } from '../../services/licenseTypeService';\r\nimport DataTable from '../common/DataTable';\r\nimport { formatDate } from '../../utils/formatters';\r\nimport { LicenseType, PaginatedResponse, PaginateQuery } from '@/types';\r\n\r\ninterface LicenseTypesTabProps {\r\n  onEditLicenseType: (licenseType: LicenseType) => void;\r\n  onCreateLicenseType: () => void;\r\n  refreshTrigger?: number;\r\n}\r\n\r\nconst LicenseTypesTab = ({ onEditLicenseType, onCreateLicenseType, refreshTrigger }: LicenseTypesTabProps) => {\r\n  const [licenseTypesData, setLicenseTypesData] = useState<PaginatedResponse<LicenseType>>();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  useEffect(() => {\r\n    loadLicenseTypes({ page: 1, limit: 10 });\r\n  }, [refreshTrigger]);\r\n\r\n  const loadLicenseTypes = async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      const response = await licenseTypeService.getLicenseTypes(query);\r\n      setLicenseTypesData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading license types:', err);\r\n      setError(err.response?.data?.message || 'Failed to load license types');\r\n      // Set empty data structure to prevent undefined errors\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (licenseType: LicenseType) => {\r\n    const confirmMessage = `Are you sure you want to delete the license type \"${licenseType.name}\"?\\n\\nThis action cannot be undone and may affect related license categories.`;\r\n\r\n    if (!confirm(confirmMessage)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await licenseTypeService.deleteLicenseType(licenseType.license_type_id);\r\n      await loadLicenseTypes({ page: 1, limit: 10 }); // Reload the list\r\n    } catch (err: any) {\r\n      console.error('Error deleting license type:', err);\r\n      const errorMessage = err.response?.data?.message || 'Failed to delete license type';\r\n\r\n      // Check if it's a constraint error (related records exist)\r\n      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {\r\n        setError('Cannot delete this license type because it is being used by one or more license categories. Please remove or reassign the related license categories first.');\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Define columns for DataTable\r\n  const licenseTypeColumns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Name',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseType) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n          {item.name}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'description',\r\n      label: 'Description',\r\n      sortable: false,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseType) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate\">\r\n          {item.description}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'created_at',\r\n      label: 'Created',\r\n      sortable: true,\r\n      render: (value: unknown, item: LicenseType) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {formatDate(item.created_at)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (value: unknown, item: LicenseType) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => onEditLicenseType(item)}\r\n            className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\r\n            title=\"Edit license type\"\r\n          >\r\n            <i className=\"ri-edit-line text-lg\"></i>\r\n          </button>\r\n          <button\r\n            onClick={() => handleDelete(item)}\r\n            className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\r\n            title=\"Delete license type\"\r\n          >\r\n            <i className=\"ri-delete-bin-line text-lg\"></i>\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Search and Add Button */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-white\">License Types</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            Manage license types and their validity periods\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={onCreateLicenseType}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200\"\r\n        >\r\n          <svg className=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n          </svg>\r\n          Add License Type\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* License Types Table */}\r\n      <DataTable\r\n        columns={licenseTypeColumns as any}\r\n        data={licenseTypesData as any}\r\n        loading={loading}\r\n        onQueryChange={(query) => {\r\n          loadLicenseTypes({\r\n            page: query.page,\r\n            limit: query.limit,\r\n            search: query.search,\r\n            sortBy: query.sortBy,\r\n          });\r\n        }}\r\n        searchPlaceholder=\"Search license types by name or description...\"\r\n        emptyStateMessage=\"No license types found\"\r\n        emptyStateIcon=\"ri-file-list-3-line\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseTypesTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcA,MAAM,kBAAkB,CAAC,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,cAAc,EAAwB;IACvG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;YAAE,MAAM;YAAG,OAAO;QAAG;IACxC,GAAG;QAAC;KAAe;IAEnB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC1D,oBAAoB;QACtB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QACxC,uDAAuD;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,iBAAiB,CAAC,kDAAkD,EAAE,YAAY,IAAI,CAAC,6EAA6E,CAAC;QAE3K,IAAI,CAAC,QAAQ,iBAAiB;YAC5B;QACF;QAEA,IAAI;YACF,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,YAAY,eAAe;YACtE,MAAM,iBAAiB;gBAAE,MAAM;gBAAG,OAAO;YAAG,IAAI,kBAAkB;QACpE,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAEpD,2DAA2D;YAC3D,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,gBAAgB;gBAC/G,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB;QACzB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,IAAI;;;;;;QAGhB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,WAAW;;;;;;QAGvB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;QAGjC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;QAIrB;KACD;IAID,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;;YAMT,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC,yIAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe,CAAC;oBACd,iBAAiB;wBACf,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,KAAK;wBAClB,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,mBAAkB;gBAClB,mBAAkB;gBAClB,gBAAe;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\nimport { LicenseCategory, PaginatedResponse, PaginateQuery, CreateLicenseCategoryDto, UpdateLicenseCategoryDto, LicenseType } from '@/types';\r\n\r\n// Utility functions for category codes\r\nexport const generateCategoryCode = (name: string): string => {\r\n  return name\r\n    .toLowerCase()\r\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\r\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\r\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\r\n    .substring(0, 50); // Limit length\r\n};\r\n\r\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\r\n  return categories.map(category => ({\r\n    ...category,\r\n    code: generateCategoryCode(category.name),\r\n    children: category.children ? addCodesToCategories(category.children) : undefined\r\n  }));\r\n};\r\n\r\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category?.license_type?.code === code) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryByCode(category.children, code);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\r\n  for (const category of categories) {\r\n    if (category.license_category_id === id) {\r\n      return category;\r\n    }\r\n    if (category.children) {\r\n      const found = findCategoryById(category.children, id);\r\n      if (found) return found;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n\r\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\r\n\r\n\r\nexport const licenseCategoryService = {\r\n  // Get all license categories with pagination\r\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license category by ID with timeout and retry handling\r\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/${id}`, {\r\n        timeout: 30000, // 30 second timeout for individual requests\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error fetching license category:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license categories by license type with improved error handling\r\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error('Error fetching license categories by type:', error);\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create new license category\r\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\r\n    return response.data;\r\n  },\r\n\r\n  // Update license category\r\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\r\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license category\r\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-categories/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license categories (simple list for dropdowns) with caching\r\n  async getAllLicenseCategories(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_CATEGORIES,\r\n      async () => {\r\n        console.log('Fetching license categories from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseCategories({ limit: 100 });\r\n        return addCodesToCategories(processApiResponse(response));\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get hierarchical tree of categories for a license type with caching\r\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `category-tree-${licenseTypeId}`,\r\n      async () => {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n        return addCodesToCategories(processApiResponse(response));\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get root categories (no parent) for a license type with caching\r\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\r\n    return cacheService.getOrSet(\r\n      `root-categories-${licenseTypeId}`,\r\n      async () => {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\r\n        return processApiResponse(response);\r\n      },\r\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\r\n    );\r\n  },\r\n\r\n  // Get license categories for parent selection dropdown\r\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    try {\r\n      const params = excludeId ? { excludeId } : {};\r\n      // Try the new endpoint first\r\n      try {\r\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\r\n          const data = processApiResponse(response).data;\r\n        if (data) {\r\n          return data.data;\r\n        } else {\r\n          return [];\r\n        }\r\n      } catch (newEndpointError) {\r\n        // Fallback to existing endpoint\r\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n        // Filter out the excluded category if specified\r\n        let categories : LicenseCategory[] = processApiResponse(response);\r\n        if (excludeId) {\r\n          categories = categories.filter(cat => cat.license_category_id !== excludeId);\r\n        }\r\n        return categories;\r\n  \r\n      }\r\n    } catch (error) {\r\n\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Get potential parent categories for a license type\r\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\r\n    const params = excludeId ? { excludeId } : {};\r\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AACA;;;;AAIO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,UAAU,cAAc,SAAS,MAAM;YACzC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAMO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6DAA6D;IAC7D,MAAM,oBAAmB,EAAU;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,sEAAsE;IACtE,MAAM,4BAA2B,aAAqB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe,EAAE;gBAC3F,SAAS;YACX;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YACA,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QACjD,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QACjD,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBACtH,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;gBAChD,IAAI,MAAM;oBACR,OAAO,KAAK,IAAI;gBAClB,OAAO;oBACL,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,gCAAgC;gBAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,gDAAgD;gBAChD,IAAI,aAAiC,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;gBACxD,IAAI,WAAW;oBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;gBACpE;gBACA,OAAO;YAET;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseCategoriesTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport {  licenseCategoryService, LicenseCategoriesResponse } from '../../services/licenseCategoryService';\r\nimport { formatCurrency, formatDate, formatLicenseCategoryFee } from '../../utils/formatters';\r\nimport DataTable from '../common/DataTable';\r\nimport { LicenseCategory, PaginateQuery } from '@/types';\r\n\r\ninterface LicenseCategoriesTabProps {\r\n  onEditLicenseCategory: (licenseCategory: LicenseCategory) => void;\r\n  onCreateLicenseCategory: () => void;\r\n  refreshTrigger?: number;\r\n}\r\n\r\nconst LicenseCategoriesTab = ({ onEditLicenseCategory, onCreateLicenseCategory, refreshTrigger }: LicenseCategoriesTabProps) => {\r\n  const [licenseCategoriesData, setLicenseCategoriesData] = useState<LicenseCategoriesResponse | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  useEffect(() => {\r\n    loadLicenseCategories({ page: 1, limit: 10 });\r\n  }, [refreshTrigger]);\r\n\r\n  const loadLicenseCategories = async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      const response = await licenseCategoryService.getLicenseCategories(query);\r\n      setLicenseCategoriesData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading license categories:', err);\r\n      setError(err.response?.data?.message || 'Failed to load license categories');\r\n      // Set empty data structure to prevent undefined errors\r\n      setLicenseCategoriesData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (licenseCategory: LicenseCategory) => {\r\n    const confirmMessage = `Are you sure you want to delete the license category \"${licenseCategory.name}\"?\\n\\nThis action cannot be undone and may affect users who have licenses of this category.`;\r\n\r\n    if (!confirm(confirmMessage)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await licenseCategoryService.deleteLicenseCategory(licenseCategory.license_category_id);\r\n      await loadLicenseCategories({ page: 1, limit: 10 }); // Reload the list\r\n    } catch (err: any) {\r\n      console.error('Error deleting license category:', err);\r\n      const errorMessage = err.response?.data?.message || 'Failed to delete license category';\r\n\r\n      // Check if it's a constraint error (related records exist)\r\n      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {\r\n        setError('Cannot delete this license category because it is being used by one or more user licenses. Please remove or reassign the related licenses first.');\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Define columns for DataTable\r\n  const licenseCategoryColumns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Name',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n          {item.name}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'validity',\r\n      label: 'Validity',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n          {item.validity}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'description',\r\n      label: 'Description',\r\n      sortable: false,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate\">\r\n          {item.description}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'authorizes',\r\n      label: 'Authorizes',\r\n      sortable: false,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate\">\r\n          {item.authorizes || 'N/A'}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'application_fee',\r\n      label: 'Application Fee',\r\n      sortable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-white\">\r\n          {formatLicenseCategoryFee(item?.fee?? 0 ,item.name,)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'created_at',\r\n      label: 'Created',\r\n      sortable: true,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {formatDate(item.created_at)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (value: unknown, item: LicenseCategory) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => onEditLicenseCategory(item)}\r\n            className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\r\n            title=\"Edit license category\"\r\n          >\r\n            <i className=\"ri-edit-line text-lg\"></i>\r\n          </button>\r\n          <button\r\n            onClick={() => handleDelete(item)}\r\n            className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\r\n            title=\"Delete license category\"\r\n          >\r\n            <i className=\"ri-delete-bin-line text-lg\"></i>\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Search and Add Button */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-white\">License Categories</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            Manage license categories, fees, and authorizations\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={onCreateLicenseCategory}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200\"\r\n        >\r\n          <svg className=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n          </svg>\r\n          Add License Category\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* License Categories Table */}\r\n      <DataTable\r\n        columns={licenseCategoryColumns as any}\r\n        data={licenseCategoriesData as any}\r\n        loading={loading}\r\n        onQueryChange={(query) => {\r\n          loadLicenseCategories({\r\n            page: query.page,\r\n            limit: query.limit,\r\n            search: query.search,\r\n            sortBy: query.sortBy,\r\n          });\r\n        }}\r\n        searchPlaceholder=\"Search license categories by name, description, or authorization...\"\r\n        emptyStateMessage=\"No license categories found\"\r\n        emptyStateIcon=\"ri-price-tag-3-line\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseCategoriesTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcA,MAAM,uBAAuB,CAAC,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,cAAc,EAA6B;IACzH,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACrG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;YAAE,MAAM;YAAG,OAAO;QAAG;IAC7C,GAAG;QAAC;KAAe;IAEnB,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,oBAAoB,CAAC;YACnE,yBAAyB;QAC3B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YACxC,uDAAuD;YACvD,yBAAyB;gBACvB,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,iBAAiB,CAAC,sDAAsD,EAAE,gBAAgB,IAAI,CAAC,2FAA2F,CAAC;QAEjM,IAAI,CAAC,QAAQ,iBAAiB;YAC5B;QACF;QAEA,IAAI;YACF,MAAM,yIAAA,CAAA,yBAAsB,CAAC,qBAAqB,CAAC,gBAAgB,mBAAmB;YACtF,MAAM,sBAAsB;gBAAE,MAAM;gBAAG,OAAO;YAAG,IAAI,kBAAkB;QACzE,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAEpD,2DAA2D;YAC3D,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,gBAAgB;gBAC/G,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB;QAC7B;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,IAAI;;;;;;QAGhB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ;;;;;;QAGpB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,WAAW;;;;;;QAGvB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,UAAU,IAAI;;;;;;QAG1B;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0HAAA,CAAA,2BAAwB,AAAD,EAAE,MAAM,OAAM,GAAG,KAAK,IAAI;;;;;;QAGxD;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;QAGjC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,sBAAsB;4BACrC,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;QAIrB;KACD;IAID,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;;YAMT,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC,yIAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe,CAAC;oBACd,sBAAsB;wBACpB,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,KAAK;wBAClB,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,mBAAkB;gBAClB,mBAAkB;gBAClB,gBAAe;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 1971, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/identificationTypeService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';\r\n\r\n// Types\r\nexport interface IdentificationType extends BaseEntity {\r\n  identification_type_id: string;\r\n  name: string;\r\n  creator?: UserReference;\r\n  updater?: UserReference;\r\n  user_identifications?: any[];\r\n}\r\n\r\nexport interface CreateIdentificationTypeDto {\r\n  name: string;\r\n}\r\n\r\nexport interface UpdateIdentificationTypeDto {\r\n  name?: string;\r\n}\r\n\r\nexport type IdentificationTypesResponse = PaginatedResponse<IdentificationType>;\r\n\r\nexport const identificationTypeService = {\r\n  // Get all identification types with pagination\r\n  async getIdentificationTypes(query: PaginateQuery = {}): Promise<IdentificationTypesResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/identification-types?${params.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get identification type by ID\r\n  async getIdentificationType(id: string): Promise<IdentificationType> {\r\n    const response = await apiClient.get(`/identification-types/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get simple list of identification types\r\n  async getSimpleIdentificationTypes(): Promise<IdentificationType[]> {\r\n    const response = await apiClient.get('/identification-types/simple');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new identification type\r\n  async createIdentificationType(identificationTypeData: CreateIdentificationTypeDto): Promise<IdentificationType> {\r\n    const response = await apiClient.post('/identification-types', identificationTypeData);\r\n    return response.data;\r\n  },\r\n\r\n  // Update identification type\r\n  async updateIdentificationType(id: string, identificationTypeData: UpdateIdentificationTypeDto): Promise<IdentificationType> {\r\n    const response = await apiClient.put(`/identification-types/${id}`, identificationTypeData);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete identification type\r\n  async deleteIdentificationType(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/identification-types/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get all identification types (simple list for dropdowns)\r\n  async getAllIdentificationTypes(): Promise<IdentificationType[]> {\r\n    const response = await this.getIdentificationTypes({ limit: 1000 });\r\n    return response.data;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAsBO,MAAM,4BAA4B;IACvC,+CAA+C;IAC/C,MAAM,wBAAuB,QAAuB,CAAC,CAAC;QACpD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,OAAO,QAAQ,IAAI;QACjF,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0CAA0C;IAC1C,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,iCAAiC;IACjC,MAAM,0BAAyB,sBAAmD;QAChF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,yBAAyB;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,0BAAyB,EAAU,EAAE,sBAAmD;QAC5F,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,0BAAyB,EAAU;QACvC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,2DAA2D;IAC3D,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,CAAC,sBAAsB,CAAC;YAAE,OAAO;QAAK;QACjE,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/IdentificationTypesTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { IdentificationType, identificationTypeService, IdentificationTypesResponse } from '../../services/identificationTypeService';\r\nimport DataTable from '../common/DataTable';\r\nimport { formatDate } from '../../utils/formatters';\r\nimport { PaginateQuery } from '@/types';\r\n\r\ninterface IdentificationTypesTabProps {\r\n  onEditIdentificationType: (identificationType: IdentificationType) => void;\r\n  onCreateIdentificationType: () => void;\r\n  refreshTrigger?: number;\r\n}\r\n\r\nconst IdentificationTypesTab = ({ onEditIdentificationType, onCreateIdentificationType, refreshTrigger }: IdentificationTypesTabProps) => {\r\n  const [identificationTypesData, setIdentificationTypesData] = useState<IdentificationTypesResponse | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  useEffect(() => {\r\n    loadIdentificationTypes({ page: 1, limit: 10 });\r\n  }, [refreshTrigger]);\r\n\r\n  const loadIdentificationTypes = async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      const response = await identificationTypeService.getIdentificationTypes(query);\r\n      setIdentificationTypesData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading identification types:', err);\r\n      setError(err.response?.data?.message || 'Failed to load identification types');\r\n      // Set empty data structure to prevent undefined errors\r\n      setIdentificationTypesData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (identificationType: IdentificationType) => {\r\n    const usageCount = identificationType.user_identifications?.length || 0;\r\n    let confirmMessage = `Are you sure you want to delete the identification type \"${identificationType.name}\"?\\n\\nThis action cannot be undone.`;\r\n\r\n    if (usageCount > 0) {\r\n      confirmMessage += `\\n\\nWarning: This identification type is currently being used by ${usageCount} user(s). Deleting it may affect user profiles.`;\r\n    }\r\n\r\n    if (!confirm(confirmMessage)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await identificationTypeService.deleteIdentificationType(identificationType.identification_type_id);\r\n      await loadIdentificationTypes({ page: 1, limit: 10 }); // Reload the list\r\n    } catch (err: any) {\r\n      console.error('Error deleting identification type:', err);\r\n      const errorMessage = err.response?.data?.message || 'Failed to delete identification type';\r\n\r\n      // Check if it's a constraint error (related records exist)\r\n      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {\r\n        setError('Cannot delete this identification type because it is being used by one or more users. Please remove or update the related user identifications first.');\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Define columns for DataTable\r\n  const identificationTypeColumns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Name',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: IdentificationType) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n          {item.name}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'created_at',\r\n      label: 'Created',\r\n      sortable: true,\r\n      render: (value: unknown, item: IdentificationType) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {formatDate(item.created_at)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (value: unknown, item: IdentificationType) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => onEditIdentificationType(item)}\r\n            className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\r\n            title=\"Edit identification type\"\r\n          >\r\n            <i className=\"ri-edit-line text-lg\"></i>\r\n          </button>\r\n          <button\r\n            onClick={() => handleDelete(item)}\r\n            className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\r\n            title=\"Delete identification type\"\r\n          >\r\n            <i className=\"ri-delete-bin-line text-lg\"></i>\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Search and Add Button */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-white\">Identification Types</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            Manage identification document types accepted by the system\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={onCreateIdentificationType}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200\"\r\n        >\r\n          <svg className=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n          </svg>\r\n          Add Identification Type\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Identification Types Table */}\r\n      <DataTable\r\n        columns={identificationTypeColumns as any}\r\n        data={identificationTypesData as any}\r\n        loading={loading}\r\n        onQueryChange={(query) => {\r\n          loadIdentificationTypes({\r\n            page: query.page,\r\n            limit: query.limit,\r\n            search: query.search,\r\n            sortBy: query.sortBy,\r\n          });\r\n        }}\r\n        searchPlaceholder=\"Search identification types by name...\"\r\n        emptyStateMessage=\"No identification types found\"\r\n        emptyStateIcon=\"ri-id-card-line\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IdentificationTypesTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcA,MAAM,yBAAyB,CAAC,EAAE,wBAAwB,EAAE,0BAA0B,EAAE,cAAc,EAA+B;IACnI,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAC3G,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB;YAAE,MAAM;YAAG,OAAO;QAAG;IAC/C,GAAG;QAAC;KAAe;IAEnB,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,4IAAA,CAAA,4BAAyB,CAAC,sBAAsB,CAAC;YACxE,2BAA2B;QAC7B,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YACxC,uDAAuD;YACvD,2BAA2B;gBACzB,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,aAAa,mBAAmB,oBAAoB,EAAE,UAAU;QACtE,IAAI,iBAAiB,CAAC,yDAAyD,EAAE,mBAAmB,IAAI,CAAC,mCAAmC,CAAC;QAE7I,IAAI,aAAa,GAAG;YAClB,kBAAkB,CAAC,iEAAiE,EAAE,WAAW,+CAA+C,CAAC;QACnJ;QAEA,IAAI,CAAC,QAAQ,iBAAiB;YAC5B;QACF;QAEA,IAAI;YACF,MAAM,4IAAA,CAAA,4BAAyB,CAAC,wBAAwB,CAAC,mBAAmB,sBAAsB;YAClG,MAAM,wBAAwB;gBAAE,MAAM;gBAAG,OAAO;YAAG,IAAI,kBAAkB;QAC3E,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAEpD,2DAA2D;YAC3D,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,gBAAgB;gBAC/G,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,4BAA4B;QAChC;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,IAAI;;;;;;QAGhB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;QAGjC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,yBAAyB;4BACxC,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;QAIrB;KACD;IAID,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;;YAMT,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC,yIAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe,CAAC;oBACd,wBAAwB;wBACtB,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,KAAK;wBAClB,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,mBAAkB;gBAClB,mBAAkB;gBAClB,gBAAe;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryDocumentService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference, LicenseCategoryDocument, CreateLicenseCategoryDocumentDto, UpdateLicenseCategoryDocumentDto } from '@/types';\r\n\r\n\r\nexport type LicenseCategoryDocumentsResponse = PaginatedResponse<LicenseCategoryDocument>;\r\n\r\nexport const licenseCategoryDocumentService = {\r\n  // Get all license category documents with pagination\r\n  async getLicenseCategoryDocuments(query: PaginateQuery = {}): Promise<LicenseCategoryDocumentsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-category-documents?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license category document by ID\r\n  async getLicenseCategoryDocument(id: string): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.get(`/license-category-documents/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license category documents by license category\r\n  async getLicenseCategoryDocumentsByCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]> {\r\n    const response = await apiClient.get(`/license-category-documents/by-license-category/${licenseCategoryId}`);\r\n    return processApiResponse(response).data;\r\n  },\r\n\r\n  // Create new license category document\r\n  async createLicenseCategoryDocument(documentData: CreateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.post('/license-category-documents', documentData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license category document\r\n  async updateLicenseCategoryDocument(id: string, documentData: UpdateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {\r\n    const response = await apiClient.patch(`/license-category-documents/${id}`, documentData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license category document\r\n  async deleteLicenseCategoryDocument(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-category-documents/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license category documents (simple list for dropdowns)\r\n  async getAllLicenseCategoryDocuments(): Promise<LicenseCategoryDocument[]> {\r\n    const response = await this.getLicenseCategoryDocuments({ limit: 1000 });\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMO,MAAM,iCAAiC;IAC5C,qDAAqD;IACrD,MAAM,6BAA4B,QAAuB,CAAC,CAAC;QACzD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,OAAO,QAAQ,IAAI;QACvF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sCAAsC;IACtC,MAAM,4BAA2B,EAAU;QACzC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;QACxE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qDAAqD;IACrD,MAAM,uCAAsC,iBAAyB;QACnE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,gDAAgD,EAAE,mBAAmB;QAC3G,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;IAC1C;IAEA,uCAAuC;IACvC,MAAM,+BAA8B,YAA8C;QAChF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,+BAA+B;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,+BAA8B,EAAU,EAAE,YAA8C;QAC5F,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,IAAI,EAAE;QAC5E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,+BAA8B,EAAU;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,4BAA4B,EAAE,IAAI;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,CAAC,2BAA2B,CAAC;YAAE,OAAO;QAAK;QACtE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseCategoryDocumentsTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { licenseCategoryDocumentService, LicenseCategoryDocumentsResponse } from '../../services/licenseCategoryDocumentService';\r\nimport { LicenseCategoryDocument, PaginateQuery } from '@/types';\r\nimport DataTable from '../common/DataTable';\r\nimport { formatDate } from '../../utils/formatters';\r\n\r\ninterface LicenseCategoryDocumentsTabProps {\r\n  onEditLicenseCategoryDocument: (document: LicenseCategoryDocument) => void;\r\n  onCreateLicenseCategoryDocument: () => void;\r\n  refreshTrigger?: number;\r\n}\r\n\r\nconst LicenseCategoryDocumentsTab = ({ onEditLicenseCategoryDocument, onCreateLicenseCategoryDocument, refreshTrigger }: LicenseCategoryDocumentsTabProps) => {\r\n  const [documentsData, setDocumentsData] = useState<LicenseCategoryDocumentsResponse | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  useEffect(() => {\r\n    loadDocuments({ page: 1, limit: 10 });\r\n  }, [refreshTrigger]);\r\n\r\n  const loadDocuments = async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      const response = await licenseCategoryDocumentService.getLicenseCategoryDocuments(query);\r\n      setDocumentsData(response);\r\n    } catch (err: any) {\r\n      console.error('Error loading license category documents:', err);\r\n      setError(err.response?.data?.message || 'Failed to load license category documents');\r\n      // Set empty data structure to prevent undefined errors\r\n      setDocumentsData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (document: LicenseCategoryDocument) => {\r\n    const confirmMessage = `Are you sure you want to delete the document requirement \"${document.name}\"?\\n\\nThis action cannot be undone and may affect license applications for the \"${document.license_category?.name}\" category.`;\r\n    \r\n    if (!confirm(confirmMessage)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await licenseCategoryDocumentService.deleteLicenseCategoryDocument(document.license_category_document_id);\r\n      await loadDocuments({ page: 1, limit: 10 }); // Reload the list\r\n    } catch (err: any) {\r\n      console.error('Error deleting license category document:', err);\r\n      const errorMessage = err.response?.data?.message || 'Failed to delete license category document';\r\n\r\n      // Check if it's a constraint error (related records exist)\r\n      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {\r\n        setError('Cannot delete this document requirement because it is being used by one or more license applications. Please remove or update the related applications first.');\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Define columns for DataTable\r\n  const documentColumns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Document Name',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: LicenseCategoryDocument) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n          {item.name}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'is_required',\r\n      label: 'Required',\r\n      sortable: true,\r\n      render: (value: unknown, item: LicenseCategoryDocument) => (\r\n        <div className=\"text-sm\">\r\n          {item.is_required ? (\r\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400\">\r\n              Required\r\n            </span>\r\n          ) : (\r\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400\">\r\n              Optional\r\n            </span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'license_category',\r\n      label: 'License Category',\r\n      sortable: false,\r\n      render: (value: unknown, item: LicenseCategoryDocument) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {item.license_category?.name || 'N/A'}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'created_at',\r\n      label: 'Created',\r\n      sortable: true,\r\n      render: (value: unknown, item: LicenseCategoryDocument) => (\r\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {formatDate(item.created_at)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (value: unknown, item: LicenseCategoryDocument) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => onEditLicenseCategoryDocument(item)}\r\n            className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\r\n            title=\"Edit document requirement\"\r\n          >\r\n            <i className=\"ri-edit-line text-lg\"></i>\r\n          </button>\r\n          <button\r\n            onClick={() => handleDelete(item)}\r\n            className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\r\n            title=\"Delete document requirement\"\r\n          >\r\n            <i className=\"ri-delete-bin-line text-lg\"></i>\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Add Button */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-white\">License Category Documents</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            Manage document requirements for license categories\r\n          </p>\r\n        </div>\r\n        <button\r\n          onClick={onCreateLicenseCategoryDocument}\r\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200\"\r\n        >\r\n          <svg className=\"-ml-1 mr-2 h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n          </svg>\r\n          Add Document Requirement\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Documents Table */}\r\n      <DataTable\r\n        columns={documentColumns as any}\r\n        data={documentsData as any}\r\n        loading={loading}\r\n        onQueryChange={(query) => {\r\n          loadDocuments({\r\n            page: query.page,\r\n            limit: query.limit,\r\n            search: query.search,\r\n            sortBy: query.sortBy,\r\n          });\r\n        }}\r\n        searchPlaceholder=\"Search document requirements by name...\"\r\n        emptyStateMessage=\"No document requirements found\"\r\n        emptyStateIcon=\"ri-file-list-3-line\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseCategoryDocumentsTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAcA,MAAM,8BAA8B,CAAC,EAAE,6BAA6B,EAAE,+BAA+B,EAAE,cAAc,EAAoC;IACvJ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2C;IAC5F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;YAAE,MAAM;YAAG,OAAO;QAAG;IACrC,GAAG;QAAC;KAAe;IAEnB,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,iJAAA,CAAA,iCAA8B,CAAC,2BAA2B,CAAC;YAClF,iBAAiB;QACnB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YACxC,uDAAuD;YACvD,iBAAiB;gBACf,MAAM,EAAE;gBACR,MAAM;oBACJ,cAAc,MAAM,KAAK,IAAI;oBAC7B,YAAY;oBACZ,aAAa,MAAM,IAAI,IAAI;oBAC3B,YAAY;oBACZ,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ;oBACR,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBACL,SAAS;gBACX;YACF;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,iBAAiB,CAAC,0DAA0D,EAAE,SAAS,IAAI,CAAC,gFAAgF,EAAE,SAAS,gBAAgB,EAAE,KAAK,WAAW,CAAC;QAEhO,IAAI,CAAC,QAAQ,iBAAiB;YAC5B;QACF;QAEA,IAAI;YACF,MAAM,iJAAA,CAAA,iCAA8B,CAAC,6BAA6B,CAAC,SAAS,4BAA4B;YACxG,MAAM,cAAc;gBAAE,MAAM;gBAAG,OAAO;YAAG,IAAI,kBAAkB;QACjE,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAEpD,2DAA2D;YAC3D,IAAI,IAAI,QAAQ,EAAE,WAAW,OAAO,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,gBAAgB;gBAC/G,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,IAAI;;;;;;QAGhB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,WAAW,iBACf,8OAAC;wBAAK,WAAU;kCAA+I;;;;;iFAI/J,8OAAC;wBAAK,WAAU;kCAA2I;;;;;;;;;;;QAMnK;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,KAAK,gBAAgB,EAAE,QAAQ;;;;;;QAGtC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;QAGjC;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,8BAA8B;4BAC7C,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;QAIrB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5E,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;;YAMT,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC,yIAAA,CAAA,UAAS;gBACR,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe,CAAC;oBACd,cAAc;wBACZ,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,KAAK;wBAClB,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,MAAM;oBACtB;gBACF;gBACA,mBAAkB;gBAClB,mBAAkB;gBAClB,gBAAe;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseTypeModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport {licenseTypeService } from '../../services/licenseTypeService';\r\nimport { LicenseType, UpdateLicenseTypeDto, CreateLicenseTypeDto } from '@/types';\r\n\r\ninterface LicenseTypeModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (licenseTypeName: string, isEdit?: boolean) => void;\r\n  licenseType?: LicenseType | null;\r\n}\r\n\r\nconst LicenseTypeModal = ({ isOpen, onClose, onSave, licenseType }: LicenseTypeModalProps) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    code: '',\r\n    description: '',\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const isEdit = !!licenseType;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      if (licenseType) {\r\n        setFormData({\r\n          name: licenseType.name,\r\n          code: licenseType.code ?? '',\r\n          description: licenseType.description ?? '',\r\n        });\r\n      } else {\r\n        setFormData({\r\n          name: '',\r\n          code: '',\r\n          description: '',\r\n        });\r\n      }\r\n      setError('');\r\n    }\r\n  }, [isOpen, licenseType]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      if (isEdit && licenseType) {\r\n        const updateData: UpdateLicenseTypeDto = {\r\n          name: formData.name,\r\n          description: formData.description,\r\n        };\r\n        await licenseTypeService.updateLicenseType(licenseType.license_type_id, updateData);\r\n      } else {\r\n        const createData: CreateLicenseTypeDto = {\r\n          name: formData.name,\r\n          code: formData.code,\r\n          description: formData.description,\r\n        };\r\n        await licenseTypeService.createLicenseType(createData);\r\n      }\r\n\r\n      onSave(formData.name, isEdit);\r\n    } catch (err: any) {\r\n      console.error('Error saving license type:', err);\r\n      setError(err.response?.data?.message || 'Failed to save license type');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: name === 'validity' ? parseInt(value) || 0 : value,\r\n    }));\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n              <div className=\"sm:flex sm:items-start\">\r\n                <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\r\n                  <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\r\n                    {isEdit ? 'Edit License Type' : 'Add New License Type'}\r\n                  </h3>\r\n\r\n                  {error && (\r\n                    <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3\">\r\n                      <div className=\"flex\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div className=\"ml-3\">\r\n                          <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"space-y-4\">\r\n                    {/* Name Field */}\r\n                    <div>\r\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Name *\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        id=\"name\"\r\n                        required\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter license type name\"\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Name Field */}\r\n                    <div>\r\n                      <label htmlFor=\"code\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Code *\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"code\"\r\n                        id=\"code\"\r\n                        required\r\n                        value={formData.code}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter license type code\"\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Description Field */}\r\n                    <div>\r\n                      <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Description *\r\n                      </label>\r\n                      <textarea\r\n                        name=\"description\"\r\n                        id=\"description\"\r\n                        required\r\n                        rows={3}\r\n                        value={formData.description}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter license type description\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Footer */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    {isEdit ? 'Updating...' : 'Creating...'}\r\n                  </>\r\n                ) : (\r\n                  isEdit ? 'Update License Type' : 'Create License Type'\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                disabled={loading}\r\n                className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseTypeModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAyB;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,IAAI,aAAa;gBACf,YAAY;oBACV,MAAM,YAAY,IAAI;oBACtB,MAAM,YAAY,IAAI,IAAI;oBAC1B,aAAa,YAAY,WAAW,IAAI;gBAC1C;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;YACF;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU,aAAa;gBACzB,MAAM,aAAmC;oBACvC,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;gBACnC;gBACA,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,YAAY,eAAe,EAAE;YAC1E,OAAO;gBACL,MAAM,aAAmC;oBACvC,MAAM,SAAS,IAAI;oBACnB,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;gBACnC;gBACA,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;YAC7C;YAEA,OAAO,SAAS,IAAI,EAAE;QACxB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,SAAS,UAAU,IAAI;YACvD,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,sBAAsB;;;;;;4CAGjC,uBACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAuB,SAAQ;gEAAY,MAAK;0EAC7D,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA0N,UAAS;;;;;;;;;;;;;;;;sEAGlQ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;0DAM/D,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA6D;;;;;;0EAG7F,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA6D;;;;;;0EAG7F,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAc,WAAU;0EAA6D;;;;;;0EAGpG,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,MAAM;gEACN,OAAO,SAAS,WAAW;gEAC3B,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,MAAK;oDAAO,SAAQ;;sEAC9E,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAEpD,SAAS,gBAAgB;;2DAG5B,SAAS,wBAAwB;;;;;;kDAGrC,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseCategoryModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport {  licenseCategoryService} from '../../services/licenseCategoryService';\r\nimport { licenseTypeService } from '../../services/licenseTypeService';\r\nimport { LicenseCategory, LicenseType, UpdateLicenseCategoryDto, CreateLicenseCategoryDto } from '@/types';\r\n\r\ninterface LicenseCategoryModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (licenseCategoryName: string, isEdit?: boolean) => void;\r\n  licenseCategory?: LicenseCategory | null;\r\n}\r\n\r\nconst LicenseCategoryModal = ({ isOpen, onClose, onSave, licenseCategory }: LicenseCategoryModalProps) => {\r\n  const [formData, setFormData] = useState({\r\n    license_type_id: '',\r\n    parent_id: '',\r\n    name: '',\r\n    validity: 5,\r\n    fee: 0,\r\n    description: '',\r\n    authorizes: '',\r\n  });\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [parentCategories, setParentCategories] = useState<LicenseCategory[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(false);\r\n  const [loadingParentCategories, setLoadingParentCategories] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const isEdit = !!licenseCategory;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      loadLicenseTypes();\r\n      if (licenseCategory) {\r\n        setFormData({\r\n          license_type_id: licenseCategory.license_type_id,\r\n          parent_id: licenseCategory.parent_id || '',\r\n          name: licenseCategory.name,\r\n          validity: licenseCategory.validity?? 1,\r\n          fee: licenseCategory.fee ?? '0',\r\n          description: licenseCategory.description ?? '',\r\n          authorizes: licenseCategory.authorizes?? '',\r\n        });\r\n        // Load parent categories for the selected license type\r\n        if (licenseCategory.license_type_id) {\r\n          loadParentCategories(licenseCategory.license_type_id, licenseCategory.license_category_id);\r\n        }\r\n      } else {\r\n        setFormData({\r\n          license_type_id: '',\r\n          parent_id: '',\r\n          name: '',\r\n          validity: 5,\r\n          fee: '0',\r\n          description: '',\r\n          authorizes: '',\r\n        });\r\n      }\r\n      setError('');\r\n    }\r\n  }, [isOpen, licenseCategory]);\r\n\r\n  // Load parent categories when license type changes\r\n  useEffect(() => {\r\n    if (formData.license_type_id) {\r\n      const excludeId = licenseCategory?.license_category_id;\r\n      loadParentCategories(formData.license_type_id, excludeId);\r\n    } else {\r\n      setParentCategories([]);\r\n    }\r\n  }, [formData.license_type_id]);\r\n\r\n  const loadLicenseTypes = async () => {\r\n    try {\r\n      setLoadingLicenseTypes(true);\r\n      const types = await licenseTypeService.getAllLicenseTypes();\r\n      setLicenseTypes(types);\r\n    } catch (err: any) {\r\n      console.error('Error loading license types:', err);\r\n      setError('Failed to load license types');\r\n    } finally {\r\n      setLoadingLicenseTypes(false);\r\n    }\r\n  };\r\n\r\n  const loadParentCategories = async (licenseTypeId: string, excludeId?: string) => {\r\n    setLoadingParentCategories(true);\r\n    try {\r\n      const categories = await licenseCategoryService.getCategoriesForParentSelection(licenseTypeId, excludeId);\r\n      // Ensure categories is always an array\r\n      const processedCategories = Array.isArray(categories) ? categories : [];\r\n      setParentCategories(processedCategories);\r\n    } catch (err: any) {\r\n      setParentCategories([]); // Set empty array on error\r\n    } finally {\r\n      setLoadingParentCategories(false);\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    // Validate validity field\r\n    if (!Number.isInteger(formData.validity) || formData.validity < 1 || formData.validity > 120) {\r\n      setError('Validity must be an integer between 1 and 120 months');\r\n      return false;\r\n    }\r\n\r\n    // Validate required fields\r\n    if (!formData.license_type_id.trim()) {\r\n      setError('License type is required');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.name.trim()) {\r\n      setError('Name is required');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.description.trim()) {\r\n      setError('Description is required');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.authorizes.trim()) {\r\n      setError('Authorizes field is required');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    // Validate form before submission\r\n    if (!validateForm()) {\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (isEdit && licenseCategory) {\r\n        const updateData: UpdateLicenseCategoryDto = {\r\n          license_type_id: formData.license_type_id,\r\n          parent_id: formData.parent_id || undefined,\r\n          name: formData.name,\r\n          validity: formData.validity,\r\n          fee: formData.fee ?? 0,\r\n          description: formData.description,\r\n          authorizes: formData.authorizes,\r\n        };\r\n        await licenseCategoryService.updateLicenseCategory(licenseCategory.license_category_id, updateData);\r\n      } else {\r\n        const createData: CreateLicenseCategoryDto = {\r\n          license_type_id: formData.license_type_id,\r\n          parent_id: formData.parent_id || undefined,\r\n          name: formData.name,\r\n          validity: formData.validity,\r\n          fee: formData.fee ?? 0,\r\n          description: formData.description,\r\n          authorizes: formData.authorizes,\r\n        };\r\n        await licenseCategoryService.createLicenseCategory(createData);\r\n      }\r\n\r\n      onSave(formData.name, isEdit);\r\n    } catch (err: any) {\r\n      console.error('Error saving license category:', err);\r\n      setError(err.response?.data?.message || 'Failed to save license category');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Handle numeric fields properly\r\n    if (name === 'validity') {\r\n      const numericValue = value === '' ? 0 : Number(value);\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [name]: numericValue,\r\n      }));\r\n    } else {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        [name]: value,\r\n      }));\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div\r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n              <div className=\"sm:flex sm:items-start\">\r\n                <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\r\n                  <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\r\n                    {isEdit ? 'Edit License Category' : 'Add New License Category'}\r\n                  </h3>\r\n\r\n                  {error && (\r\n                    <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3\">\r\n                      <div className=\"flex\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div className=\"ml-3\">\r\n                          <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"space-y-4\">\r\n                    {/* License Type Field */}\r\n                    <div>\r\n                      <label htmlFor=\"license_type_id\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        License Type *\r\n                      </label>\r\n                      <select\r\n                        name=\"license_type_id\"\r\n                        id=\"license_type_id\"\r\n                        required\r\n                        value={formData.license_type_id}\r\n                        onChange={handleChange}\r\n                        disabled={loadingLicenseTypes}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                      >\r\n                        <option value=\"\">Select a license type</option>\r\n                        {licenseTypes.map((type) => (\r\n                          <option key={type.license_type_id} value={type.license_type_id}>\r\n                            {type.name}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                      {loadingLicenseTypes && (\r\n                        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">Loading license types...</p>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Parent Category Field */}\r\n                    <div>\r\n                      <label htmlFor=\"parent_id\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Parent Category (Optional)\r\n                      </label>\r\n                      <select\r\n                        name=\"parent_id\"\r\n                        id=\"parent_id\"\r\n                        value={formData.parent_id}\r\n                        onChange={handleChange}\r\n                        disabled={loadingParentCategories || !formData.license_type_id}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                      >\r\n                        <option value=\"\">No parent (Root category)</option>\r\n                        {(() => {\r\n                          console.log('🎨 Rendering parent categories:', parentCategories, 'length:', parentCategories?.length);\r\n                          if (!Array.isArray(parentCategories)) {\r\n                            console.log('⚠️ parentCategories is not an array:', typeof parentCategories);\r\n                            return null;\r\n                          }\r\n                          return parentCategories.map((category) => {\r\n                            console.log('🏷️ Rendering category:', category);\r\n                            return (\r\n                              <option key={category.license_category_id} value={category.license_category_id}>\r\n                                {category.parent ? `${category.parent.name} → ${category.name}` : category.name}\r\n                              </option>\r\n                            );\r\n                          });\r\n                        })()}\r\n                      </select>\r\n                      {loadingParentCategories && (\r\n                        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">Loading parent categories...</p>\r\n                      )}\r\n                      {!formData.license_type_id && (\r\n                        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">Select a license type first to see available parent categories</p>\r\n                      )}\r\n                      {formData.license_type_id && !loadingParentCategories && (!parentCategories || parentCategories.length === 0) && (\r\n                        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">No existing categories available as parents for this license type</p>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Name Field */}\r\n                    <div>\r\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Name *\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        id=\"name\"\r\n                        required\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter license category name\"\r\n                      />\r\n                    </div>\r\n\r\n\r\n                    {/* Validity Field */}\r\n                    <div>\r\n                      <label htmlFor=\"validity\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Validity Period (Months) *\r\n                      </label>\r\n                      <input\r\n                        type=\"number\"\r\n                        name=\"validity\"\r\n                        id=\"validity\"\r\n                        required\r\n                        min=\"1\"\r\n                        max=\"120\"\r\n                        step=\"1\"\r\n                        value={formData.validity}\r\n                        onChange={handleChange}\r\n                        className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none sm:text-sm dark:bg-gray-700 dark:text-white ${\r\n                          formData.validity >= 1 && formData.validity <= 120 && Number.isInteger(formData.validity)\r\n                            ? 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'\r\n                            : 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'\r\n                        }`}\r\n                        placeholder=\"Enter validity period in months\"\r\n                      />\r\n                      <p className={`mt-1 text-xs ${\r\n                        formData.validity >= 1 && formData.validity <= 120 && Number.isInteger(formData.validity)\r\n                          ? 'text-gray-500 dark:text-gray-400'\r\n                          : 'text-red-500 dark:text-red-400'\r\n                      }`}>\r\n                        {formData.validity >= 1 && formData.validity <= 120 && Number.isInteger(formData.validity)\r\n                          ? 'Enter a value between 1 and 120 months'\r\n                          : 'Must be an integer between 1 and 120 months'\r\n                        }\r\n                      </p>\r\n                    </div>\r\n\r\n                    {/* Fee Field */}\r\n                    <div>\r\n                      <label htmlFor=\"fee\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Fee\r\n                      </label>\r\n                      <input\r\n                        type=\"number\"\r\n                        name=\"fee\"\r\n                        id=\"fee\"\r\n                        value={formData.fee}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter fee amount (e.g., 5000.00)\"\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Description Field */}\r\n                    <div>\r\n                      <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Description *\r\n                      </label>\r\n                      <textarea\r\n                        name=\"description\"\r\n                        id=\"description\"\r\n                        required\r\n                        rows={3}\r\n                        value={formData.description}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter license category description\"\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Authorizes Field */}\r\n                    <div>\r\n                      <label htmlFor=\"authorizes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                        Authorizes *\r\n                      </label>\r\n                      <textarea\r\n                        name=\"authorizes\"\r\n                        id=\"authorizes\"\r\n                        required\r\n                        rows={3}\r\n                        value={formData.authorizes}\r\n                        onChange={handleChange}\r\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm\"\r\n                        placeholder=\"Enter what this license category authorizes\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Footer */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    {isEdit ? 'Updating...' : 'Creating...'}\r\n                  </>\r\n                ) : (\r\n                  isEdit ? 'Update License Category' : 'Create License Category'\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                disabled={loading}\r\n                className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseCategoryModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,uBAAuB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAA6B;IACnG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,iBAAiB;QACjB,WAAW;QACX,MAAM;QACN,UAAU;QACV,KAAK;QACL,aAAa;QACb,YAAY;IACd;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,IAAI,iBAAiB;gBACnB,YAAY;oBACV,iBAAiB,gBAAgB,eAAe;oBAChD,WAAW,gBAAgB,SAAS,IAAI;oBACxC,MAAM,gBAAgB,IAAI;oBAC1B,UAAU,gBAAgB,QAAQ,IAAG;oBACrC,KAAK,gBAAgB,GAAG,IAAI;oBAC5B,aAAa,gBAAgB,WAAW,IAAI;oBAC5C,YAAY,gBAAgB,UAAU,IAAG;gBAC3C;gBACA,uDAAuD;gBACvD,IAAI,gBAAgB,eAAe,EAAE;oBACnC,qBAAqB,gBAAgB,eAAe,EAAE,gBAAgB,mBAAmB;gBAC3F;YACF,OAAO;gBACL,YAAY;oBACV,iBAAiB;oBACjB,WAAW;oBACX,MAAM;oBACN,UAAU;oBACV,KAAK;oBACL,aAAa;oBACb,YAAY;gBACd;YACF;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAQ;KAAgB;IAE5B,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,eAAe,EAAE;YAC5B,MAAM,YAAY,iBAAiB;YACnC,qBAAqB,SAAS,eAAe,EAAE;QACjD,OAAO;YACL,oBAAoB,EAAE;QACxB;IACF,GAAG;QAAC,SAAS,eAAe;KAAC;IAE7B,MAAM,mBAAmB;QACvB,IAAI;YACF,uBAAuB;YACvB,MAAM,QAAQ,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YACzD,gBAAgB;QAClB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,uBAAuB,OAAO,eAAuB;QACzD,2BAA2B;QAC3B,IAAI;YACF,MAAM,aAAa,MAAM,yIAAA,CAAA,yBAAsB,CAAC,+BAA+B,CAAC,eAAe;YAC/F,uCAAuC;YACvC,MAAM,sBAAsB,MAAM,OAAO,CAAC,cAAc,aAAa,EAAE;YACvE,oBAAoB;QACtB,EAAE,OAAO,KAAU;YACjB,oBAAoB,EAAE,GAAG,2BAA2B;QACtD,SAAU;YACR,2BAA2B;QAC7B;IACF;IAEA,MAAM,eAAe;QACnB,0BAA0B;QAC1B,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK;YAC5F,SAAS;YACT,OAAO;QACT;QAEA,2BAA2B;QAC3B,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;YACpC,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;YAC/B,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,kCAAkC;QAClC,IAAI,CAAC,gBAAgB;YACnB,WAAW;YACX;QACF;QAEA,IAAI;YACF,IAAI,UAAU,iBAAiB;gBAC7B,MAAM,aAAuC;oBAC3C,iBAAiB,SAAS,eAAe;oBACzC,WAAW,SAAS,SAAS,IAAI;oBACjC,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,KAAK,SAAS,GAAG,IAAI;oBACrB,aAAa,SAAS,WAAW;oBACjC,YAAY,SAAS,UAAU;gBACjC;gBACA,MAAM,yIAAA,CAAA,yBAAsB,CAAC,qBAAqB,CAAC,gBAAgB,mBAAmB,EAAE;YAC1F,OAAO;gBACL,MAAM,aAAuC;oBAC3C,iBAAiB,SAAS,eAAe;oBACzC,WAAW,SAAS,SAAS,IAAI;oBACjC,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,KAAK,SAAS,GAAG,IAAI;oBACrB,aAAa,SAAS,WAAW;oBACjC,YAAY,SAAS,UAAU;gBACjC;gBACA,MAAM,yIAAA,CAAA,yBAAsB,CAAC,qBAAqB,CAAC;YACrD;YAEA,OAAO,SAAS,IAAI,EAAE;QACxB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,iCAAiC;QACjC,IAAI,SAAS,YAAY;YACvB,MAAM,eAAe,UAAU,KAAK,IAAI,OAAO;YAC/C,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,0BAA0B;;;;;;4CAGrC,uBACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAuB,SAAQ;gEAAY,MAAK;0EAC7D,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA0N,UAAS;;;;;;;;;;;;;;;;sEAGlQ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;0DAM/D,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAA6D;;;;;;0EAGxG,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,OAAO,SAAS,eAAe;gEAC/B,UAAU;gEACV,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4EAAkC,OAAO,KAAK,eAAe;sFAC3D,KAAK,IAAI;2EADC,KAAK,eAAe;;;;;;;;;;;4DAKpC,qCACC,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;kEAKjE,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAY,WAAU;0EAA6D;;;;;;0EAGlG,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,SAAS;gEACzB,UAAU;gEACV,UAAU,2BAA2B,CAAC,SAAS,eAAe;gEAC9D,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,CAAC;wEACA,QAAQ,GAAG,CAAC,mCAAmC,kBAAkB,WAAW,kBAAkB;wEAC9F,IAAI,CAAC,MAAM,OAAO,CAAC,mBAAmB;4EACpC,QAAQ,GAAG,CAAC,wCAAwC,OAAO;4EAC3D,OAAO;wEACT;wEACA,OAAO,iBAAiB,GAAG,CAAC,CAAC;4EAC3B,QAAQ,GAAG,CAAC,2BAA2B;4EACvC,qBACE,8OAAC;gFAA0C,OAAO,SAAS,mBAAmB;0FAC3E,SAAS,MAAM,GAAG,GAAG,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,IAAI,EAAE,GAAG,SAAS,IAAI;+EADpE,SAAS,mBAAmB;;;;;wEAI7C;oEACF,CAAC;;;;;;;4DAEF,yCACC,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;4DAE9D,CAAC,SAAS,eAAe,kBACxB,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;4DAE9D,SAAS,eAAe,IAAI,CAAC,2BAA2B,CAAC,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,CAAC,mBAC1G,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;kEAKjE,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAA6D;;;;;;0EAG7F,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAMhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAA6D;;;;;;0EAGjG,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAW,CAAC,sKAAsK,EAChL,SAAS,QAAQ,IAAI,KAAK,SAAS,QAAQ,IAAI,OAAO,OAAO,SAAS,CAAC,SAAS,QAAQ,IACpF,iFACA,8EACJ;gEACF,aAAY;;;;;;0EAEd,8OAAC;gEAAE,WAAW,CAAC,aAAa,EAC1B,SAAS,QAAQ,IAAI,KAAK,SAAS,QAAQ,IAAI,OAAO,OAAO,SAAS,CAAC,SAAS,QAAQ,IACpF,qCACA,kCACJ;0EACC,SAAS,QAAQ,IAAI,KAAK,SAAS,QAAQ,IAAI,OAAO,OAAO,SAAS,CAAC,SAAS,QAAQ,IACrF,2CACA;;;;;;;;;;;;kEAMR,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAM,WAAU;0EAA6D;;;;;;0EAG5F,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,GAAG;gEACnB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAc,WAAU;0EAA6D;;;;;;0EAGpG,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,MAAM;gEACN,OAAO,SAAS,WAAW;gEAC3B,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAa,WAAU;0EAA6D;;;;;;0EAGnG,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,QAAQ;gEACR,MAAM;gEACN,OAAO,SAAS,UAAU;gEAC1B,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,MAAK;oDAAO,SAAQ;;sEAC9E,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAEpD,SAAS,gBAAgB;;2DAG5B,SAAS,4BAA4B;;;;;;kDAGzC,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 3837, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,uGAAuG;IACvG,MAAM,iBAAiB,CAAC,4OAA4O,EAClQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 3912, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/IdentificationTypeModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { IdentificationType, identificationTypeService, CreateIdentificationTypeDto, UpdateIdentificationTypeDto } from '../../services/identificationTypeService';\r\nimport TextInput from '../forms/TextInput';\r\n\r\ninterface IdentificationTypeModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (identificationTypeName: string, isEdit?: boolean) => void;\r\n  identificationType?: IdentificationType | null;\r\n}\r\n\r\nconst IdentificationTypeModal = ({ isOpen, onClose, onSave, identificationType }: IdentificationTypeModalProps) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const isEdit = !!identificationType;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      if (identificationType) {\r\n        setFormData({\r\n          name: identificationType.name,\r\n        });\r\n      } else {\r\n        setFormData({\r\n          name: '',\r\n        });\r\n      }\r\n      setError('');\r\n    }\r\n  }, [isOpen, identificationType]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      if (isEdit && identificationType) {\r\n        const updateData: UpdateIdentificationTypeDto = {\r\n          name: formData.name,\r\n        };\r\n        await identificationTypeService.updateIdentificationType(identificationType.identification_type_id, updateData);\r\n      } else {\r\n        const createData: CreateIdentificationTypeDto = {\r\n          name: formData.name,\r\n        };\r\n        await identificationTypeService.createIdentificationType(createData);\r\n      }\r\n\r\n      onSave(formData.name, isEdit);\r\n    } catch (err: any) {\r\n      console.error('Error saving identification type:', err);\r\n      setError(err.response?.data?.message || 'Failed to save identification type');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n              <div className=\"sm:flex sm:items-start\">\r\n                <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\r\n                  <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\r\n                    {isEdit ? 'Edit Identification Type' : 'Add New Identification Type'}\r\n                  </h3>\r\n\r\n                  {error && (\r\n                    <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3\">\r\n                      <div className=\"flex\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div className=\"ml-3\">\r\n                          <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"space-y-4\">\r\n                    {/* Name Field */}\r\n                    <div>\r\n                      <TextInput\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        label=\"Identification Type Name\"\r\n                        required\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        placeholder=\"Enter identification type name (e.g., National ID, Passport, Driver's License)\"\r\n                      />\r\n                      <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\r\n                        Enter the name of the identification document type\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Footer */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    {isEdit ? 'Updating...' : 'Creating...'}\r\n                  </>\r\n                ) : (\r\n                  isEdit ? 'Update Identification Type' : 'Create Identification Type'\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                disabled={loading}\r\n                className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IdentificationTypeModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,0BAA0B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAAgC;IAC5G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;IACR;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,IAAI,oBAAoB;gBACtB,YAAY;oBACV,MAAM,mBAAmB,IAAI;gBAC/B;YACF,OAAO;gBACL,YAAY;oBACV,MAAM;gBACR;YACF;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAQ;KAAmB;IAE/B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU,oBAAoB;gBAChC,MAAM,aAA0C;oBAC9C,MAAM,SAAS,IAAI;gBACrB;gBACA,MAAM,4IAAA,CAAA,4BAAyB,CAAC,wBAAwB,CAAC,mBAAmB,sBAAsB,EAAE;YACtG,OAAO;gBACL,MAAM,aAA0C;oBAC9C,MAAM,SAAS,IAAI;gBACrB;gBACA,MAAM,4IAAA,CAAA,4BAAyB,CAAC,wBAAwB,CAAC;YAC3D;YAEA,OAAO,SAAS,IAAI,EAAE;QACxB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,6BAA6B;;;;;;4CAGxC,uBACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAuB,SAAQ;gEAAY,MAAK;0EAC7D,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA0N,UAAS;;;;;;;;;;;;;;;;sEAGlQ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;0DAM/D,8OAAC;gDAAI,WAAU;0DAEb,cAAA,8OAAC;;sEACC,8OAAC,wIAAA,CAAA,UAAS;4DACR,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DAAE,WAAU;sEAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,MAAK;oDAAO,SAAQ;;sEAC9E,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAEpD,SAAS,gBAAgB;;2DAG5B,SAAS,+BAA+B;;;;;;kDAG5C,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 4215, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,6LAA6L,EACpN,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;oEAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/settings/LicenseCategoryDocumentModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport TextInput from '../forms/TextInput';\r\nimport Select from '../forms/Select';\r\nimport { LicenseCategoryDocument, licenseCategoryDocumentService, CreateLicenseCategoryDocumentDto, UpdateLicenseCategoryDocumentDto } from '../../services/licenseCategoryDocumentService';\r\nimport { LicenseCategory, licenseCategoryService } from '../../services/licenseCategoryService';\r\n\r\ninterface LicenseCategoryDocumentModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onSave: (documentName: string, isEdit?: boolean) => void;\r\n  document?: LicenseCategoryDocument | null;\r\n}\r\n\r\nconst LicenseCategoryDocumentModal = ({ isOpen, onClose, onSave, document }: LicenseCategoryDocumentModalProps) => {\r\n  const [formData, setFormData] = useState({\r\n    license_category_id: '',\r\n    name: '',\r\n    is_required: true,\r\n  });\r\n  const [licenseCategories, setLicenseCategories] = useState<LicenseCategory[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [loadingCategories, setLoadingCategories] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const isEdit = !!document;\r\n\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      loadLicenseCategories();\r\n      if (document) {\r\n        setFormData({\r\n          license_category_id: document.license_category_id,\r\n          name: document.name,\r\n          is_required: document.is_required,\r\n        });\r\n      } else {\r\n        setFormData({\r\n          license_category_id: '',\r\n          name: '',\r\n          is_required: true,\r\n        });\r\n      }\r\n      setError('');\r\n    }\r\n  }, [isOpen, document]);\r\n\r\n  const loadLicenseCategories = async () => {\r\n    try {\r\n      setLoadingCategories(true);\r\n      const categories = await licenseCategoryService.getAllLicenseCategories();\r\n      setLicenseCategories(categories);\r\n    } catch (err: unknown) {\r\n      console.error('Error loading license categories:', err);\r\n      setError('Failed to load license categories');\r\n    } finally {\r\n      setLoadingCategories(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      if (isEdit && document) {\r\n        const updateData: UpdateLicenseCategoryDocumentDto = {\r\n          license_category_id: formData.license_category_id,\r\n          name: formData.name,\r\n          is_required: formData.is_required,\r\n        };\r\n        await licenseCategoryDocumentService.updateLicenseCategoryDocument(document.license_category_document_id, updateData);\r\n      } else {\r\n        const createData: CreateLicenseCategoryDocumentDto = {\r\n          license_category_id: formData.license_category_id,\r\n          name: formData.name,\r\n          is_required: formData.is_required,\r\n        };\r\n        await licenseCategoryDocumentService.createLicenseCategoryDocument(createData);\r\n      }\r\n\r\n      onSave(formData.name, isEdit);\r\n    } catch (err: unknown) {\r\n      console.error('Error saving license category document:', err);\r\n      const errorMessage = err instanceof Error && 'response' in err &&\r\n        typeof err.response === 'object' && err.response !== null &&\r\n        'data' in err.response && typeof err.response.data === 'object' && err.response.data !== null &&\r\n        'message' in err.response.data && typeof err.response.data.message === 'string'\r\n        ? err.response.data.message\r\n        : 'Failed to save document requirement';\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n    const { name, value, type } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,\r\n    }));\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <form onSubmit={handleSubmit}>\r\n            <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n              <div className=\"sm:flex sm:items-start\">\r\n                <div className=\"mt-3 text-center sm:mt-0 sm:text-left w-full\">\r\n                  <h3 className=\"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4\">\r\n                    {isEdit ? 'Edit Document Requirement' : 'Add New Document Requirement'}\r\n                  </h3>\r\n\r\n                  {error && (\r\n                    <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3\">\r\n                      <div className=\"flex\">\r\n                        <div className=\"flex-shrink-0\">\r\n                          <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </div>\r\n                        <div className=\"ml-3\">\r\n                          <p className=\"text-sm text-red-800 dark:text-red-200\">{error}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"space-y-4\">\r\n                    {/* License Category Field */}\r\n                    <div>\r\n                      <Select\r\n                        name=\"license_category_id\"\r\n                        label=\"License Category\"\r\n                        required\r\n                        value={formData.license_category_id}\r\n                        onChange={handleChange}\r\n                        disabled={loadingCategories}\r\n                      >\r\n                        <option value=\"\">Select a license category</option>\r\n                        {licenseCategories.map((category) => (\r\n                          <option key={category.license_category_id} value={category.license_category_id}>\r\n                            {category.name}\r\n                          </option>\r\n                        ))}\r\n                      </Select>\r\n                      {loadingCategories && (\r\n                        <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">Loading license categories...</p>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Document Name Field */}\r\n                    <TextInput\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      label=\"Document Name\"\r\n                      required\r\n                      value={formData.name}\r\n                      onChange={handleChange}\r\n                      placeholder=\"Enter document name (e.g., Certificate of Incorporation)\"\r\n                    />\r\n\r\n                    {/* Required Field */}\r\n                    <div>\r\n                      <div className=\"flex items-center\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          name=\"is_required\"\r\n                          id=\"is_required\"\r\n                          checked={formData.is_required}\r\n                          onChange={handleChange}\r\n                          className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700\"\r\n                        />\r\n                        <label htmlFor=\"is_required\" className=\"ml-2 block text-sm text-gray-700 dark:text-gray-300\">\r\n                          This document is required for license applications\r\n                        </label>\r\n                      </div>\r\n                      <p className=\"mt-1 text-xs text-gray-500 dark:text-gray-400\">\r\n                        Uncheck if this document is optional for applicants\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modal Footer */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    {isEdit ? 'Updating...' : 'Creating...'}\r\n                  </>\r\n                ) : (\r\n                  isEdit ? 'Update Document Requirement' : 'Create Document Requirement'\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                disabled={loading}\r\n                className=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LicenseCategoryDocumentModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAeA,MAAM,+BAA+B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAqC;IAC5G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,qBAAqB;QACrB,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,SAAS,CAAC,CAAC;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA,IAAI,UAAU;gBACZ,YAAY;oBACV,qBAAqB,SAAS,mBAAmB;oBACjD,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;gBACnC;YACF,OAAO;gBACL,YAAY;oBACV,qBAAqB;oBACrB,MAAM;oBACN,aAAa;gBACf;YACF;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,wBAAwB;QAC5B,IAAI;YACF,qBAAqB;YACrB,MAAM,aAAa,MAAM,yIAAA,CAAA,yBAAsB,CAAC,uBAAuB;YACvE,qBAAqB;QACvB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS;QACX,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,UAAU,UAAU;gBACtB,MAAM,aAA+C;oBACnD,qBAAqB,SAAS,mBAAmB;oBACjD,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;gBACnC;gBACA,MAAM,iJAAA,CAAA,iCAA8B,CAAC,6BAA6B,CAAC,SAAS,4BAA4B,EAAE;YAC5G,OAAO;gBACL,MAAM,aAA+C;oBACnD,qBAAqB,SAAS,mBAAmB;oBACjD,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;gBACnC;gBACA,MAAM,iJAAA,CAAA,iCAA8B,CAAC,6BAA6B,CAAC;YACrE;YAEA,OAAO,SAAS,IAAI,EAAE;QACxB,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,eAAe,eAAe,SAAS,cAAc,OACzD,OAAO,IAAI,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,QACrD,UAAU,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,QAAQ,CAAC,IAAI,KAAK,QACzF,aAAa,IAAI,QAAQ,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,WACrE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,GACzB;YACJ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YACzE,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,8BAA8B;;;;;;4CAGzC,uBACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAuB,SAAQ;gEAAY,MAAK;0EAC7D,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAA0N,UAAS;;;;;;;;;;;;;;;;sEAGlQ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;0DAM/D,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC,qIAAA,CAAA,UAAM;gEACL,MAAK;gEACL,OAAM;gEACN,QAAQ;gEACR,OAAO,SAAS,mBAAmB;gEACnC,UAAU;gEACV,UAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;4EAA0C,OAAO,SAAS,mBAAmB;sFAC3E,SAAS,IAAI;2EADH,SAAS,mBAAmB;;;;;;;;;;;4DAK5C,mCACC,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;kEAKjE,8OAAC,wIAAA,CAAA,UAAS;wDACR,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,QAAQ;wDACR,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,aAAY;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,IAAG;wEACH,SAAS,SAAS,WAAW;wEAC7B,UAAU;wEACV,WAAU;;;;;;kFAEZ,8OAAC;wEAAM,SAAQ;wEAAc,WAAU;kFAAsD;;;;;;;;;;;;0EAI/F,8OAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,MAAK;oDAAO,SAAQ;;sEAC9E,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAEpD,SAAS,gBAAgB;;2DAG5B,SAAS,gCAAgC;;;;;;kDAG7C,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 4709, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { LicenseType } from '../../services/licenseTypeService';\r\nimport { LicenseCategory } from '../../services/licenseCategoryService';\r\nimport { IdentificationType } from '../../services/identificationTypeService';\r\nimport { LicenseCategoryDocument } from '../../services/licenseCategoryDocumentService';\r\nimport SettingsTabs from '../../components/settings/SettingsTabs';\r\nimport LicenseTypesTab from '../../components/settings/LicenseTypesTab';\r\nimport LicenseCategoriesTab from '../../components/settings/LicenseCategoriesTab';\r\nimport IdentificationTypesTab from '../../components/settings/IdentificationTypesTab';\r\nimport LicenseCategoryDocumentsTab from '../../components/settings/LicenseCategoryDocumentsTab';\r\nimport LicenseTypeModal from '../../components/settings/LicenseTypeModal';\r\nimport LicenseCategoryModal from '../../components/settings/LicenseCategoryModal';\r\nimport IdentificationTypeModal from '../../components/settings/IdentificationTypeModal';\r\nimport LicenseCategoryDocumentModal from '../../components/settings/LicenseCategoryDocumentModal';\r\nimport ClientSystemModal from '../../components/settings/ClientSystemModal';\r\n\r\nexport default function SettingsPage() {\r\n  const [activeTab, setActiveTab] = useState('license-types');\r\n  const [successMessage, setSuccessMessage] = useState<string>('');\r\n  const [errorMessage, setErrorMessage] = useState<string>('');\r\n\r\n  // License Types Modal State\r\n  const [isLicenseTypeModalOpen, setIsLicenseTypeModalOpen] = useState(false);\r\n  const [editingLicenseType, setEditingLicenseType] = useState<LicenseType | null>(null);\r\n\r\n  // License Categories Modal State\r\n  const [isLicenseCategoryModalOpen, setIsLicenseCategoryModalOpen] = useState(false);\r\n  const [editingLicenseCategory, setEditingLicenseCategory] = useState<LicenseCategory | null>(null);\r\n\r\n  // Identification Types Modal State\r\n  const [isIdentificationTypeModalOpen, setIsIdentificationTypeModalOpen] = useState(false);\r\n  const [editingIdentificationType, setEditingIdentificationType] = useState<IdentificationType | null>(null);\r\n\r\n  // License Category Documents Modal State\r\n  const [isLicenseCategoryDocumentModalOpen, setIsLicenseCategoryDocumentModalOpen] = useState(false);\r\n  const [editingLicenseCategoryDocument, setEditingLicenseCategoryDocument] = useState<LicenseCategoryDocument | null>(null);\r\n\r\n\r\n  const handleTabChange = (tabId: string) => {\r\n    setActiveTab(tabId);\r\n  };\r\n\r\n  // Refresh functions for each tab\r\n  const [refreshLicenseTypes, setRefreshLicenseTypes] = useState(0);\r\n  const [refreshLicenseCategories, setRefreshLicenseCategories] = useState(0);\r\n  const [refreshIdentificationTypes, setRefreshIdentificationTypes] = useState(0);\r\n  const [refreshLicenseCategoryDocuments, setRefreshLicenseCategoryDocuments] = useState(0);\r\n  const [refreshClientSystems, setRefreshClientSystems] = useState(0);\r\n\r\n  // License Type Handlers\r\n  const handleCreateLicenseType = () => {\r\n    setEditingLicenseType(null);\r\n    setIsLicenseTypeModalOpen(true);\r\n  };\r\n\r\n  const handleEditLicenseType = (licenseType: LicenseType) => {\r\n    setEditingLicenseType(licenseType);\r\n    setIsLicenseTypeModalOpen(true);\r\n  };\r\n\r\n  const handleLicenseTypeModalClose = () => {\r\n    setIsLicenseTypeModalOpen(false);\r\n    setEditingLicenseType(null);\r\n  };\r\n\r\n  const handleLicenseTypeSaved = (licenseTypeName: string, isEdit: boolean = false) => {\r\n    handleLicenseTypeModalClose();\r\n    const action = isEdit ? 'updated' : 'created';\r\n    setSuccessMessage(`License type \"${licenseTypeName}\" has been ${action} successfully!`);\r\n    setErrorMessage('');\r\n    setTimeout(() => setSuccessMessage(''), 5000);\r\n    // Trigger refresh of license types table\r\n    setRefreshLicenseTypes(prev => prev + 1);\r\n  };\r\n\r\n  // License Category Handlers\r\n  const handleCreateLicenseCategory = () => {\r\n    setEditingLicenseCategory(null);\r\n    setIsLicenseCategoryModalOpen(true);\r\n  };\r\n\r\n  const handleEditLicenseCategory = (licenseCategory: LicenseCategory) => {\r\n    setEditingLicenseCategory(licenseCategory);\r\n    setIsLicenseCategoryModalOpen(true);\r\n  };\r\n\r\n  const handleLicenseCategoryModalClose = () => {\r\n    setIsLicenseCategoryModalOpen(false);\r\n    setEditingLicenseCategory(null);\r\n  };\r\n\r\n  const handleLicenseCategorySaved = (licenseCategoryName: string, isEdit: boolean = false) => {\r\n    handleLicenseCategoryModalClose();\r\n    const action = isEdit ? 'updated' : 'created';\r\n    setSuccessMessage(`License category \"${licenseCategoryName}\" has been ${action} successfully!`);\r\n    setErrorMessage('');\r\n    setTimeout(() => setSuccessMessage(''), 5000);\r\n    // Trigger refresh of license categories table\r\n    setRefreshLicenseCategories(prev => prev + 1);\r\n  };\r\n\r\n  // Identification Type Handlers\r\n  const handleCreateIdentificationType = () => {\r\n    setEditingIdentificationType(null);\r\n    setIsIdentificationTypeModalOpen(true);\r\n  };\r\n\r\n  const handleEditIdentificationType = (identificationType: IdentificationType) => {\r\n    setEditingIdentificationType(identificationType);\r\n    setIsIdentificationTypeModalOpen(true);\r\n  };\r\n\r\n  const handleIdentificationTypeModalClose = () => {\r\n    setIsIdentificationTypeModalOpen(false);\r\n    setEditingIdentificationType(null);\r\n  };\r\n\r\n  const handleIdentificationTypeSaved = (identificationTypeName: string, isEdit: boolean = false) => {\r\n    handleIdentificationTypeModalClose();\r\n    const action = isEdit ? 'updated' : 'created';\r\n    setSuccessMessage(`Identification type \"${identificationTypeName}\" has been ${action} successfully!`);\r\n    setErrorMessage('');\r\n    setTimeout(() => setSuccessMessage(''), 5000);\r\n    // Trigger refresh of identification types table\r\n    setRefreshIdentificationTypes(prev => prev + 1);\r\n  };\r\n\r\n  // License Category Document Handlers\r\n  const handleCreateLicenseCategoryDocument = () => {\r\n    setEditingLicenseCategoryDocument(null);\r\n    setIsLicenseCategoryDocumentModalOpen(true);\r\n  };\r\n\r\n  const handleEditLicenseCategoryDocument = (document: LicenseCategoryDocument) => {\r\n    setEditingLicenseCategoryDocument(document);\r\n    setIsLicenseCategoryDocumentModalOpen(true);\r\n  };\r\n\r\n  const handleLicenseCategoryDocumentModalClose = () => {\r\n    setIsLicenseCategoryDocumentModalOpen(false);\r\n    setEditingLicenseCategoryDocument(null);\r\n  };\r\n\r\n  const handleLicenseCategoryDocumentSaved = (documentName: string, isEdit: boolean = false) => {\r\n    handleLicenseCategoryDocumentModalClose();\r\n    const action = isEdit ? 'updated' : 'created';\r\n    setSuccessMessage(`Document requirement \"${documentName}\" has been ${action} successfully!`);\r\n    setErrorMessage('');\r\n    setTimeout(() => setSuccessMessage(''), 5000);\r\n    // Trigger refresh of license category documents table\r\n    setRefreshLicenseCategoryDocuments(prev => prev + 1);\r\n  };\r\n\r\n  // Define tabs for the tab system\r\n  const tabs = [\r\n    {\r\n      id: 'license-types',\r\n      label: 'License Types',\r\n      content: (\r\n        <LicenseTypesTab\r\n          onEditLicenseType={handleEditLicenseType}\r\n          onCreateLicenseType={handleCreateLicenseType}\r\n          refreshTrigger={refreshLicenseTypes}\r\n        />\r\n      )\r\n    },\r\n    {\r\n      id: 'license-categories',\r\n      label: 'License Categories',\r\n      content: (\r\n        <LicenseCategoriesTab\r\n          onEditLicenseCategory={handleEditLicenseCategory}\r\n          onCreateLicenseCategory={handleCreateLicenseCategory}\r\n          refreshTrigger={refreshLicenseCategories}\r\n        />\r\n      )\r\n    },\r\n\r\n    {\r\n      id: 'license-category-documents',\r\n      label: 'Document Requirements',\r\n      content: (\r\n        <LicenseCategoryDocumentsTab\r\n          onEditLicenseCategoryDocument={handleEditLicenseCategoryDocument}\r\n          onCreateLicenseCategoryDocument={handleCreateLicenseCategoryDocument}\r\n          refreshTrigger={refreshLicenseCategoryDocuments}\r\n        />\r\n      )\r\n    },\r\n    {\r\n      id: 'identification-types',\r\n      label: 'Identification Types',\r\n      content: (\r\n        <IdentificationTypesTab\r\n          onEditIdentificationType={handleEditIdentificationType}\r\n          onCreateIdentificationType={handleCreateIdentificationType}\r\n          refreshTrigger={refreshIdentificationTypes}\r\n        />\r\n      )\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page Header */}\r\n        <div className=\"mb-6\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">Settings</h1>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Manage license types, categories, and identification types\r\n          </p>\r\n        </div>\r\n\r\n        {/* Success Message */}\r\n        {successMessage && (\r\n          <div className=\"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300\">\r\n            <div className=\"flex items-start\">\r\n              <div className=\"flex-shrink-0\">\r\n                <div className=\"h-5 w-5 text-green-400 dark:text-green-500\">\r\n                  <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"ml-3 flex-1\">\r\n                <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200 mb-1\">\r\n                  Success\r\n                </h3>\r\n                <p className=\"text-sm text-green-700 dark:text-green-300\">\r\n                  {successMessage}\r\n                </p>\r\n              </div>\r\n              <div className=\"ml-4 flex-shrink-0\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setSuccessMessage('')}\r\n                  className=\"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200\"\r\n                  aria-label=\"Dismiss success message\"\r\n                >\r\n                  <svg className=\"h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error Message */}\r\n        {errorMessage && (\r\n          <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300\">\r\n            <div className=\"flex items-start\">\r\n              <div className=\"flex-shrink-0\">\r\n                <div className=\"h-5 w-5 text-red-400 dark:text-red-500\">\r\n                  <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"ml-3 flex-1\">\r\n                <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\r\n                  Error\r\n                </h3>\r\n                <p className=\"text-sm text-red-700 dark:text-red-300\">\r\n                  {errorMessage}\r\n                </p>\r\n              </div>\r\n              <div className=\"ml-4 flex-shrink-0\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setErrorMessage('')}\r\n                  className=\"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200\"\r\n                  aria-label=\"Dismiss error message\"\r\n                >\r\n                  <svg className=\"h-4 w-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        <SettingsTabs\r\n          tabs={tabs}\r\n          activeTab={activeTab}\r\n          onTabChange={handleTabChange}\r\n        />\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      <LicenseTypeModal\r\n        isOpen={isLicenseTypeModalOpen}\r\n        onClose={handleLicenseTypeModalClose}\r\n        onSave={handleLicenseTypeSaved}\r\n        licenseType={editingLicenseType}\r\n      />\r\n\r\n      <LicenseCategoryModal\r\n        isOpen={isLicenseCategoryModalOpen}\r\n        onClose={handleLicenseCategoryModalClose}\r\n        onSave={handleLicenseCategorySaved}\r\n        licenseCategory={editingLicenseCategory}\r\n      />\r\n\r\n      <IdentificationTypeModal\r\n        isOpen={isIdentificationTypeModalOpen}\r\n        onClose={handleIdentificationTypeModalClose}\r\n        onSave={handleIdentificationTypeSaved}\r\n        identificationType={editingIdentificationType}\r\n      />\r\n\r\n      <LicenseCategoryDocumentModal\r\n        isOpen={isLicenseCategoryDocumentModalOpen}\r\n        onClose={handleLicenseCategoryDocumentModalClose}\r\n        onSave={handleLicenseCategoryDocumentSaved}\r\n        document={editingLicenseCategoryDocument}\r\n      />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,4BAA4B;IAC5B,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,iCAAiC;IACjC,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAE7F,mCAAmC;IACnC,MAAM,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnF,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAEtG,yCAAyC;IACzC,MAAM,CAAC,oCAAoC,sCAAsC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7F,MAAM,CAAC,gCAAgC,kCAAkC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IAGrH,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,iCAAiC;IACjC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,iCAAiC,mCAAmC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,wBAAwB;IACxB,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,0BAA0B;IAC5B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,sBAAsB;QACtB,0BAA0B;IAC5B;IAEA,MAAM,8BAA8B;QAClC,0BAA0B;QAC1B,sBAAsB;IACxB;IAEA,MAAM,yBAAyB,CAAC,iBAAyB,SAAkB,KAAK;QAC9E;QACA,MAAM,SAAS,SAAS,YAAY;QACpC,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,WAAW,EAAE,OAAO,cAAc,CAAC;QACtF,gBAAgB;QAChB,WAAW,IAAM,kBAAkB,KAAK;QACxC,yCAAyC;QACzC,uBAAuB,CAAA,OAAQ,OAAO;IACxC;IAEA,4BAA4B;IAC5B,MAAM,8BAA8B;QAClC,0BAA0B;QAC1B,8BAA8B;IAChC;IAEA,MAAM,4BAA4B,CAAC;QACjC,0BAA0B;QAC1B,8BAA8B;IAChC;IAEA,MAAM,kCAAkC;QACtC,8BAA8B;QAC9B,0BAA0B;IAC5B;IAEA,MAAM,6BAA6B,CAAC,qBAA6B,SAAkB,KAAK;QACtF;QACA,MAAM,SAAS,SAAS,YAAY;QACpC,kBAAkB,CAAC,kBAAkB,EAAE,oBAAoB,WAAW,EAAE,OAAO,cAAc,CAAC;QAC9F,gBAAgB;QAChB,WAAW,IAAM,kBAAkB,KAAK;QACxC,8CAA8C;QAC9C,4BAA4B,CAAA,OAAQ,OAAO;IAC7C;IAEA,+BAA+B;IAC/B,MAAM,iCAAiC;QACrC,6BAA6B;QAC7B,iCAAiC;IACnC;IAEA,MAAM,+BAA+B,CAAC;QACpC,6BAA6B;QAC7B,iCAAiC;IACnC;IAEA,MAAM,qCAAqC;QACzC,iCAAiC;QACjC,6BAA6B;IAC/B;IAEA,MAAM,gCAAgC,CAAC,wBAAgC,SAAkB,KAAK;QAC5F;QACA,MAAM,SAAS,SAAS,YAAY;QACpC,kBAAkB,CAAC,qBAAqB,EAAE,uBAAuB,WAAW,EAAE,OAAO,cAAc,CAAC;QACpG,gBAAgB;QAChB,WAAW,IAAM,kBAAkB,KAAK;QACxC,gDAAgD;QAChD,8BAA8B,CAAA,OAAQ,OAAO;IAC/C;IAEA,qCAAqC;IACrC,MAAM,sCAAsC;QAC1C,kCAAkC;QAClC,sCAAsC;IACxC;IAEA,MAAM,oCAAoC,CAAC;QACzC,kCAAkC;QAClC,sCAAsC;IACxC;IAEA,MAAM,0CAA0C;QAC9C,sCAAsC;QACtC,kCAAkC;IACpC;IAEA,MAAM,qCAAqC,CAAC,cAAsB,SAAkB,KAAK;QACvF;QACA,MAAM,SAAS,SAAS,YAAY;QACpC,kBAAkB,CAAC,sBAAsB,EAAE,aAAa,WAAW,EAAE,OAAO,cAAc,CAAC;QAC3F,gBAAgB;QAChB,WAAW,IAAM,kBAAkB,KAAK;QACxC,sDAAsD;QACtD,mCAAmC,CAAA,OAAQ,OAAO;IACpD;IAEA,iCAAiC;IACjC,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,uBACE,8OAAC,iJAAA,CAAA,UAAe;gBACd,mBAAmB;gBACnB,qBAAqB;gBACrB,gBAAgB;;;;;;QAGtB;QACA;YACE,IAAI;YACJ,OAAO;YACP,uBACE,8OAAC,sJAAA,CAAA,UAAoB;gBACnB,uBAAuB;gBACvB,yBAAyB;gBACzB,gBAAgB;;;;;;QAGtB;QAEA;YACE,IAAI;YACJ,OAAO;YACP,uBACE,8OAAC,6JAAA,CAAA,UAA2B;gBAC1B,+BAA+B;gBAC/B,iCAAiC;gBACjC,gBAAgB;;;;;;QAGtB;QACA;YACE,IAAI;YACJ,OAAO;YACP,uBACE,8OAAC,wJAAA,CAAA,UAAsB;gBACrB,0BAA0B;gBAC1B,4BAA4B;gBAC5B,gBAAgB;;;;;;QAGtB;KACD;IAED,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CACjE,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;oBAMtD,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,MAAK;4CAAe,SAAQ;sDAC/B,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAwI,UAAS;;;;;;;;;;;;;;;;;;;;;8CAIlL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;8CAGL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASpP,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,MAAK;4CAAe,SAAQ;sDAC/B,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;;;;;;8CAIpQ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0D;;;;;;sDAGxE,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;8CAGL,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQrP,8OAAC,8IAAA,CAAA,UAAY;wBACX,MAAM;wBACN,WAAW;wBACX,aAAa;;;;;;;;;;;;0BAKjB,8OAAC,kJAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;;;;;;0BAGf,8OAAC,sJAAA,CAAA,UAAoB;gBACnB,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,iBAAiB;;;;;;0BAGnB,8OAAC,yJAAA,CAAA,UAAuB;gBACtB,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,oBAAoB;;;;;;0BAGtB,8OAAC,8JAAA,CAAA,UAA4B;gBAC3B,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}