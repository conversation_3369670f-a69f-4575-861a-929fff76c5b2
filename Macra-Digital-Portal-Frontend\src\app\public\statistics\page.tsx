import { Metadata } from 'next';
import StatisticsClient from './StatisticsClient';

export const metadata: Metadata = {
  title: 'License Statistics',
  description: 'View public statistics about MACRA licenses including active, expired, and suspended licenses. Transparent data about the regulatory landscape.',
  keywords: [
    'MACRA statistics',
    'license statistics',
    'regulatory data',
    'telecommunications statistics',
    'broadcasting statistics',
    'license transparency'
  ],
  openGraph: {
    title: 'MACRA License Statistics',
    description: 'View public statistics about MACRA licenses including active, expired, and suspended licenses.',
    url: 'https://portal.macra.mw/public/statistics',
  },
  twitter: {
    title: 'MACRA License Statistics',
    description: 'View public statistics about MACRA licenses including active, expired, and suspended licenses.',
  },
};

export default function StatisticsPage() {
  return <StatisticsClient />;
}


