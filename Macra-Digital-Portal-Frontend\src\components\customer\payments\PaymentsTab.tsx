'use client';

import { useState, useEffect, useCallback } from 'react';
import { paymentService } from '../../../services/paymentService';
import DataTable from '../../common/DataTable';
import Select from '../../common/Select';
import { Payment, PaymentFilters } from '@/types/invoice';
import { PaginatedResponse, PaginateQuery } from '@/types';
import { formatAmount, formatDate } from '@/utils/formatters';
import { useAuth } from '@/contexts/AuthContext';

interface PaymentsTabProps {
  onViewPayment?: (payment: any) => void;
  onUploadProof?: (payment: any) => void;
}

const PaymentsTab = ({ onViewPayment, onUploadProof }: PaymentsTabProps) => {
  const [paymentsData, setPaymentsData] = useState<PaginatedResponse<Payment> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });
  const [selectedPayment, setSelectedPayment] = useState<any>(null);
  const [showProofModal, setShowProofModal] = useState(false);
  const {user} = useAuth();

  const loadPayments = useCallback(async (query: PaginateQuery & PaymentFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await paymentService.getPayments(query, user?.isCustomer);
      setPaymentsData(response);
    } catch (err) {
      console.error('Error loading payments:', err);
      setError('Failed to load payments. Please try again.');
      setPaymentsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPayments({ ...currentQuery, ...filters });
  }, [loadPayments, currentQuery, filters]);

  const handleViewProofOfPayment = (payment: any) => {
    setSelectedPayment(payment);
    setShowProofModal(true);
  };

  const handleQueryChange = (query: PaginateQuery) => {
    setCurrentQuery(query);
  };

  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'OVERDUE': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'CANCELLED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'LICENSE_FEE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PROCUREMENT_FEE': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'APPLICATION_FEE': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'RENEWAL_FEE': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';
      case 'PENALTY_FEE': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const paymentColumns = [

    {
      key: 'payment_type',
      label: 'Type',
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(String(value))}`}>
          {String(value).replace('_', ' ')}
        </span>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: unknown, item: any) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          <div className="max-w-xs truncate font-medium" title={String(value)}>
            {String(value)}
          </div>
          {item.entity_type && item.entity_id && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {item.entity_type === 'application' ? '📋 Application' : '📄 ' + item.entity_type}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'issue_date',
      label: 'Issue Date',
      sortable: true,
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
            { value ? formatDate(String(value)) : 'Not specified' }
        </div>
      ),
    },
    {
      key: 'paid_date',
      label: 'Payment Date',
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value ? formatDate(String(value)) : 'Not paid'}
        </div>
      ),
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value: unknown, item: any) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {formatAmount(Number(value), item.currency)}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: unknown) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(String(value))}`}>
          {String(value)}
        </span>
      ),
    },
    {
      key: 'payment_method',
      label: 'Payment Method',
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value ? String(value).replace('_', ' ') : 'Not specified'}
        </div>
      ),
    },
    {
      key: 'transaction_reference',
      label: 'Reference',
      render: (value: unknown) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value ? String(value) : 'N/A'}
        </div>
      ),
    },
    {
      key: 'documents',
      label: 'Proof of Payment',
      render: (value: unknown, item: any) => {
        const documents = item.documents || [];
        const proofOfPaymentDocs = documents.filter((doc: any) =>
          doc.document_type === 'proof_of_payment' ||
          doc.document_type === 'PROOF_OF_PAYMENT'
        );

        if (proofOfPaymentDocs.length === 0) {
          return (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              No proof uploaded
            </div>
          );
        }

        return (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleViewProofOfPayment(item)}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              <i className="ri-file-text-line mr-1"></i>
              View ({proofOfPaymentDocs.length})
            </button>
          </div>
        );
      },
    }
  ];

  const paymentTypes = [
    { value: '', label: 'All Types' },
    { value: 'LICENSE_FEE', label: 'License Fee' },
    { value: 'PROCUREMENT_FEE', label: 'Procurement Fee' },
    { value: 'APPLICATION_FEE', label: 'Application Fee' },
    { value: 'RENEWAL_FEE', label: 'Renewal Fee' },
    { value: 'PENALTY_FEE', label: 'Penalty Fee' },
  ];

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  const dateRangeOptions = [
    { value: '', label: 'All Time' },
    { value: 'last-30', label: 'Last 30 Days' },
    { value: 'last-90', label: 'Last 90 Days' },
    { value: 'last-year', label: 'Last Year' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Payment History</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            View all your payment records and transaction history
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {/* Total: {paymentsData?.meta.totalItems || 0} payment{(paymentsData?.meta.totalItems || 0) !== 1 ? 's' : ''} */}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <Select
            label="Status"
            value={filters.status || 'pending'}
            onChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
          />
          <Select
            label="Payment Type"
            value={filters.payment_type || ''}
            onChange={(value) => handleFilterChange('payment_type', value)}
            options={paymentTypes}
          />
          <Select
            label="Date Range"
            value={filters.dateRange || ''}
            onChange={(value) => handleFilterChange('dateRange', value)}
            options={dateRangeOptions}
          />
          <div className="flex items-end">
            <button
              type="button"
              onClick={clearFilters}
              className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-red-500 rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <DataTable
          columns={paymentColumns}
          data={paymentsData}
          loading={loading}
          onQueryChange={handleQueryChange}
          searchPlaceholder="Search payments by invoice number, description..."
          emptyStateIcon="ri-money-dollar-circle-line"
          emptyStateMessage="No payments found"
        />
      </div>

      {/* Proof of Payment Modal */}
      {showProofModal && selectedPayment && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
              onClick={() => setShowProofModal(false)}
            ></div>

            {/* Modal panel */}
            <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Proof of Payment Documents
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Payment #{selectedPayment.invoice_number} - {formatAmount(selectedPayment.amount, selectedPayment.currency)}
                  </p>
                </div>
                <button
                  onClick={() => setShowProofModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Documents List */}
              <div className="space-y-4">
                {selectedPayment.documents && selectedPayment.documents.length > 0 ? (
                  selectedPayment.documents
                    .filter((doc: any) =>
                      doc.document_type === 'proof_of_payment' ||
                      doc.document_type === 'PROOF_OF_PAYMENT'
                    )
                    .map((document: any, index: number) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <i className="ri-file-text-line text-2xl text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {document.file_name}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Uploaded on {formatDate(document.created_at)}
                              </p>
                              {document.file_size && (
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  Size: {(document.file_size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => window.open(`/api/documents/${document.document_id}/download`, '_blank')}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                            >
                              <i className="ri-download-line mr-1"></i>
                              Download
                            </button>
                            <button
                              onClick={() => window.open(`/api/documents/${document.document_id}/view`, '_blank')}
                              className="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                            >
                              <i className="ri-eye-line mr-1"></i>
                              View
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="text-center py-8">
                    <i className="ri-file-text-line text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
                    <p className="text-gray-500 dark:text-gray-400">
                      No proof of payment documents found for this payment.
                    </p>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowProofModal(false)}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentsTab;
