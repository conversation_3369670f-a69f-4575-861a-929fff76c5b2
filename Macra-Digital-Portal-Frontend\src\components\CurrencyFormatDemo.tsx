'use client';

import React from 'react';
import { formatCurrency } from '../utils/formatters';

const CurrencyFormatDemo = () => {
  const amounts = [
    1000,
    10000,
    20000,
    100000,
    1000000,
    10000000
  ];

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
        Currency Formatting Examples
      </h2>
      <p className="mb-4 text-gray-600 dark:text-gray-400">
        Demonstrating currency formatting with commas after the first 2 digits for numbers over 5 digits.
      </p>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Original Number
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Formatted (MWK)
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Formatted (USD)
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {amounts.map((amount, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-900/50' : ''}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {amount.toString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {formatCurrency(amount, 'MWK')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {formatCurrency(amount, 'USD')}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-6">
        <h3 className="text-lg font-medium mb-2 text-gray-900 dark:text-gray-100">
          How to Use
        </h3>
        <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto text-sm">
          {`import { formatCurrency } from '../utils/formatters';

// Basic usage
formatCurrency(20000); // MK20,000

// With different currency
formatCurrency(100000, 'USD'); // $100,000

// With decimal places
formatCurrency(20000.50, 'MWK', 2); // MK20,000.50`}
        </pre>
      </div>
    </div>
  );
};

export default CurrencyFormatDemo;