/**
 * Debug utilities for notification system
 */

import { apiClient } from '../lib/apiClient';

export const debugNotificationEndpoint = async () => {
  console.log('=== Debugging Notification Endpoint ===');
  
  try {
    // Test basic API connectivity
    console.log('1. Testing API connectivity...');
    const healthResponse = await apiClient.get('/health');
    console.log('✅ API Health Check:', healthResponse.data);
  } catch (error: any) {
    console.error('❌ API Health Check failed:', error.message);
  }

  try {
    // Test notification endpoint
    console.log('2. Testing notification endpoint...');
    const notificationResponse = await apiClient.get('/notifications?limit=1');
    console.log('✅ Notification endpoint response:', notificationResponse.data);
  } catch (error: any) {
    console.error('❌ Notification endpoint failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      code: error.code,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        headers: error.config?.headers
      }
    });
  }

  try {
    // Test auth token
    console.log('3. Testing authentication...');
    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    if (token) {
      console.log('✅ Auth token found:', token.substring(0, 20) + '...');
      
      // Decode token payload
      const payload = JSON.parse(atob(token.split('.')[1]));
      console.log('Token payload:', {
        sub: payload.sub,
        exp: new Date(payload.exp * 1000).toISOString(),
        iat: new Date(payload.iat * 1000).toISOString(),
        role: payload.role
      });
    } else {
      console.warn('⚠️ No auth token found');
    }
  } catch (error: any) {
    console.error('❌ Auth token test failed:', error.message);
  }

  console.log('=== Debug Complete ===');
};

// Call this function from browser console: debugNotificationEndpoint()
if (typeof window !== 'undefined') {
  (window as any).debugNotificationEndpoint = debugNotificationEndpoint;
}