/**
 * Applicant Information Types
 * Consolidated interface definitions for applicant-related data
 */

// Main applicant information interface
export interface ApplicantInfoData {
  applicantName: string;
  postalPoBox: string;
  postalCity: string;
  postalCountry: string;
  physicalStreet: string;
  physicalCity: string;
  physicalCountry: string;
  telephone: string;
  fax?: string; // Optional field
  email: string;
}

// Shareholder data interface
export interface ShareholderData {
  name: string;
  nationality: string;
  address: string;
  shareholding: string;
  shares?: number; // Optional for backward compatibility
  percentage?: number; // Optional for backward compatibility
}

// Director data interface
export interface DirectorData {
  name: string;
  nationality: string;
  address: string;
  position?: string; // Optional for backward compatibility
  appointmentDate?: string; // Optional for backward compatibility
}

// Company profile data interface
export interface CompanyProfileData {
  shareholders: ShareholderData[];
  directors: DirectorData[];
  foreignOwnership: string;
  businessRegistrationNo: string;
  tpin: string;
  website: string;
  dateOfIncorporation: string;
  placeOfIncorporation: string;
}

// Management data interface
export interface ManagementData {
  ceoName: string;
  ceoQualifications: string;
  ceoExperience: string;
  technicalManagerName: string;
  technicalManagerQualifications: string;
  technicalManagerExperience: string;
  organizationalStructure: string;
}

// Professional services data interface
export interface ProfessionalServicesData {
  auditorsName: string;
  auditorsAddress: string;
  lawyersName: string;
  lawyersAddress: string;
  bankersName: string;
  bankersAddress: string;
}

// Business information data interface
export interface BusinessInfoData {
  businessDescription: string;
  operationalAreas: string;
  facilities: string;
  equipment: string;
  businessModel: string;
}

// Service scope data interface
export interface ServiceScopeData {
  servicesOffered: string;
  targetMarket: string;
  geographicCoverage: string;
  serviceStandards: string;
}

// Business plan data interface
export interface BusinessPlanData {
  marketAnalysis: string;
  financialProjections: string;
  competitiveAdvantage: string;
  riskAssessment: string;
  implementationTimeline: string;
}

// Legal history data interface
export interface LegalHistoryData {
  previousViolations: string;
  courtCases: string;
  regulatoryHistory: string;
  complianceRecord: string;
}

// Complete application form data interface
export interface ApplicationFormData {
  applicantInfo: ApplicantInfoData;
  companyProfile: CompanyProfileData;
  management: ManagementData;
  professionalServices: ProfessionalServicesData;
  businessInfo: BusinessInfoData;
  serviceScope: ServiceScopeData;
  businessPlan: BusinessPlanData;
  legalHistory: LegalHistoryData;
}

// Component props interface for application form components
export interface ApplicationFormComponentProps {
  data: any;
  onChange: (data: any) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}

// Form field validation interface
export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

// Form validation schema interface
export interface ValidationSchema {
  [fieldName: string]: FieldValidation;
}
