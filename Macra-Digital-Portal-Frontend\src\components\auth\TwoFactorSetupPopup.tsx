'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ShieldCheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import Modal from '@/components/common/Modal';

interface TwoFactorSetupPopupProps {
  isOpen: boolean;
  onClose: () => void;
  isCustomerPortal?: boolean;
  userName?: string;
}

const TwoFactorSetupPopup: React.FC<TwoFactorSetupPopupProps> = ({
  isOpen,
  onClose,
  isCustomerPortal = false,
  userName = 'User'
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSetupNow = () => {
    setLoading(true);
    const setupPath = isCustomerPortal ? '/customer/auth/setup-2fa' : '/auth/setup-2fa';

    // Store session flag to indicate this is from popup
    sessionStorage.setItem('2fa_setup_from_popup', 'true');
    sessionStorage.setItem('2fa_setup_redirect_back', window.location.pathname);

    // Close the popup and navigate
    onClose();
    router.push(setupPath);
  };

  const handleRemindLater = () => {
    // Store preference to remind later (could be used for future reminders)
    localStorage.setItem('2fa_remind_later', Date.now().toString());
    onClose();
  };

  const handleSkip = () => {
    // Store preference to skip (could be used to not show again for a period)
    localStorage.setItem('2fa_setup_skipped', Date.now().toString());
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Secure Your Account"
      size="md"
      showCloseButton={true}
      closeOnOverlayClick={false}
    >
      <div className="text-center">
        {/* Icon */}
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
          <ShieldCheckIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>

        {/* Content */}
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Enable Two-Factor Authentication
        </h3>
        
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Hi {userName}! Protect your account with an extra layer of security. 
          Two-factor authentication helps keep your account safe from unauthorized access.
        </p>

        {/* Benefits */}
        <div className="text-left bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Benefits of 2FA:
          </h4>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• Enhanced account security</li>
            <li>• Protection against unauthorized access</li>
            <li>• Peace of mind for your data</li>
            <li>• Quick and easy setup process</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col space-y-3">
          <button
            onClick={handleSetupNow}
            disabled={loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            {loading ? 'Setting up...' : 'Set Up Now'}
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={handleRemindLater}
              className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Remind Later
            </button>
            
            <button
              onClick={handleSkip}
              className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Skip for Now
            </button>
          </div>
        </div>

        {/* Footer Note */}
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
          You can always enable 2FA later from your profile settings.
        </p>
      </div>
    </Modal>
  );
};

export default TwoFactorSetupPopup;
