'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { notificationService } from '@/services/notificationService';
import { AppNotification } from '@/types/notification';

export interface NotificationCounts {
  total: number;
  unread: number;
}

export interface UseNotificationsReturn {
  notifications: AppNotification[];
  unreadCount: number;
  totalCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  getNotificationCounts: () => Promise<void>;
}

export const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<AppNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, isAuthenticated } = useAuth();
  const { showError } = useToast();

  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated || !user) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching notifications for user:', {
        userId: user.user_id,
        email: user.email
      });

      const data = await notificationService.getUserNotifications({
        page: 1,
        limit: 50 // Get more notifications for better UX
      });

      if (data && data.notifications) {
        setNotifications(data.notifications);
        setUnreadCount(data.unread_count);
        setTotalCount(data.total_count);
      } else {
        setNotifications([]);
        setUnreadCount(0);
        setTotalCount(0);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';
      setError(errorMessage);
      console.error('Error fetching notifications:', err);
      // Don't show toast error for initial load failures
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  const markAsRead = useCallback(async (id: string) => {
    if (!isAuthenticated) {
      return;
    }

    try {
      await notificationService.markAsRead(id);

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.notification_id === id
            ? { ...notification, status: 'read', read_at: new Date().toISOString() }
            : notification
        )
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';
      setError(errorMessage);
      showError('Failed to mark notification as read');
      console.error('Error marking notification as read:', err);
    }
  }, [isAuthenticated, showError]);

  const markAllAsRead = useCallback(async () => {
    if (!isAuthenticated || notifications.length === 0) {
      return;
    }

    try {
      // Mark all unread notifications as read
      const unreadNotifications = notifications.filter(n => n.status === 'unread');

      for (const notification of unreadNotifications) {
        await markAsRead(notification.notification_id);
      }

      setUnreadCount(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';
      setError(errorMessage);
      showError('Failed to mark all notifications as read');
      console.error('Error marking all notifications as read:', err);
    }
  }, [isAuthenticated, notifications, markAsRead, showError]);

  const getNotificationCounts = useCallback(async () => {
    if (!isAuthenticated) {
      return;
    }

    try {
      const counts = await notificationService.getNotificationCount();
      setUnreadCount(counts.unread);
      setTotalCount(counts.total);
    } catch (err) {
      console.error('Error getting notification counts:', err);
    }
  }, [isAuthenticated]);

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications();
  }, [fetchNotifications]);

  // Initial fetch when component mounts and user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchNotifications();
    }
  }, [isAuthenticated, user, fetchNotifications]);

  // Set up polling for new notifications every 30 seconds
  useEffect(() => {
    if (!isAuthenticated || !user) {
      return;
    }

    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isAuthenticated, user, fetchNotifications]);

  return {
    notifications,
    unreadCount,
    totalCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
    getNotificationCounts,
  };
};
