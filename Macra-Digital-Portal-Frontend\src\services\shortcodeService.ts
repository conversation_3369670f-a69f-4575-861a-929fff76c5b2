/**
 * Shortcode Service
 * Handles API calls to shortcode endpoints following backend service patterns
 */

import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import {
  CreateShortcodeDto,
  UpdateShortcodeDto,
  Shortcode,
  ShortcodeFormData,
  ShortcodeStatus,
  ShortcodeFilters
} from '@/types/shortcode';
import { PaginateQuery } from '@/types';

/**
 * Custom error class for shortcode service operations
 */
export class ShortcodeServiceError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ShortcodeServiceError';
  }
}

/**
 * Shortcode Service Class
 * Implements backend service patterns with proper error handling,
 * validation, and response processing
 */
class ShortcodeService {
  private readonly baseUrl = '/standards/shortcodes';

  /**
   * Handle API errors consistently following backend patterns
   */
  private handleApiError(error: any, operation: string): never {
    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;
    const errorData = error.response?.data;

    // Log detailed error information
    console.error(`❌ ShortcodeService.${operation} error:`, {
      status,
      statusText: error.response?.statusText,
      message,
      data: errorData,
      url: error.config?.url,
      method: error.config?.method,
      code: error.code
    });

    // Handle specific error cases
    if (status === 401) {
      throw new ShortcodeServiceError(
        'Authentication failed. Please log in again.',
        'AUTH_FAILED',
        status,
        errorData
      );
    }

    if (status === 403) {
      throw new ShortcodeServiceError(
        'Access denied. You do not have permission to perform this operation.',
        'ACCESS_DENIED',
        status,
        errorData
      );
    }

    if (status === 404) {
      throw new ShortcodeServiceError(
        'Shortcode not found.',
        'NOT_FOUND',
        status,
        errorData
      );
    }

    if (status === 409) {
      throw new ShortcodeServiceError(
        message || 'Conflict occurred. The shortcode may already exist.',
        'CONFLICT',
        status,
        errorData
      );
    }

    if (status === 422) {
      throw new ShortcodeServiceError(
        message || 'Validation failed. Please check your input data.',
        'VALIDATION_FAILED',
        status,
        errorData
      );
    }

    if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
      throw new ShortcodeServiceError(
        'Network error. Please check your connection and try again.',
        'NETWORK_ERROR',
        undefined,
        errorData
      );
    }

    if (error.code === 'ECONNABORTED') {
      throw new ShortcodeServiceError(
        'Request timeout. The server took too long to respond.',
        'TIMEOUT',
        undefined,
        errorData
      );
    }

    // Generic error fallback
    throw new ShortcodeServiceError(
      message || 'An unexpected error occurred while processing your request.',
      'UNKNOWN_ERROR',
      status,
      errorData
    );
  }

  /**
   * Validate create shortcode DTO
   */
  private validateCreateShortcodeDto(data: CreateShortcodeDto): void {
    const errors: string[] = [];

    if (!data.audience) {
      errors.push('Audience is required');
    }

    if (!data.category) {
      errors.push('Category is required');
    }

    if (!data.description || data.description.trim().length === 0) {
      errors.push('Description is required');
    }

    if (data.description && data.description.length > 255) {
      errors.push('Description must not exceed 255 characters');
    }

    if (data.notes && data.notes.length > 255) {
      errors.push('Notes must not exceed 255 characters');
    }



    if (errors.length > 0) {
      throw new ShortcodeServiceError(
        `Validation failed: ${errors.join(', ')}`,
        'VALIDATION_ERROR',
        422,
        { errors }
      );
    }
  }

  /**
   * Validate update shortcode DTO
   */
  private validateUpdateShortcodeDto(data: UpdateShortcodeDto): void {
    const errors: string[] = [];

    if (data.description !== undefined && data.description.length > 255) {
      errors.push('Description must not exceed 255 characters');
    }

    if (data.notes !== undefined && data.notes.length > 255) {
      errors.push('Notes must not exceed 255 characters');
    }



    if (errors.length > 0) {
      throw new ShortcodeServiceError(
        `Validation failed: ${errors.join(', ')}`,
        'VALIDATION_ERROR',
        422,
        { errors }
      );
    }
  }

  /**
   * Create a new shortcode
   */
  async createShortcode(data: CreateShortcodeDto, userId?: string): Promise<Shortcode> {
    try {
      console.log('🔍 ShortcodeService.createShortcode:', { data, userId });

      // Validate input data
      this.validateCreateShortcodeDto(data);

      // Prepare request data with user context
      const requestData = {
        ...data,
        created_by: userId
      };

      const response = await apiClient.post(this.baseUrl, requestData);
      const result = processApiResponse(response);

      console.log('✅ Shortcode created successfully:', {
        shortcode_id: result.shortcode_id,
        shortcode: result.shortcode,
        status: result.status
      });

      return result;
    } catch (error: any) {
      this.handleApiError(error, 'createShortcode');
    }
  }

  /**
   * Update an existing shortcode
   */
  async updateShortcode(id: string, data: UpdateShortcodeDto, userId?: string): Promise<Shortcode> {
    try {
      console.log(`🔍 ShortcodeService.updateShortcode:`, { id, data, userId });

      // Validate input data
      this.validateUpdateShortcodeDto(data);

      // Prepare request data with user context
      const requestData = {
        ...data,
        updated_by: userId
      };

      const response = await apiClient.put(`${this.baseUrl}/${id}`, requestData);
      const result = processApiResponse(response);

      console.log('✅ Shortcode updated successfully:', {
        shortcode_id: result.shortcode_id,
        shortcode: result.shortcode,
        status: result.status
      });

      return result;
    } catch (error: any) {
      this.handleApiError(error, 'updateShortcode');
    }
  }

  /**
   * Build query parameters for API requests
   */
  private buildQueryParams(query: PaginateQuery & ShortcodeFilters): Record<string, any> {
    const params: Record<string, any> = {};

    // Pagination parameters
    if (query.page !== undefined) params.page = query.page;
    if (query.limit !== undefined) params.limit = query.limit;
    if (query.sortBy) params.sortBy = query.sortBy;
    if (query.searchBy) params.searchBy = query.searchBy;
    if (query.search) params.search = query.search;
    if (query.filter) params.filter = query.filter;

    // Filter parameters
    if (query.status) params.status = query.status;
    if (query.category) params.category = query.category;
    if (query.audience) params.audience = query.audience;
    if (query.assigned_to) params.assigned_to = query.assigned_to;
    if (query.application_id) params.application_id = query.application_id;

    return params;
  }

  /**
   * Get all shortcodes with optional filtering and pagination
   */
  async getAllShortcodes(query?: PaginateQuery & ShortcodeFilters): Promise<Shortcode[]> {
    try {
      console.log('🔍 ShortcodeService.getAllShortcodes:', { query });

      const params = query ? this.buildQueryParams(query) : {};
      const response = await apiClient.get(this.baseUrl, { params });
      const result = processApiResponse(response);

      // Handle both paginated and non-paginated responses
      const shortcodes = Array.isArray(result) ? result : (result.data || []);

      console.log(`✅ Retrieved ${shortcodes.length} shortcodes`);
      return shortcodes;
    } catch (error: any) {
      this.handleApiError(error, 'getAllShortcodes');
    }
  }

  /**
   * Get shortcode by ID
   */
  async getShortcodeById(id: string): Promise<Shortcode> {
    try {
      console.log(`🔍 ShortcodeService.getShortcodeById:`, { id });

      if (!id || id.trim().length === 0) {
        throw new ShortcodeServiceError(
          'Shortcode ID is required',
          'INVALID_INPUT',
          400
        );
      }

      const response = await apiClient.get(`${this.baseUrl}/${id}`);
      const result = processApiResponse(response);

      console.log('✅ Shortcode retrieved successfully:', {
        shortcode_id: result.shortcode_id,
        shortcode: result.shortcode
      });

      return result;
    } catch (error: any) {
      this.handleApiError(error, 'getShortcodeById');
    }
  }

  /**
   * Get shortcode by code
   */
  async getShortcodeByCode(shortcode: string): Promise<Shortcode> {
    try {
      console.log(`🔍 ShortcodeService.getShortcodeByCode:`, { shortcode });

      if (!shortcode || shortcode.trim().length === 0) {
        throw new ShortcodeServiceError(
          'Shortcode value is required',
          'INVALID_INPUT',
          400
        );
      }

      const response = await apiClient.get(`${this.baseUrl}/code/${shortcode}`);
      const result = processApiResponse(response);

      console.log('✅ Shortcode retrieved by code successfully:', {
        shortcode_id: result.shortcode_id,
        shortcode: result.shortcode
      });

      return result;
    } catch (error: any) {
      this.handleApiError(error, 'getShortcodeByCode');
    }
  }

  /**
   * Delete shortcode
   */
  async deleteShortcode(id: string): Promise<void> {
    try {
      console.log(`🔍 ShortcodeService.deleteShortcode:`, { id });

      if (!id || id.trim().length === 0) {
        throw new ShortcodeServiceError(
          'Shortcode ID is required',
          'INVALID_INPUT',
          400
        );
      }

      await apiClient.delete(`${this.baseUrl}/${id}`);

      console.log('✅ Shortcode deleted successfully:', { id });
    } catch (error: any) {
      this.handleApiError(error, 'deleteShortcode');
    }
  }

  /**
   * Convert form data to create DTO following backend patterns
   */
  convertFormDataToCreateDto(
    formData: ShortcodeFormData,
    applicationId?: string
  ): CreateShortcodeDto {
    console.log('🔄 Converting form data to create DTO:', { formData, applicationId });

    const dto: CreateShortcodeDto = {
      application_id: applicationId,
      audience: formData.audience,
      status: ShortcodeStatus.INACTIVE, // Always set to inactive as per requirements
      category: formData.category,
      description: formData.description.trim(),
      notes: formData.notes?.trim() || undefined,
      shortcode_length: formData.shortcode_length || 3, // Default to 3 if not provided
    };

    // Validate the DTO before returning
    this.validateCreateShortcodeDto(dto);

    console.log('✅ Form data converted to create DTO:', dto);
    return dto;
  }

  /**
   * Convert form data to update DTO following backend patterns
   */
  convertFormDataToUpdateDto(
    formData: ShortcodeFormData,
    applicationId?: string
  ): UpdateShortcodeDto {
    console.log('🔄 Converting form data to update DTO:', { formData, applicationId });

    const dto: UpdateShortcodeDto = {
      application_id: applicationId,
      audience: formData.audience,
      category: formData.category,
      description: formData.description.trim(),
      notes: formData.notes?.trim() || undefined,
      shortcode_length: formData.shortcode_length,
      // Note: status is not included in update as it should remain uneditable
    };

    // Validate the DTO before returning
    this.validateUpdateShortcodeDto(dto);

    console.log('✅ Form data converted to update DTO:', dto);
    return dto;
  }

  /**
   * Validate shortcode form data (legacy method for backward compatibility)
   */
  validateFormData(formData: ShortcodeFormData): string[] {
    console.log('🔍 Validating form data (legacy method):', formData);

    try {
      // Use the new validation method internally
      const dto = this.convertFormDataToCreateDto(formData);
      this.validateCreateShortcodeDto(dto);
      return []; // No errors
    } catch (error: any) {
      if (error instanceof ShortcodeServiceError && error.details?.errors) {
        return error.details.errors;
      }
      return [error.message || 'Validation failed'];
    }
  }

  /**
   * Test backend connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 ShortcodeService.testConnection: Testing connectivity...');

      await apiClient.get('/health');

      console.log('✅ Backend health check passed');
      return true;
    } catch (error: any) {
      console.error('❌ Backend connectivity test failed:', {
        message: error.message,
        status: error.response?.status,
        code: error.code
      });
      return false;
    }
  }

  /**
   * Get shortcode statistics (if supported by backend)
   */
  async getShortcodeStats(): Promise<any> {
    try {
      console.log('🔍 ShortcodeService.getShortcodeStats: Fetching statistics...');

      const response = await apiClient.get(`${this.baseUrl}/stats`);
      const result = processApiResponse(response);

      console.log('✅ Shortcode statistics retrieved successfully');
      return result;
    } catch (error: any) {
      this.handleApiError(error, 'getShortcodeStats');
    }
  }
}

/**
 * Singleton instance of ShortcodeService
 * Following backend service patterns with proper error handling and validation
 */
export const shortcodeService = new ShortcodeService();

/**
 * Default export for backward compatibility
 */
export default shortcodeService;

/**
 * Export the service class for testing and advanced usage
 */
export { ShortcodeService };
