import { Metadata } from 'next';
import VerifyLicenseClient from './VerifyLicenseClient';

export const metadata: Metadata = {
  title: 'Verify License',
  description: 'Verify the authenticity of MACRA licenses instantly. Enter a license number to check its validity, status, and details.',
  keywords: [
    'license verification',
    'MACRA license check',
    'verify license authenticity',
    'license validation',
    'telecommunications license',
    'broadcasting license'
  ],
  openGraph: {
    title: 'Verify MACRA License',
    description: 'Verify the authenticity of MACRA licenses instantly. Enter a license number to check its validity, status, and details.',
    url: 'https://portal.macra.mw/public/verify',
  },
  twitter: {
    title: 'Verify MACRA License',
    description: 'Verify the authenticity of MACRA licenses instantly. Enter a license number to check its validity, status, and details.',
  },
};

export default function VerifyLicensePage() {
  return <VerifyLicenseClient />;
}

