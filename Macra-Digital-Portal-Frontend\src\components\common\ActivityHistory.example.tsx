'use client';

import React, { useState } from 'react';
import ActivityHistory from './ActivityHistory';
import { ActivityNote } from '@/services/activityNotesService';

/**
 * Example component demonstrating how to use the reusable ActivityHistory component
 */
const ActivityHistoryExample: React.FC = () => {
  const [selectedEntityType, setSelectedEntityType] = useState('application');
  const [selectedEntityId, setSelectedEntityId] = useState('APP-2024-001');
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notesCount, setNotesCount] = useState(0);

  const handleNotesChange = (notes: ActivityNote[]) => {
    setNotesCount(notes.length);
  };

  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const entityTypes = [
    { value: 'application', label: 'Application' },
    { value: 'user', label: 'User' },
    { value: 'organization', label: 'Organization' },
    { value: 'license', label: 'License' },
    { value: 'invoice', label: 'Invoice' },
    { value: 'payment', label: 'Payment' },
  ];

  const sampleEntityIds = {
    application: ['APP-2024-001', 'APP-2024-002', 'APP-2024-003'],
    user: ['USER-001', 'USER-002', 'USER-003'],
    organization: ['ORG-001', 'ORG-002', 'ORG-003'],
    license: ['LIC-001', 'LIC-002', 'LIC-003'],
    invoice: ['INV-001', 'INV-002', 'INV-003'],
    payment: ['PAY-001', 'PAY-002', 'PAY-003'],
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          ActivityHistory Component Example
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          This example demonstrates the reusable ActivityHistory component that can display 
          activity notes for any entity type with search and filtering capabilities.
        </p>

        {/* Controls */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Component Configuration
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Entity Type
              </label>
              <select
                value={selectedEntityType}
                onChange={(e) => {
                  setSelectedEntityType(e.target.value);
                  setSelectedEntityId(sampleEntityIds[e.target.value as keyof typeof sampleEntityIds][0]);
                }}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {entityTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Entity ID
              </label>
              <select
                value={selectedEntityId}
                onChange={(e) => setSelectedEntityId(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {sampleEntityIds[selectedEntityType as keyof typeof sampleEntityIds].map(id => (
                  <option key={id} value={id}>{id}</option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={triggerRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
              >
                <i className="ri-refresh-line mr-2"></i>
                Refresh
              </button>
            </div>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            <strong>Current Notes Count:</strong> {notesCount}
          </div>
        </div>

        {/* ActivityHistory Component */}
        <ActivityHistory
          entityType={selectedEntityType}
          entityId={selectedEntityId}
          title={`${selectedEntityType.charAt(0).toUpperCase() + selectedEntityType.slice(1)} Activity History`}
          showSearch={true}
          showFilters={true}
          maxHeight="max-h-96"
          onNotesChange={handleNotesChange}
          refreshTrigger={refreshTrigger}
          className="mb-6"
        />

        {/* Usage Examples */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Usage */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
              Basic Usage
            </h4>
            <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-x-auto">
{`<ActivityHistory
  entityType="application"
  entityId="APP-2024-001"
/>`}
            </pre>
          </div>

          {/* Advanced Usage */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
              Advanced Usage
            </h4>
            <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-x-auto">
{`<ActivityHistory
  entityType="application"
  entityId="APP-2024-001"
  title="Custom Title"
  showSearch={true}
  showFilters={true}
  maxHeight="max-h-64"
  onNotesChange={handleNotesChange}
  refreshTrigger={refreshTrigger}
  className="custom-class"
/>`}
            </pre>
          </div>
        </div>

        {/* Props Documentation */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Component Props
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Prop
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Required
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    entityType
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    The type of entity (application, user, organization, etc.)
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    entityId
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                    Yes
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    The unique identifier of the entity
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    title
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Custom title for the component (default: "Activity History")
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    showSearch
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Show search input field (default: true)
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    showFilters
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    boolean
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Show filter dropdowns (default: true)
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    maxHeight
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    string
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Maximum height CSS class (default: "max-h-96")
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    onNotesChange
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    function
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Callback when notes are loaded or changed
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    refreshTrigger
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    number
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    No
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    Increment to trigger external refresh
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Features List */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Component Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Reusable for any entity type</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Real-time search functionality</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Advanced filtering by type, category, priority</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Responsive design</span>
              </li>
            </ul>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Loading states and error handling</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Customizable height and styling</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>External refresh trigger support</span>
              </li>
              <li className="flex items-center">
                <i className="ri-check-line text-green-500 mr-2"></i>
                <span>Dark mode support</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityHistoryExample;
