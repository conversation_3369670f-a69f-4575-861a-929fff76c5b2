// Re-export core types
export * from './applicant_info';
export * from './base';
export * from './shortcode';
export * from './license'

// Re-export license types explicitly
export * from './license';

// Re-export other types selectively to avoid conflicts
export * from './user';
export * from './identification';
export * from './invoice';
export * from './notification';
export * from './department';
export * from './organization';
export * from './public';
export * from './task';
export * from './audit';
export * from './payment';
export * from './equipment';
export * from './license-category';
export * from './address_types';

// Re-export license types with explicit naming to avoid conflicts
export type {
  LicenseType as LicenseTypeEntity,
  CreateLicenseTypeDto,
  UpdateLicenseTypeDto,
  LicenseTypeFilters,
  LicenseTypesResponse
} from './license-type';

// Common/Shared types
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
  status?: number;
}

export interface ApiError {
  message: string;
  error?: string;
  statusCode?: number;
  timestamp?: string;
  path?: string;
}

// Common entity fields
export interface BaseEntity {
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface SoftDeleteEntity extends BaseEntity {
  deleted_at?: string;
}

// User reference type (for creator/updater fields)
export interface UserReference {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
}

// Navigation types
export interface NavigationItem {
  id: string;
  name: string;
  code?: string;
  href: string;
  label: string;
  roles?: string[];
  icon?: string;
  children?: NavigationItem[];
}

// File/Document types
export interface FileUpload {
  file: File;
  name: string;
  type: string;
  size: number;
}

export interface UploadedFile {
  file_id: string;
  filename: string;
  original_name: string;
  mime_type: string;
  size: number;
  path: string;
  url?: string;
  created_at: string;
}

// Status types
export type EntityStatus = 'active' | 'inactive' | 'suspended' | 'pending' | 'approved' | 'rejected';

// Common filter types
export interface BaseFilters {
  search?: string;
  status?: string;
  dateRange?: string;
  created_by?: string;
  updated_by?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file' | 'date';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface FormConfig {
  fields: FormField[];
  submitLabel?: string;
  cancelLabel?: string;
}

// Table/Grid types
export interface TableColumn<T = any> {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: T) => React.ReactNode;
}

export interface TableConfig<T = any> {
  columns: TableColumn<T>[];
  sortable?: boolean;
  filterable?: boolean;
  selectable?: boolean;
  pagination?: boolean;
}

// Modal/Dialog types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

// Notification/Toast types
export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  actions?: {
    label: string;
    action: () => void;
  }[];
}