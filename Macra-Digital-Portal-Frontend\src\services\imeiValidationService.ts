import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

// Types for IMEI validation
export interface IMEIValidationRequest {
  imei: string;
}

export interface IMEIValidationResponse {
  imei: string;
  isValid: boolean;
  deviceInfo?: {
    manufacturer?: string;
    model?: string;
    model_name?: string;
    type?: string;
    device_type?: string;
    tac?: string; // Type Allocation Code (first 8 digits)
    device_serial_number?: string;
    device_approval_date?: string;
  };
  status: 'valid' | 'invalid' | 'blacklisted' | 'unknown' | 'exists';
  message?: string;
  validatedAt: string;
  source?: string; // Source of validation (e.g., 'gsma', 'local_db')
  // Direct fields for when IMEI exists
  model_name?: string;
  device_serial_number?: string;
  device_approval_date?: string;
  device_type?: string;
}

export interface IMEIApplicationData {
  application_id: string;
  imei: string;
  validation_result: IMEIValidationResponse;
  modify_device?: boolean;
  created_at: string;
  updated_at: string;
}

export const imeiValidationService = {
  /**
   * Validate IMEI against the devices endpoint
   * @param imei - The IMEI number to validate (15 digits)
   * @returns Promise<IMEIValidationResponse>
   */
  async validateIMEI(imei: string): Promise<IMEIValidationResponse> {
    try {
      console.log('🔍 Validating IMEI:', imei);
      
      // Ensure IMEI is clean (no spaces or special characters)
      const cleanImei = imei.replace(/\D/g, '');
      
      if (cleanImei.length !== 15) {
        throw new Error('IMEI must be exactly 15 digits');
      }

      const response = await apiClient.get(`/devices/imei/${cleanImei}`);
      const validationResult = processApiResponse(response);

      // Check if the response indicates the IMEI exists (has device data)
      if (validationResult && typeof validationResult === 'object') {
        // If the response contains device information fields, treat it as existing IMEI
        if (validationResult.model_name || validationResult.device_serial_number ||
            validationResult.device_approval_date || validationResult.device_type) {

          const formattedResult: IMEIValidationResponse = {
            imei: cleanImei,
            isValid: true,
            status: 'exists',
            message: 'Device found in database',
            validatedAt: new Date().toISOString(),
            model_name: validationResult.model_name,
            device_serial_number: validationResult.device_serial_number,
            device_approval_date: validationResult.device_approval_date,
            device_type: validationResult.device_type,
            deviceInfo: {
              model_name: validationResult.model_name,
              device_serial_number: validationResult.device_serial_number,
              device_approval_date: validationResult.device_approval_date,
              device_type: validationResult.device_type,
              manufacturer: validationResult.manufacturer,
              model: validationResult.model || validationResult.model_name,
              type: validationResult.type || validationResult.device_type,
              tac: validationResult.tac
            }
          };

          console.log('✅ IMEI exists in database:', formattedResult);
          return formattedResult;
        }
      }

      console.log('✅ IMEI validation result:', validationResult);
      return validationResult;
    } catch (error: any) {
      console.error('❌ IMEI validation error:', error);
      
      // Handle different types of errors
      if (error.response?.status === 404) {
        return {
          imei,
          isValid: false,
          status: 'unknown',
          message: 'IMEI not found in database',
          validatedAt: new Date().toISOString()
        };
      }
      
      if (error.response?.status === 400) {
        return {
          imei,
          isValid: false,
          status: 'invalid',
          message: error.response.data?.message || 'Invalid IMEI format',
          validatedAt: new Date().toISOString()
        };
      }
      
      // For network errors or other issues, throw the error
      throw new Error(error.message || 'Failed to validate IMEI');
    }
  },

  /**
   * Save IMEI validation data for an application
   * @param applicationId - The application ID
   * @param imeiData - The IMEI validation data
   * @returns Promise<IMEIApplicationData>
   */
  async saveIMEIForApplication(
    applicationId: string, 
    imeiData: { imei: string; validation_result: IMEIValidationResponse }
  ): Promise<IMEIApplicationData> {
    try {
      console.log('💾 Saving IMEI data for application:', applicationId);
      
      const response = await apiClient.post(`/applications/${applicationId}/imei`, imeiData);
      const savedData = processApiResponse(response);
      
      console.log('✅ IMEI data saved:', savedData);
      return savedData;
    } catch (error: any) {
      console.error('❌ Error saving IMEI data:', error);
      throw new Error(error.response?.data?.message || 'Failed to save IMEI data');
    }
  },

  /**
   * Get IMEI validation data for an application
   * @param applicationId - The application ID
   * @returns Promise<IMEIApplicationData | null>
   */
  async getIMEIForApplication(applicationId: string): Promise<IMEIApplicationData | null> {
    try {
      console.log('📖 Loading IMEI data for application:', applicationId);
      
      const response = await apiClient.get(`/applications/${applicationId}/imei`);
      const imeiData = processApiResponse(response);
      
      console.log('✅ IMEI data loaded:', imeiData);
      return imeiData;
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log('ℹ️ No IMEI data found for application:', applicationId);
        return null;
      }
      
      console.error('❌ Error loading IMEI data:', error);
      throw new Error(error.response?.data?.message || 'Failed to load IMEI data');
    }
  },

  /**
   * Update IMEI validation data for an application
   * @param applicationId - The application ID
   * @param imeiData - The updated IMEI validation data
   * @returns Promise<IMEIApplicationData>
   */
  async updateIMEIForApplication(
    applicationId: string, 
    imeiData: { imei: string; validation_result: IMEIValidationResponse }
  ): Promise<IMEIApplicationData> {
    try {
      console.log('🔄 Updating IMEI data for application:', applicationId);
      
      const response = await apiClient.put(`/applications/${applicationId}/imei`, imeiData);
      const updatedData = processApiResponse(response);
      
      console.log('✅ IMEI data updated:', updatedData);
      return updatedData;
    } catch (error: any) {
      console.error('❌ Error updating IMEI data:', error);
      throw new Error(error.response?.data?.message || 'Failed to update IMEI data');
    }
  },

  /**
   * Delete IMEI validation data for an application
   * @param applicationId - The application ID
   * @returns Promise<{ message: string }>
   */
  async deleteIMEIForApplication(applicationId: string): Promise<{ message: string }> {
    try {
      console.log('🗑️ Deleting IMEI data for application:', applicationId);
      
      const response = await apiClient.delete(`/applications/${applicationId}/imei`);
      const result = processApiResponse(response);
      
      console.log('✅ IMEI data deleted:', result);
      return result;
    } catch (error: any) {
      console.error('❌ Error deleting IMEI data:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete IMEI data');
    }
  },

  /**
   * Batch validate multiple IMEIs
   * @param imeis - Array of IMEI numbers to validate
   * @returns Promise<IMEIValidationResponse[]>
   */
  async validateMultipleIMEIs(imeis: string[]): Promise<IMEIValidationResponse[]> {
    try {
      console.log('🔍 Batch validating IMEIs:', imeis.length);
      
      const cleanImeis = imeis.map(imei => imei.replace(/\D/g, ''));
      
      const response = await apiClient.post('/devices/imei/batch-validate', {
        imeis: cleanImeis
      });
      
      const results = processApiResponse(response);
      console.log('✅ Batch validation results:', results);
      return results;
    } catch (error: any) {
      console.error('❌ Batch validation error:', error);
      throw new Error(error.response?.data?.message || 'Failed to validate IMEIs');
    }
  }
};
