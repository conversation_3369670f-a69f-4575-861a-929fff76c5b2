'use client';

import React, { useState } from 'react';
import { HelpCircle, Phone, Mail, ExternalLink, Shield, Search, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
import PageTransition, { StaggeredAnimation } from '@/components/public/PageTransition';
import { FAQSchema } from '@/components/public/StructuredData';

// FAQ Item Component with collapsible functionality
const FAQItem: React.FC<{
  question: string;
  answer: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  delay?: number;
}> = ({ question, answer, isOpen, onToggle, delay = 0 }) => {
  return (
    <StaggeredAnimation delay={delay}>
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <button
          onClick={onToggle}
          className="flex items-center justify-between w-full text-left hover:text-primary transition-colors"
        >
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {question}
          </h3>
          {isOpen ? (
            <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          )}
        </button>
        <div 
          className={`mt-2 overflow-hidden transition-all duration-300 ease-in-out ${
            isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="text-gray-600 dark:text-gray-400">
            {answer}
          </div>
        </div>
      </div>
    </StaggeredAnimation>
  );
};

export default function HelpClient() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0); // First FAQ open by default
  const [searchTerm, setSearchTerm] = useState('');

  const faqData = [
    {
      question: "How do I verify a license?",
      answer: (
        <div>
          <p className="mb-2">
            To verify a license, enter the license number in the format LIC-YYYY-MM-NNN
            (e.g., LIC-2024-01-001) on the verification page. You can optionally add a
            verification code if available for enhanced security.
          </p>
          <a 
            href="/public/verify" 
            className="inline-flex items-center space-x-2 text-primary hover:text-red-700 transition-colors font-medium"
          >
            <Shield className="h-4 w-4" />
            <span>Start Verification Now</span>
          </a>
        </div>
      )
    },
    {
      question: "What is the license number format?",
      answer: (
        <div>
          <p className="mb-2">License numbers follow the format LIC-YYYY-MM-NNN where:</p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li><strong>LIC</strong> = License prefix</li>
            <li><strong>YYYY</strong> = Year of issue (4 digits)</li>
            <li><strong>MM</strong> = Month of issue (2 digits)</li>
            <li><strong>NNN</strong> = Sequential number (3 digits)</li>
          </ul>
          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Example:</strong> LIC-2024-01-001 means the first license issued in January 2024
            </p>
          </div>
        </div>
      )
    },
    {
      question: "What is a verification code?",
      answer: (
        <p>
          A verification code is an optional 12-character alphanumeric code that provides
          enhanced security for license verification. It&apos;s typically found on official
          license documents or certificates and adds an extra layer of authentication.
        </p>
      )
    },
    {
      question: "What do the different license statuses mean?",
      answer: (
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <div>
              <strong>Active:</strong> License is currently valid and in good standing
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div>
              <strong>Expired:</strong> License has passed its expiration date and requires renewal
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div>
              <strong>Suspended:</strong> License is temporarily inactive due to regulatory actions
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
            <div>
              <strong>Revoked:</strong> License has been permanently cancelled
            </div>
          </div>
        </div>
      )
    },
    {
      question: "Why can't I find a license?",
      answer: (
        <div>
          <p className="mb-2">If a license cannot be found, it may be due to:</p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Incorrect license number format or typos</li>
            <li>License has not been issued yet</li>
            <li>License has been revoked or cancelled</li>
            <li>System maintenance or temporary issues</li>
          </ul>
          <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Tip:</strong> Double-check the license number format and try again. If the issue persists, contact MACRA support.
            </p>
          </div>
        </div>
      )
    },
    {
      question: "Is this verification service official?",
      answer: (
        <p>
          Yes, this is the official license verification service provided by the
          Malawi Communications Regulatory Authority (MACRA). All verification
          results are sourced directly from MACRA&apos;s official database and are
          updated in real-time.
        </p>
      )
    }
  ];

  const filteredFAQs = faqData.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (typeof faq.answer === 'string' && faq.answer.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <PageTransition>
      <FAQSchema />
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Page Header */}
        <StaggeredAnimation delay={100}>
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-purple-100 dark:bg-purple-900/20 p-3 rounded-full animate-pulse">
                <HelpCircle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Help & Support
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Get help with license verification and find answers to common questions
            </p>
          </div>
        </StaggeredAnimation>

        {/* Search Bar */}
        <StaggeredAnimation delay={200}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search help topics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
            />
          </div>
        </StaggeredAnimation>

        {/* Quick Actions */}
        <StaggeredAnimation delay={300}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <Search className="h-6 w-6 text-primary" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Verify a License</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Quickly verify the authenticity of any MACRA-issued license.
              </p>
              <a
                href="/public/verify"
                className="inline-flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors shadow-sm"
              >
                <Shield className="h-4 w-4" />
                <span>Start Verification</span>
              </a>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <Phone className="h-6 w-6 text-green-600 dark:text-green-400" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Contact Support</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Need direct assistance? Contact MACRA support team.
              </p>
              <div className="space-y-2 text-sm">
                <a 
                  href="tel:+2651770100"
                  className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
                >
                  <Phone className="h-4 w-4" />
                  <span>+265 1 770 100</span>
                </a>
                <a 
                  href="mailto:<EMAIL>"
                  className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
                >
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </a>
              </div>
            </div>
          </div>
        </StaggeredAnimation>

        {/* FAQ Section */}
        <StaggeredAnimation delay={400}>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Frequently Asked Questions
            </h2>

            {filteredFAQs.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  No results found for &quot;{searchTerm}&quot;
                </p>
                <button
                  onClick={() => setSearchTerm('')}
                  className="mt-2 text-primary hover:text-red-700 transition-colors"
                >
                  Clear search
                </button>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredFAQs.map((faq, index) => (
                  <FAQItem
                    key={index}
                    question={faq.question}
                    answer={faq.answer}
                    isOpen={openFAQ === index}
                    onToggle={() => toggleFAQ(index)}
                    delay={index * 100}
                  />
                ))}
              </div>
            )}
          </div>
        </StaggeredAnimation>

        {/* Troubleshooting */}
        <StaggeredAnimation delay={500}>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-6 border border-yellow-200 dark:border-yellow-800 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mt-0.5" />
              <div>
                <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                  Troubleshooting Tips
                </h3>
                <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                  <li>• Double-check the license number format (LIC-YYYY-MM-NNN)</li>
                  <li>• Ensure there are no extra spaces or characters</li>
                  <li>• Try without the verification code if having issues</li>
                  <li>• Clear your browser cache and try again</li>
                  <li>• Contact support if problems persist</li>
                </ul>
              </div>
            </div>
          </div>
        </StaggeredAnimation>

        {/* Contact Information */}
        <StaggeredAnimation delay={600}>
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Contact MACRA
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Office Address</h4>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Malawi Communications Regulatory Authority<br />
                  Private Bag 261<br />
                  Lilongwe 3, Malawi
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Contact Details</h4>
                <div className="text-gray-600 dark:text-gray-400 text-sm space-y-1">
                  <a
                    href="tel:+2651770100"
                    className="flex items-center space-x-2 hover:text-primary transition-colors"
                  >
                    <Phone className="h-4 w-4" />
                    <span>+265 1 770 100</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center space-x-2 hover:text-primary transition-colors"
                  >
                    <Mail className="h-4 w-4" />
                    <span><EMAIL></span>
                  </a>
                  <a
                    href="https://macra.mw"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-primary hover:text-red-700 transition-colors"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>macra.mw</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </StaggeredAnimation>
      </div>
    </PageTransition>
  );
}
