{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ix7efcYw/GoqOa8BXrighmJ44+pcT+fWrqZPp7PCo8o=", "__NEXT_PREVIEW_MODE_ID": "1b4c9b0dff48508cc7460caac7183c86", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "81956b57c1ae02901bf2de3d9bbd57a7f4efef67b4388cf524858374c2b77e1a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "84347523c791935540e48081848eeb246f6302b1bee2854129a996ee52f2c4f4"}}}, "sortedMiddleware": ["/"], "functions": {}}