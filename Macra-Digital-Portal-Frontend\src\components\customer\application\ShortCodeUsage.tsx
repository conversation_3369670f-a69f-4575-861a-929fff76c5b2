'use client';

import React, { useState, useEffect } from 'react';
import { FormInput, TextArea, Select } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import {
  ShortcodeFormData,
  DEFAULT_SHORTCODE_FORM_DATA,
  SHORTCODE_CATEGORY_OPTIONS,
  SHORTCODE_AUDIENCE_OPTIONS,
  SHORTCODE_VALIDATION
} from '@/types/shortcode';
import { shortcodeService } from '@/services/shortcodeService';

interface ShortCodeUsageProps {
  initialData?: Partial<ShortcodeFormData>;
  onDataChange?: (data: ShortcodeFormData) => void;
  onValidationChange?: (isValid: boolean) => void;
  disabled?: boolean;
  showValidation?: boolean;
}

const ShortCodeUsage: React.FC<ShortCodeUsageProps> = ({
  initialData = {},
  onDataChange,
  onValidationChange,
  disabled = false,
  showValidation = true
}) => {
  const [formData, setFormData] = useState<ShortcodeFormData>({
    ...DEFAULT_SHORTCODE_FORM_DATA,
    ...initialData
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validate form data
  const validateForm = (data: ShortcodeFormData): string[] => {
    return shortcodeService.validateFormData(data);
  };

  // Handle form data changes
  const handleChange = (field: keyof ShortcodeFormData, value: any) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    setTouched({ ...touched, [field]: true });

    // Validate and notify parent
    const validationErrors = validateForm(updatedData);
    setErrors(validationErrors);
    
    if (onDataChange) {
      onDataChange(updatedData);
    }
    
    if (onValidationChange) {
      onValidationChange(validationErrors.length === 0);
    }
  };

  // Handle field blur
  const handleBlur = (field: keyof ShortcodeFormData) => {
    setTouched({ ...touched, [field]: true });
  };

  // Get field error
  const getFieldError = (field: keyof ShortcodeFormData): string | undefined => {
    if (!touched[field] || !showValidation) return undefined;
    
    switch (field) {
      case 'audience':
        return !formData.audience ? 'Audience is required' : undefined;
      case 'category':
        return !formData.category ? 'Category is required' : undefined;
      case 'description':
        if (!formData.description?.trim()) return 'Description is required';
        if (formData.description.length > SHORTCODE_VALIDATION.DESCRIPTION_MAX_LENGTH) {
          return `Description must not exceed ${SHORTCODE_VALIDATION.DESCRIPTION_MAX_LENGTH} characters`;
        }
        return undefined;
      case 'notes':
        if (formData.notes && formData.notes.length > SHORTCODE_VALIDATION.NOTES_MAX_LENGTH) {
          return `Notes must not exceed ${SHORTCODE_VALIDATION.NOTES_MAX_LENGTH} characters`;
        }
        return undefined;

      default:
        return undefined;
    }
  };

  // Initialize validation on mount
  useEffect(() => {
    const validationErrors = validateForm(formData);
    setErrors(validationErrors);
    
    if (onValidationChange) {
      onValidationChange(validationErrors.length === 0);
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Short Code Usage Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide details about how you intend to use the short code service.
        </p>
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Audience */}
        <Select
          label="Intended Audience"
          value={formData.audience}
          onChange={(e) => handleChange('audience', e.target.value)}
          onBlur={() => handleBlur('audience')}
          options={SHORTCODE_AUDIENCE_OPTIONS}
          required
          disabled={disabled}
          error={getFieldError('audience')}
          helperText="Select the target audience for your short code service"
        />

        {/* Category */}
        <Select
          label="Service Category"
          value={formData.category}
          onChange={(e) => handleChange('category', e.target.value)}
          onBlur={() => handleBlur('category')}
          options={SHORTCODE_CATEGORY_OPTIONS}
          required
          disabled={disabled}
          error={getFieldError('category')}
          helperText="Choose the category that best describes your service"
        />


      </div>

      {/* Description */}
      <TextArea
        label="Service Description"
        value={formData.description}
        onChange={(e) => handleChange('description', e.target.value)}
        onBlur={() => handleBlur('description')}
        placeholder="Describe the service you plan to offer using this short code..."
        required
        disabled={disabled}
        error={getFieldError('description')}
        helperText={`Provide a detailed description of your short code service (${formData.description.length}/${SHORTCODE_VALIDATION.DESCRIPTION_MAX_LENGTH} characters)`}
        rows={4}
        maxLength={SHORTCODE_VALIDATION.DESCRIPTION_MAX_LENGTH}
      />

      {/* Notes */}
      <TextArea
        label="Additional Notes"
        value={formData.notes || ''}
        onChange={(e) => handleChange('notes', e.target.value)}
        onBlur={() => handleBlur('notes')}
        placeholder="Any additional information or special requirements..."
        disabled={disabled}
        error={getFieldError('notes')}
        helperText={`Optional additional information (${(formData.notes || '').length}/${SHORTCODE_VALIDATION.NOTES_MAX_LENGTH} characters)`}
        rows={3}
        maxLength={SHORTCODE_VALIDATION.NOTES_MAX_LENGTH}
      />

      {/* Important Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Important Information
            </h4>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
              <ul className="list-disc list-inside space-y-1">
                <li>Short codes will be automatically assigned based on availability and your preferences</li>
                <li>All short codes are initially set to inactive status until approved by MACRA</li>
                <li>You will be notified once your short code application is processed</li>
                <li>Ensure your service description accurately reflects your intended use</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Validation Messages */}
      {showValidation && errors.length > 0 && (
        <FormMessages
          type="error"
          messages={errors}
        />
      )}
    </div>
  );
};

export default ShortCodeUsage;
