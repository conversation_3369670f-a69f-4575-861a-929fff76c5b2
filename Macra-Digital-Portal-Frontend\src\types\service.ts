// Service-specific types and DTOs that are used across multiple services

// Generic service response types
export interface ServiceResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface ServiceError {
  message: string;
  code?: string;
  details?: any;
}

// File upload types for services
export interface FileUploadResponse {
  file_id: string;
  filename: string;
  original_name: string;
  url: string;
  size: number;
  mime_type: string;
}

export interface BulkUploadResponse {
  successful: FileUploadResponse[];
  failed: {
    filename: string;
    error: string;
  }[];
  total: number;
  success_count: number;
  error_count: number;
}

// Search and filter types
export interface SearchOptions {
  query: string;
  fields?: string[];
  exact_match?: boolean;
  case_sensitive?: boolean;
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterOptions {
  [key: string]: string | string[] | number | boolean | null;
}

// Export/Import types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf';
  fields?: string[];
  filters?: FilterOptions;
  include_headers?: boolean;
}

export interface ImportOptions {
  file: File;
  mapping?: Record<string, string>;
  skip_headers?: boolean;
  validate_only?: boolean;
}

export interface ImportResult {
  total_rows: number;
  successful_rows: number;
  failed_rows: number;
  errors: {
    row: number;
    field?: string;
    message: string;
  }[];
  warnings: {
    row: number;
    field?: string;
    message: string;
  }[];
}

// Validation types
export interface ValidationRule {
  field: string;
  rules: string[];
  message?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: {
    field: string;
    message: string;
  }[];
}

// Cache types
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key?: string;
  tags?: string[];
}

// Batch operation types
export interface BatchOperation<T = any> {
  operation: 'create' | 'update' | 'delete';
  data: T;
  id?: string;
}

export interface BatchResult<T = any> {
  successful: {
    operation: string;
    data: T;
    id?: string;
  }[];
  failed: {
    operation: string;
    data: T;
    id?: string;
    error: string;
  }[];
  total: number;
  success_count: number;
  error_count: number;
}

// Notification service types
export interface NotificationPayload {
  type: 'email' | 'sms' | 'push' | 'in_app';
  recipient: string;
  subject?: string;
  message: string;
  template?: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  scheduled_at?: string;
}

export interface NotificationResult {
  id: string;
  status: 'sent' | 'failed' | 'pending' | 'scheduled';
  sent_at?: string;
  error?: string;
}

// Analytics and reporting types
export interface AnalyticsQuery {
  metric: string;
  dimensions?: string[];
  filters?: FilterOptions;
  date_range?: {
    start: string;
    end: string;
  };
  group_by?: string;
  aggregation?: 'sum' | 'count' | 'avg' | 'min' | 'max';
}

export interface AnalyticsResult {
  metric: string;
  value: number;
  dimensions?: Record<string, string>;
  timestamp?: string;
}

export interface ReportOptions {
  title: string;
  description?: string;
  format: 'pdf' | 'html' | 'csv' | 'xlsx';
  template?: string;
  data: any;
  charts?: {
    type: 'bar' | 'line' | 'pie' | 'table';
    data: any;
    options?: any;
  }[];
}

// Webhook types
export interface WebhookPayload {
  event: string;
  data: any;
  timestamp: string;
  source: string;
}

export interface WebhookResponse {
  success: boolean;
  message?: string;
  retry?: boolean;
}

// Background job types
export interface JobOptions {
  priority?: number;
  delay?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
}

export interface JobResult {
  id: string;
  status: 'pending' | 'active' | 'completed' | 'failed' | 'delayed';
  progress?: number;
  result?: any;
  error?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

// Health check types
export interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  message?: string;
  details?: any;
  timestamp: string;
}

export interface SystemHealth {
  overall_status: 'healthy' | 'unhealthy' | 'degraded';
  services: HealthCheck[];
  timestamp: string;
}

// Rate limiting types
export interface RateLimit {
  limit: number;
  remaining: number;
  reset: number;
  window: number;
}

// Feature flag types
export interface FeatureFlag {
  name: string;
  enabled: boolean;
  conditions?: {
    user_id?: string[];
    role?: string[];
    percentage?: number;
  };
}

// Configuration types
export interface ConfigValue {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  default_value?: any;
  required?: boolean;
}

export interface ConfigSection {
  name: string;
  description?: string;
  values: ConfigValue[];
}
