import { customerApi } from '@/lib/customer-api';
import { processApiResponse } from '@/lib/authUtils';
import { invoiceService } from './invoiceService';
import { applicationService } from './applicationService';
import { Payment, PaymentFilters, PaymentStatistics } from '@/types/invoice';
import { PaginatedResponse, PaginateQuery } from '@/types';
import { apiClient } from '@/lib';

class PaymentService {


  // Helper method to get user application IDs
  private async getUserApplicationIds(): Promise<string[]> {
    try {
      console.log('📋 Fetching user applications...');
      const applicationsPromise = applicationService.getUserApplications();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Applications fetch timeout')), 10000)
      );

      const userApplications = await Promise.race([applicationsPromise, timeoutPromise]);
      const applicationIds = userApplications.data?.map((app: any) => app.application_id) || [];
      console.log(`📋 Found ${applicationIds.length} user applications`);
      return applicationIds;
    } catch (error) {
      console.warn('Failed to fetch user applications:', error);
      return [];
    }
  }

  // Helper method to create empty response
  private createEmptyResponse(query: PaginateQuery & PaymentFilters): PaginatedResponse<Payment> {
    return {
      data: [],
      meta: {
        itemsPerPage: query.limit || 10,
        totalItems: 0,
        currentPage: query.page || 1,
        totalPages: 0,
        sortBy: [],
        searchBy: [],
        search: query.search || '',
        select: [],
      },
      links: { current: '' },
    };
  }

  // Helper method to apply filters
  private applyFilters(payments: any[], query: PaginateQuery & PaymentFilters): any[] {
    let filteredPayments = payments;

    if (query.status) {
      const allowedStatuses = query.status.split(',').map((s: string) => s.trim().toUpperCase());
      filteredPayments = filteredPayments.filter(payment =>
        allowedStatuses.includes(payment.status.toUpperCase())
      );
    }

    if (query.payment_type) {
      filteredPayments = filteredPayments.filter(payment =>
        payment.payment_type === query.payment_type
      );
    }

    if (query.search) {
      const searchTerm = query.search.toLowerCase();
      filteredPayments = filteredPayments.filter(payment =>
        payment.invoice_number?.toLowerCase().includes(searchTerm) ||
        payment.description?.toLowerCase().includes(searchTerm) ||
        payment.payment_type?.toLowerCase().includes(searchTerm)
      );
    }

    if (query.dateRange && typeof query.dateRange === 'object') {
      const { start, end } = query.dateRange as { start?: string; end?: string };
      if (start || end) {
        filteredPayments = filteredPayments.filter(payment => {
          const paymentDate = new Date(payment.created_at);
          if (start && paymentDate < new Date(start)) return false;
          if (end && paymentDate > new Date(end)) return false;
          return true;
        });
      }
    }

    return filteredPayments;
  }


  // Helper method to get payments for applications
  private async getPaymentsForApplications(applicationIds: string[]): Promise<any[]> {
    const allPayments: any[] = [];

    for (const applicationId of applicationIds) {
      try {
        console.log(`📄 Getting invoices for application: ${applicationId}`);
        const response = await invoiceService.getInvoicesByEntity('application', applicationId);
        const invoices = processApiResponse(response);
        return invoices;
      } catch (error) {
        console.warn(`⚠️ Could not get invoices for application ${applicationId}:`, error);
      }
    }

    return allPayments;
  }
  async getPayments(query: PaginateQuery & PaymentFilters, isCustomer?: boolean): Promise<PaginatedResponse<Payment>> {
    try {
      const params = this.buildQueryParams(query);
      if (isCustomer) {
        // Use customer-specific endpoint for customers
        console.log('🔒 Using customer-specific payments endpoint');
        return customerApi.getPayments(params);
      } else {
        // Use general payments endpoint for admin/staff (shows all payments)
        const response = await apiClient.get('/payments', { params });
        return processApiResponse(response);
      }
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 403) {
        return await this.getPaymentsByApplications(query);
      }

      if (error.response?.status === 404) {
        return await this.getPaymentsByApplications(query);
      }
      return await this.getPaymentsByApplications(query);
    }
  }

  // Helper method to build query parameters
  private buildQueryParams(query: PaginateQuery & PaymentFilters): any {
    const params: any = {};
    if (query.page) params.page = query.page;
    if (query.limit) params.limit = query.limit;
    if (query.status) params.status = query.status;
    if (query.payment_type) params.paymentType = query.payment_type;
    if (query.search) params.search = query.search;
    if (query.dateRange) params.dateRange = query.dateRange;
    return params;
  }

  private async getPaymentsByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {
    try {
      // Get user's applications to filter payments
      const applicationIds = await this.getUserApplicationIds();

      if (applicationIds.length === 0) {
        console.log('ℹ️ No applications found for user, returning empty payments');
        return this.createEmptyResponse(query);
      }

      // Get payments for user's applications
      const allPayments = await this.getPaymentsForApplications(applicationIds);
      console.log(`📊 Total payments found: ${allPayments.length}`);
      return processApiResponse(allPayments);
    } catch (error) {
      console.error('❌ Error getting user payments:', error);
      // Return empty data if API is not available
      return this.createEmptyResponse(query);
    }
  }

  async getInvoices(query: PaginateQuery & PaymentFilters, isCustomer: boolean = false): Promise<PaginatedResponse<Payment>> {


    try {
      const params: any = {};
      if (query.page) params.page = query.page;
      if (query.limit) params.limit = query.limit;
      // For invoices, we want pending and overdue statuses
      params.status = query.status || 'pending';
      if (query.search) params.search = query.search;
      if (isCustomer) {
        // Use customer-specific endpoint for customers
        return await customerApi.getInvoices(params);
      } else {
        // Use general invoices endpoint for admin/staff (shows all invoices)
        const response = await apiClient.get('/invoices', { params });
        return processApiResponse(response);
      }
    } catch (apiError: any) {
      console.error('❌ Error in getInvoices:', apiError);

      // Check if it's a permission error (403) or other API error
      if (apiError?.response?.status === 403) {
        console.warn('⚠️ Permission denied for invoices API, using application-based approach');
      }
      // Fallback: get invoices by applications
      return await this.getInvoicesByApplications(query);
    }
  }

  private async getInvoicesByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {
    try {
      // Use the existing getInvoices endpoint which should handle user filtering on the backend
      const params = this.buildQueryParams(query);
      // Set default status for invoices if not specified
      if (!params.status) {
        params.status = 'draft,pending,overdue';
      }

      const response = await customerApi.getInvoices(params);
      const processedResponse = processApiResponse(response);

      // Return the processed response directly - backend should handle pagination and filtering
      return processedResponse;
    } catch (error) {
      console.error('PaymentService.getInvoicesByApplications error:', error);
      return this.createEmptyResponse(query);
    }
  }

  async getPaymentById(id: string): Promise<Payment> {
    try {
      const response = await apiClient.get(`/payments/${id}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('PaymentService.getPaymentById error:', error);
      throw new Error('Payment not found');
    }
  }

  async getPaymentStatistics(isCustomer?: boolean): Promise<PaymentStatistics | null> {
    try {
      try {
        let response;
        if (isCustomer) {
          // Use customer-specific endpoint for customers
          response = await customerApi.getPaymentStatistics();
        } else {
          // Use general statistics endpoint for admin/staff (all statistics)
          console.log('👨‍💼 Using admin/staff statistics endpoint (all statistics)');
          response = await apiClient.get('/payments/statistics');
        }

        const processedResponse = processApiResponse(response).data;

        // Transform backend response to our format
        return {
          total: processedResponse.totalPayments || 0,
          pending: processedResponse.pendingPayments || 0,
          paid: processedResponse.paidPayments || 0,
          overdue: processedResponse.overduePayments || 0,
          cancelled: processedResponse.cancelledPayments || 0,
          totalAmount: processedResponse.totalAmount || 0,
          pendingAmount: processedResponse.pendingAmount || 0,
          paidAmount: processedResponse.paidAmount || 0,
          overdueAmount: processedResponse.overdueAmount || 0,
        };
      } catch (apiError: any) {
        console.warn('⚠️ Backend statistics API failed:', apiError);

        // Check if it's a permission error (403) or other API error
        if (apiError?.response?.status === 403) {
          console.warn('⚠️ Permission denied for statistics API');
        }
        return null;
      }
    } catch (error) {
      console.error('❌ Error calculating payment statistics:', error);
      return null;
    }
  }


  async getPaymentsByEntity(_entityType: string, _entityId: string, query: PaginateQuery): Promise<PaginatedResponse<Payment>| null> {
    try {
      // Customer API doesn't have entity-specific payments, so we'll filter from all payments
      const params: any = {};
      if (query.page) params.page = query.page;
      if (query.limit) params.limit = query.limit;
      const response = await customerApi.getPayments(params);
      return processApiResponse(response);
    } catch (error) {
      console.error('PaymentService.getPaymentsByEntity error:', error);
      return null;
    }
  }

  async updatePayment(_id: string, _data: Partial<Payment>): Promise<Payment> {
    try {
      // Customer API doesn't have update payment method, so we'll throw an error
      throw new Error('Payment updates not supported in customer portal');
    } catch (error) {
      throw new Error('Payment updates not supported in customer portal');
    }
  }

  async createPayment(data: Omit<Payment, 'payment_id' | 'created_at' | 'updated_at'>): Promise<Payment> {
    try {
      const paymentData = {
        amount: data.amount,
        currency: data.currency,
        dueDate: data.due_date,
        issueDate: data.issue_date,
        description: data.description,
        paymentType: data.payment_type.replace('_', ' '),
        clientName: data.user?.first_name + ' ' + data.user?.last_name || 'Customer',
        clientEmail: data.user?.email || '<EMAIL>',
        paymentMethod: data.payment_method,
        notes: data.notes,
      };

      const response = await customerApi.createPayment(paymentData);
      const processedResponse = processApiResponse(response);

      // Transform response back to our format
      return processedResponse;
    } catch (error) {
      throw new Error('Failed to create payment');
    }
  }

  async deletePayment(_id: string): Promise<void> {
    throw new Error('Payment deletion not supported in customer portal');
  }

  // Proof of payment upload
  async uploadProofOfPayment(paymentId: string, file: File, data: any): Promise<any> {
    try {
      const formData = new FormData();

      formData.append('file', file);
      Object.keys(data).forEach(key => {
        formData.append(key, data[key]);
      });

      const response = await customerApi.uploadProofOfPayment(paymentId, formData);
      return processApiResponse(response);
    } catch (error) {
      console.log('Proof of payment API not available, simulating upload');

      // Fallback to simulation if API is not available
      await new Promise(resolve => setTimeout(resolve, 2000));

      return {
        success: true,
        message: 'Proof of payment uploaded successfully',
        uploadId: `upload_${Date.now()}`,
        status: 'pending_review'
      };
    }
  }

  private mapInvoiceTypeToPaymentType(entityType: string): string {
    switch (entityType) {
      case 'application': return 'APPLICATION_FEE';
      case 'license': return 'LICENSE_FEE';
      case 'renewal': return 'RENEWAL_FEE';
      case 'procurement': return 'PROCUREMENT_FEE';
      default: return 'LICENSE_FEE';
    }
  }


  async getInvoicePayments(invoiceId: string): Promise<any> {
    try {
      const response = await customerApi.getInvoicePayments(invoiceId);
      const processedResponse = processApiResponse(response);
      return {
        success: true,
        data: processedResponse.data || processedResponse,
        message: 'Invoice payments retrieved successfully'
      };
    } catch (error) {
      console.error('PaymentService.getInvoicePayments error:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to get invoice payments'
      };
    }
  }

  async approvePayment(paymentId: string): Promise<any> {
    try {
      // This would be an admin/staff endpoint to approve payments
      const response = await fetch(`/api/payments/${paymentId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to approve payment');
      }

      const data = await response.json();
      return {
        success: true,
        data,
        message: 'Payment approved successfully'
      };
    } catch (error) {
      console.error('Error approving payment:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : 'Failed to approve payment'
      };
    }
  }

  async rejectPayment(paymentId: string, reason: string): Promise<any> {
    try {
      // This would be an admin/staff endpoint to reject payments
      const response = await fetch(`/api/payments/${paymentId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        throw new Error('Failed to reject payment');
      }

      const data = await response.json();
      return {
        success: true,
        data,
        message: 'Payment rejected successfully'
      };
    } catch (error) {
      console.error('Error rejecting payment:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : 'Failed to reject payment'
      };
    }
  }
}

export const paymentService = new PaymentService();
