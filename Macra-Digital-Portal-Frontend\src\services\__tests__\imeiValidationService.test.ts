import { imeiValidationService } from '../imeiValidationService';
import { apiClient } from '../../lib/apiClient';

// Mock the API client
jest.mock('../../lib/apiClient');
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock processApiResponse
jest.mock('@/lib/authUtils', () => ({
  processApiResponse: jest.fn((response) => response.data)
}));

describe('imeiValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateIMEI', () => {
    test('should validate IMEI successfully', async () => {
      const mockResponse = {
        data: {
          imei: '490154203237518',
          isValid: true,
          deviceInfo: {
            manufacturer: 'Apple',
            model: 'iPhone 12',
            type: 'smartphone',
            tac: '49015420'
          },
          status: 'valid',
          message: 'IMEI is valid',
          validatedAt: '2024-01-01T00:00:00Z',
          source: 'gsma'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateIMEI('490154203237518');

      expect(mockedApiClient.get).toHaveBeenCalledWith('/standards/devices/imei/490154203237518');
      expect(result).toEqual(mockResponse.data);
      expect(result.isValid).toBe(true);
      expect(result.status).toBe('valid');
    });

    test('should handle invalid IMEI format', async () => {
      await expect(imeiValidationService.validateIMEI('12345')).rejects.toThrow('IMEI must be exactly 15 digits');
    });

    test('should handle IMEI not found (404)', async () => {
      const mockError = {
        response: {
          status: 404
        }
      };

      mockedApiClient.get.mockRejectedValue(mockError);

      const result = await imeiValidationService.validateIMEI('490154203237518');

      expect(result.isValid).toBe(false);
      expect(result.status).toBe('unknown');
      expect(result.message).toBe('IMEI not found in database');
    });

    test('should handle invalid IMEI (400)', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid IMEI checksum'
          }
        }
      };

      mockedApiClient.get.mockRejectedValue(mockError);

      const result = await imeiValidationService.validateIMEI('490154203237518');

      expect(result.isValid).toBe(false);
      expect(result.status).toBe('invalid');
      expect(result.message).toBe('Invalid IMEI checksum');
    });

    test('should clean IMEI input', async () => {
      const mockResponse = {
        data: {
          imei: '490154203237518',
          isValid: true,
          status: 'valid',
          validatedAt: '2024-01-01T00:00:00Z'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      await imeiValidationService.validateIMEI('490 154 203 237 518');

      expect(mockedApiClient.get).toHaveBeenCalledWith('/standards/devices/imei/490154203237518');
    });
  });

  describe('saveIMEIForApplication', () => {
    test('should save IMEI data for application', async () => {
      const applicationId = 'app-123';
      const imeiData = {
        imei: '490154203237518',
        validation_result: {
          imei: '490154203237518',
          isValid: true,
          status: 'valid' as const,
          validatedAt: '2024-01-01T00:00:00Z'
        }
      };

      const mockResponse = {
        data: {
          application_id: applicationId,
          ...imeiData,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.saveIMEIForApplication(applicationId, imeiData);

      expect(mockedApiClient.post).toHaveBeenCalledWith(`/applications/${applicationId}/imei`, imeiData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getIMEIForApplication', () => {
    test('should get IMEI data for application', async () => {
      const applicationId = 'app-123';
      const mockResponse = {
        data: {
          application_id: applicationId,
          imei: '490154203237518',
          validation_result: {
            imei: '490154203237518',
            isValid: true,
            status: 'valid',
            validatedAt: '2024-01-01T00:00:00Z'
          },
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.getIMEIForApplication(applicationId);

      expect(mockedApiClient.get).toHaveBeenCalledWith(`/applications/${applicationId}/imei`);
      expect(result).toEqual(mockResponse.data);
    });

    test('should return null when IMEI data not found', async () => {
      const applicationId = 'app-123';
      const mockError = {
        response: {
          status: 404
        }
      };

      mockedApiClient.get.mockRejectedValue(mockError);

      const result = await imeiValidationService.getIMEIForApplication(applicationId);

      expect(result).toBeNull();
    });
  });

  describe('validateMultipleIMEIs', () => {
    test('should validate multiple IMEIs', async () => {
      const imeis = ['490154203237518', '356938035643809'];
      const mockResponse = {
        data: [
          {
            imei: '490154203237518',
            isValid: true,
            status: 'valid',
            validatedAt: '2024-01-01T00:00:00Z'
          },
          {
            imei: '356938035643809',
            isValid: true,
            status: 'valid',
            validatedAt: '2024-01-01T00:00:00Z'
          }
        ]
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await imeiValidationService.validateMultipleIMEIs(imeis);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/standards/devices/imei/batch-validate', {
        imeis: ['490154203237518', '356938035643809']
      });
      expect(result).toEqual(mockResponse.data);
      expect(result).toHaveLength(2);
    });
  });
});
