/**
 * Equipment Details Page - Simplified Version
 * 
 * Only includes the required fields:
 * - manufacturer_name
 * - manufacturer_address  
 * - manufacturer_country
 * - brand_trade_name
 * - product_type_name
 * - equipment_category
 * - equipment_model
 * - imei (optional)
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { ApplicationLayout } from '@/components/applications';
import { applicationDeviceService, ApplicationDevice } from '@/services/applicationDeviceService';

// Types and Interfaces
interface EquipmentFormData {
  // Required fields
  manufacturer_name: string;
  manufacturer_address: string;
  manufacturer_country: string;
  brand_trade_name: string;
  product_type_name: string;
  equipment_category: string;
  equipment_model: string;
  // Optional field
  imei?: string;
}

interface ValidationErrors {
  [key: string]: string;
}

// Form field configuration
const FORM_FIELDS = {
  REQUIRED: [
    'manufacturer_name',
    'manufacturer_address',
    'manufacturer_country',
    'brand_trade_name',
    'product_type_name',
    'equipment_category',
    'equipment_model'
  ],
  OPTIONAL: [
    'imei'
  ]
} as const;

// Reusable form input component
interface FormInputProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  type?: 'text' | 'textarea';
  rows?: number;
  className?: string;
}

const FormInput: React.FC<FormInputProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder,
  required = false,
  error,
  disabled = false,
  type = 'text',
  rows = 3,
  className = ''
}) => {
  const baseClassName = `w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 ${
    error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
  } ${className}`;

  return (
    <div>
      <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {label} {required && '*'}
      </label>
      {type === 'textarea' ? (
        <textarea
          id={id}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={baseClassName}
          placeholder={placeholder}
          rows={rows}
          disabled={disabled}
        />
      ) : (
        <input
          type="text"
          id={id}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={baseClassName}
          placeholder={placeholder}
          disabled={disabled}
        />
      )}
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400 mt-1">{error}</p>
      )}
    </div>
  );
};

const EquipmentDetailsPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // Consolidated state management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [existingDevice, setExistingDevice] = useState<ApplicationDevice | null>(null);
  
  // Single source of truth for form data
  const [formData, setFormData] = useState<EquipmentFormData>({
    manufacturer_name: '',
    manufacturer_address: '',
    manufacturer_country: '',
    brand_trade_name: '',
    product_type_name: '',
    equipment_category: '',
    equipment_model: '',
    imei: ''
  });
  
  // Validation errors
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  // Dynamic navigation hook
  const { nextStep } = useDynamicNavigation({
    currentStepRoute: 'equipment-details',
    licenseCategoryId,
    applicationId
  });

  // Utility functions
  const clearMessages = () => {
    setError(null);
    setSuccessMessage(null);
  };

  const updateFormField = (field: keyof EquipmentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const populateFormFromDevice = (device: ApplicationDevice) => {
    setFormData({
      manufacturer_name: device.manufacturer_name || '',
      manufacturer_address: device.manufacturer_address || '',
      manufacturer_country: device.manufacturer_country || '',
      brand_trade_name: device.brand_trade_name || '',
      product_type_name: device.product_type_name || '',
      equipment_model: device.equipment_model || '',
      equipment_category: device.equipment_category?.name || '',
      imei: device.imei || ''
    });
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};

    // Validate required fields
    FORM_FIELDS.REQUIRED.forEach(field => {
      const value = formData[field as keyof EquipmentFormData];
      if (!value || !value.toString().trim()) {
        errors[field] = `${field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Check authentication and required parameters
  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!licenseCategoryId) {
      setError('License category ID is required');
      return;
    }

    setLoading(false);
  }, [isAuthenticated, authLoading, router, licenseCategoryId]);

  // Load existing device data if available
  useEffect(() => {
    const loadExistingData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setLoading(true);
        clearMessages();

        const response = await applicationDeviceService.getDevicesByApplication(applicationId);
        const devices = response.data;

        if (devices && devices.length > 0) {
          const device = devices[0];
          setExistingDevice(device);
          populateFormFromDevice(device);
        }
      } catch (err: any) {
        setError('Failed to load existing device data');
      } finally {
        setLoading(false);
      }
    };

    loadExistingData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save device data
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setError('Application ID is required');
      return false;
    }

    setIsSaving(true);
    clearMessages();

    try {
      // Prepare device payload from form data
      const devicePayload = {
        application_id: applicationId,
        // Include IMEI if provided
        ...(formData.imei && formData.imei.trim() && { imei: formData.imei }),
        // Equipment and manufacturer information
        manufacturer_name: formData.manufacturer_name,
        manufacturer_address: formData.manufacturer_address || undefined,
        manufacturer_country: formData.manufacturer_country || 'Unknown',
        brand_trade_name: formData.brand_trade_name || undefined,
        product_type_name: formData.product_type_name || undefined,
        equipment_category: formData.equipment_category || undefined,
        equipment_model: formData.equipment_model || undefined, // Map to model_name for backend
        approval_status: 'pending'
      };

      let device;
      if (existingDevice) {
        console.log('📱 Updating existing device with ID:', existingDevice.device_id);
        device = await applicationDeviceService.updateDevice(existingDevice.device_id, devicePayload);
      } else {
        console.log('📱 Creating new device');
        device = await applicationDeviceService.createDevice(devicePayload);
        setExistingDevice(device);
      }

      // Save to localStorage as backup
      localStorage.setItem(
        `equipment_data_${applicationId}`,
        JSON.stringify({
          formData,
          timestamp: new Date().toISOString(),
          licenseCategoryId,
          applicationId
        })
      );

      setSuccessMessage('Equipment information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => setSuccessMessage(null), 5000);

      console.log('✅ Equipment data saved successfully');
      return true;
    } catch (err: any) {
      console.error('❌ Error saving equipment data:', err);
      setError(err.message || 'Failed to save equipment information');
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save and continue
  const handleSaveAndContinue = async () => {
    // Validate form before proceeding
    if (!validateForm()) {
      setError('Please complete all required equipment details before proceeding to the next step.');
      return;
    }

    // Save the data before proceeding
    console.log('🔄 Starting save process...');
    const saveSuccess = await handleSave();
    console.log('💾 Save result:', saveSuccess);

    if (!saveSuccess) {
      console.error('❌ Save failed, not proceeding to next step');
      setError('Please fix the validation errors before proceeding.');
      return;
    }

    console.log('🧭 Checking navigation - nextStep:', nextStep);
    if (nextStep) {
      // Navigate to next step
      const params = new URLSearchParams();
      params.set('license_category_id', licenseCategoryId!);
      if (applicationId) {
        params.set('application_id', applicationId);
      }

      const nextUrl = `/customer/applications/apply/${nextStep.route}?${params.toString()}`;
      console.log('🔗 Navigating to:', nextUrl);
      router.push(nextUrl);
    } else {
      console.error('❌ No next step available for navigation');
      setError('Unable to determine next step. Please contact support.');
    }
  };

  // Handle previous
  const handlePrevious = () => {
    // Go back to license selection
    router.push('/customer/applications');
  };

  // Loading state
  if (loading || authLoading) {
    return (
      <CustomerLayout>
        <div className="flex justify-center items-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error && !licenseCategoryId) {
    return (
      <CustomerLayout>
        <div className="max-w-2xl mx-auto mt-8">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <button
              onClick={() => router.push('/customer/applications')}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleSaveAndContinue}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Applications"
        saveButtonText="Save Equipment Details"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Equipment Details
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Please provide detailed information about the equipment for type approval certification.
          </p>
        </div>

        {/* Form Messages */}
        <FormMessages
          errorMessage={error}
          successMessage={successMessage}
          className="mb-6"
        />

        {/* Equipment Details Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="space-y-6">
            {/* Manufacturer Details Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Manufacturer Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Manufacturer Name */}
                <FormInput
                  id="manufacturer_name"
                  label="Manufacturer Name"
                  value={formData.manufacturer_name}
                  onChange={(value) => updateFormField('manufacturer_name', value)}
                  placeholder="Enter manufacturer name"
                  required={true}
                  error={validationErrors.manufacturer_name}
                  disabled={isSaving}
                />

                {/* Manufacturer Country */}
                <FormInput
                  id="manufacturer_country"
                  label="Country of Origin"
                  value={formData.manufacturer_country}
                  onChange={(value) => updateFormField('manufacturer_country', value)}
                  placeholder="Enter country of origin"
                  required={true}
                  error={validationErrors.manufacturer_country}
                  disabled={isSaving}
                />

                {/* Manufacturer Address */}
                <div className="md:col-span-2">
                  <FormInput
                    id="manufacturer_address"
                    label="Manufacturer Address"
                    value={formData.manufacturer_address}
                    onChange={(value) => updateFormField('manufacturer_address', value)}
                    placeholder="Enter manufacturer address"
                    type="textarea"
                    rows={3}
                    required={true}
                    error={validationErrors.manufacturer_address}
                    disabled={isSaving}
                  />
                </div>
              </div>
            </div>

            {/* Equipment Information Section */}
            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Equipment Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Brand/Trade Name */}
                <FormInput
                  id="brand_trade_name"
                  label="Brand/Trade Name"
                  value={formData.brand_trade_name}
                  onChange={(value) => updateFormField('brand_trade_name', value)}
                  placeholder="Enter brand or trade name"
                  required={true}
                  error={validationErrors.brand_trade_name}
                  disabled={isSaving}
                />

                {/* Product Type/Name */}
                <FormInput
                  id="product_type_name"
                  label="Product Type/Name"
                  value={formData.product_type_name}
                  onChange={(value) => updateFormField('product_type_name', value)}
                  placeholder="Enter product type or name"
                  required={true}
                  error={validationErrors.product_type_name}
                  disabled={isSaving}
                />

                {/* Equipment Category */}
                <FormInput
                  id="equipment_category"
                  label="Equipment Category"
                  value={formData.equipment_category}
                  onChange={(value) => updateFormField('equipment_category', value)}
                  placeholder="Enter equipment category"
                  required={true}
                  error={validationErrors.equipment_category}
                  disabled={isSaving}
                />

                {/* Equipment Model */}
                <FormInput
                  id="equipment_model"
                  label="Equipment Model"
                  value={formData.equipment_model}
                  onChange={(value) => updateFormField('equipment_model', value)}
                  placeholder="Enter equipment model"
                  required={true}
                  error={validationErrors.equipment_model}
                  disabled={isSaving}
                />

                {/* IMEI (Optional) */}
                {/* <div className="md:col-span-2">
                  <FormInput
                    id="imei"
                    label="IMEI Number (Optional)"
                    value={formData.imei || ''}
                    onChange={(value) => updateFormField('imei', value)}
                    placeholder="Enter IMEI number (optional)"
                    disabled={isSaving}
                  />
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default EquipmentDetailsPage;
