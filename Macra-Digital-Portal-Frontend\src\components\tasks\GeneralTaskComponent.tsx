'use client';

import React, { useState } from 'react';
import { Task } from '@/services/task-assignment';
import { taskAssignmentService } from '@/services/task-assignment';
import { useToast } from '@/contexts/ToastContext';

interface GeneralTaskComponentProps {
  task: Task;
  onTaskUpdate: () => void;
}

const GeneralTaskComponent: React.FC<GeneralTaskComponentProps> = ({ task, onTaskUpdate }) => {
  const { showToast } = useToast();
  const [updating, setUpdating] = useState(false);
  const [notes, setNotes] = useState('');

  const handleCompleteTask = async () => {
    try {
      setUpdating(true);
      
      const response = await taskAssignmentService.updateTask(task.task_id, {
        status: 'completed',
        completion_notes: notes || undefined,
      });
      
      if (response.success) {
        showToast('Task completed successfully', 'success');
        onTaskUpdate();
      } else {
        throw new Error(response.message || 'Failed to complete task');
      }
    } catch (error) {
      console.error('Error completing task:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
      showToast(errorMessage, 'error');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateStatus = async (status: string) => {
    try {
      setUpdating(true);
      
      const response = await taskAssignmentService.updateTask(task.task_id, {
        status,
        review_notes: notes || undefined,
      });
      
      if (response.success) {
        showToast(`Task status updated to ${status.replace('_', ' ')}`, 'success');
        onTaskUpdate();
      } else {
        throw new Error(response.message || 'Failed to update task status');
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update task status';
      showToast(errorMessage, 'error');
    } finally {
      setUpdating(false);
    }
  };

  const getTaskTypeIcon = (taskType: string) => {
    switch (taskType) {
      case 'complaint': return 'ri-feedback-line';
      case 'data_breach': return 'ri-shield-cross-line';
      case 'evaluation': return 'ri-search-line';
      case 'inspection': return 'ri-search-eye-line';
      case 'document_review': return 'ri-file-text-line';
      case 'compliance_check': return 'ri-shield-check-line';
      case 'follow_up': return 'ri-phone-line';
      default: return 'ri-task-line';
    }
  };

  const getTaskTypeColor = (taskType: string) => {
    switch (taskType) {
      case 'complaint': return 'text-red-600 bg-red-100';
      case 'data_breach': return 'text-red-600 bg-red-100';
      case 'evaluation': return 'text-purple-600 bg-purple-100';
      case 'inspection': return 'text-orange-600 bg-orange-100';
      case 'document_review': return 'text-blue-600 bg-blue-100';
      case 'compliance_check': return 'text-green-600 bg-green-100';
      case 'follow_up': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Task Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <div className={`p-2 rounded-lg mr-3 ${getTaskTypeColor(task.task_type)}`}>
              <i className={`${getTaskTypeIcon(task.task_type)} text-lg`}></i>
            </div>
            Task Details
          </h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Task Information</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Task Number:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{task.task_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                    {task.task_type.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                    {task.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Priority:</span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                    {task.priority.toUpperCase()}
                  </span>
                </div>
                {task.due_date && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Due Date:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {new Date(task.due_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Assignment Information</h3>
              <div className="space-y-3">
                {task.assignee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Assigned To:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {task.assignee.first_name} {task.assignee.last_name}
                    </span>
                  </div>
                )}
                {task.assigner && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Assigned By:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {task.assigner.first_name} {task.assigner.last_name}
                    </span>
                  </div>
                )}
                {task.assigned_at && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Assigned At:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {new Date(task.assigned_at).toLocaleDateString()}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Created:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {new Date(task.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Task Description */}
          {task.description && (
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Description</h3>
              <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                {task.description}
              </p>
            </div>
          )}

          {/* Entity Information */}
          {task.entity_type && task.entity_id && (
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Related Entity</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                  {task.entity_type}
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">•</span>
                <span className="text-sm text-gray-600 dark:text-gray-400">ID:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {task.entity_id}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Task Actions */}
      {task.status !== 'completed' && task.status !== 'cancelled' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-tools-line mr-2 text-purple-600"></i>
              Task Actions
            </h2>
          </div>
          <div className="p-6">
            {/* Notes/Comments */}
            <div className="mb-6">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes/Comments
              </label>
              <textarea
                id="notes"
                rows={4}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="Add notes or comments about this task..."
              />
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {task.status === 'pending' && (
                <button
                  onClick={() => handleUpdateStatus('in_progress')}
                  disabled={updating}
                  className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {updating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Starting...
                    </>
                  ) : (
                    <>
                      <i className="ri-play-line mr-2"></i>
                      Start Task
                    </>
                  )}
                </button>
              )}

              {task.status === 'in_progress' && (
                <>
                  <button
                    onClick={handleCompleteTask}
                    disabled={updating}
                    className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {updating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Completing...
                      </>
                    ) : (
                      <>
                        <i className="ri-check-line mr-2"></i>
                        Complete Task
                      </>
                    )}
                  </button>

                  <button
                    onClick={() => handleUpdateStatus('on_hold')}
                    disabled={updating}
                    className="flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    <i className="ri-pause-line mr-2"></i>
                    Put On Hold
                  </button>
                </>
              )}

              {task.status === 'on_hold' && (
                <button
                  onClick={() => handleUpdateStatus('in_progress')}
                  disabled={updating}
                  className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  <i className="ri-play-line mr-2"></i>
                  Resume Task
                </button>
              )}

              <button
                onClick={() => handleUpdateStatus('cancelled')}
                disabled={updating}
                className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                <i className="ri-close-line mr-2"></i>
                Cancel Task
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Task History/Notes */}
      {(task.review || task.review_notes || task.completion_notes) && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-history-line mr-2 text-gray-600"></i>
              Task History
            </h2>
          </div>
          <div className="p-6 space-y-4">
            {task.review && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Review</h3>
                <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {task.review}
                </p>
              </div>
            )}
            {task.review_notes && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Review Notes</h3>
                <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {task.review_notes}
                </p>
              </div>
            )}
            {task.completion_notes && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Completion Notes</h3>
                <p className="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {task.completion_notes}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralTaskComponent;
