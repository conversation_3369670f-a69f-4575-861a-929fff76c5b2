'use client';

import React, { useState } from 'react';
import { Address, ENTITY_TYPES } from '@/types/address_types';
import { useAddresses } from '@/hooks/useAddressing';
import AddressDropdown from './AddressDropdown';
import AddressModal from './AddressModal';

/**
 * Example component demonstrating how to use the new Address components
 * This shows the integration between AddressDropdown, AddressModal, and useAddressing hook
 */
const AddressExample: React.FC = () => {
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);

  // Use the enhanced addressing hook with caching
  const {
    addresses,
    loading,
    error,
    createAddress,
    editAddress,
    getAddressesByEntity,
    clearAddressCache,
  } = useAddresses();

  const handleAddressSelect = (addressId: string) => {
    setSelectedAddressId(addressId);
  };

  const handleCreateAddress = () => {
    setEditingAddress(null);
    setIsModalOpen(true);
  };

  const handleEditAddress = (address: Address) => {
    setEditingAddress(address);
    setIsModalOpen(true);
  };

  const handleSaveAddress = (address: Address) => {
    console.log('Address saved:', address);
    // The hook automatically updates the addresses list
    setIsModalOpen(false);
    setEditingAddress(null);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingAddress(null);
  };

  const selectedAddress = addresses.find(addr => addr.address_id === selectedAddressId);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
        Address Management Example
      </h1>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Address Selection */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
          Select Address
        </h2>
        <div className="flex gap-4 items-end">
          <div className="flex-1">
            <AddressDropdown
              addresses={addresses}
              selectedAddressId={selectedAddressId}
              onSelect={handleAddressSelect}
              placeholder="Choose an address..."
              disabled={loading}
              error={error || ''}
            />
          </div>
          <button
            onClick={handleCreateAddress}
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i className="ri-add-line mr-2"></i>
            Add Address
          </button>
        </div>
      </div>

      {/* Selected Address Details */}
      {selectedAddress && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Selected Address
              </h3>
              <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <p><strong>Type:</strong> {selectedAddress.address_type}</p>
                <p><strong>Address:</strong> {selectedAddress.address_line_1}</p>
                {selectedAddress.address_line_2 && (
                  <p><strong>Address Line 2:</strong> {selectedAddress.address_line_2}</p>
                )}
                <p><strong>City:</strong> {selectedAddress.city}</p>
                <p><strong>Postal Code:</strong> {selectedAddress.postal_code}</p>
                <p><strong>Country:</strong> {selectedAddress.country}</p>
              </div>
            </div>
            <button
              onClick={() => handleEditAddress(selectedAddress)}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <i className="ri-edit-line mr-1"></i>
              Edit
            </button>
          </div>
        </div>
      )}

      {/* Address List */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            All Addresses ({addresses.length})
          </h2>
          <button
            onClick={clearAddressCache}
            className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            <i className="ri-refresh-line mr-1"></i>
            Clear Cache
          </button>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <i className="ri-loader-4-line animate-spin text-2xl text-gray-400"></i>
            <p className="mt-2 text-gray-500 dark:text-gray-400">Loading addresses...</p>
          </div>
        ) : addresses.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <i className="ri-map-pin-line text-4xl mb-2"></i>
            <p>No addresses found. Create your first address!</p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {addresses.map((address) => (
              <div
                key={address.address_id}
                className={`p-4 border rounded-md cursor-pointer transition-colors ${
                  selectedAddressId === address.address_id
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
                onClick={() => handleAddressSelect(address.address_id)}
              >
                <div className="flex justify-between items-start mb-2">
                  <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded capitalize">
                    {address.address_type}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditAddress(address);
                    }}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <i className="ri-edit-line"></i>
                  </button>
                </div>
                <p className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                  {address.address_line_1}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {[address.city, address.postal_code].filter(Boolean).join(', ')}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Address Modal */}
      <AddressModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveAddress}
        address={editingAddress}
        entityType={ENTITY_TYPES.USER}
        entityId="example-user-id"
      />
    </div>
  );
};

export default AddressExample;
