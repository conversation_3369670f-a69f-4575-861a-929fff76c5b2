'use client';

import React from 'react';

interface Step {
  id: number;
  label: string;
  completed?: boolean;
}

interface ProgressIndicatorProps {
  steps: Step[];
  currentStep: number;
  className?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
  className = ''
}) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
              step.id === currentStep
                ? 'bg-primary text-white'
                : step.id < currentStep || step.completed
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
            }`}>
              {step.id < currentStep || step.completed ? (
                <i className="ri-check-line"></i>
              ) : (
                step.id
              )}
            </div>
            {index < steps.length - 1 && (
              <div className={`w-12 h-0.5 mx-2 transition-colors ${
                step.id < currentStep || step.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
              }`}></div>
            )}
          </div>
        ))}
      </div>
      
      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
        {steps.map((step) => (
          <span key={step.id} className="text-center max-w-20 truncate">
            {step.label}
          </span>
        ))}
      </div>
    </div>
  );
};

export default ProgressIndicator;
