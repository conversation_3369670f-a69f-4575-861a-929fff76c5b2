import { User } from "./user";

export interface InvoiceItem {
  item_id: string;
  description: string;
  quantity: number;
  unit_price: number;
}

export interface Invoice {
  invoice_id?: string;
  client_id?: string;
  invoice_number: string;
  amount: number;
  balance?: number;
  status: string;
  entity_type?: string;
  entity_id?: string;
  issue_date: string;
  due_date: string;
  description: string;
  items?: InvoiceItem[];
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
  client?: Client
}


export interface Client {
  applicant_id?: string;
  business_registration_number?: string;
  created_at?: string;
  created_by?: string;
  date_incorporation?: string;
  deleted_at?: string | null;
  email?: string;
  fax?: string;
  level_of_insurance_cover?: string;
  name?: string;
  phone?: string;
  place_incorporation?: string;
  tpin?: string;
  updated_at?: string;
  updated_by?: string | null;
  website?: string;
}

export interface CreateInvoiceDto {
  client_id: string;
  amount: number;
  entity_type: string;
  entity_id: string;
  description: string;
  due_date: string;
  items?: InvoiceItem[];
}

export interface UpdateInvoiceDto {
  amount?: number;
  status?: string;
  description?: string;
  due_date?: string;
  items?: InvoiceItem[];
}

export interface InvoiceFilters {
  status?: string;
  entity_type?: string;
  entity_id?: string;
  client_id?: string;
  search?: string;
}






export interface Payment {
  payment_id: string;
  invoice_id?: string;
  invoice_number: string;
  amount: number;
  currency: string;
  payment_type: string;
  description: string;
  status: 'pending' | 'approved' | 'overdue' | 'canceled';
  due_date: string;
  paid_date?: string;
  issue_date: string;
  payment_method?: string;
  transaction_reference?: string;
  notes?: string;
  entity_type?: string;
  entity_id?: string;
  user_id: string;
  created_by: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  user?: User;
}


export interface PaymentFilters {
  status?: string;
  payment_type?: string;
  dateRange?: string;
  search?: string;
  entity_type?: string;
}

export interface PaymentStatistics {
  total: number;
  pending: number;
  paid: number;
  overdue: number;
  cancelled: number;
  totalAmount: number;
  pendingAmount: number;
  paidAmount: number;
  overdueAmount: number;
}