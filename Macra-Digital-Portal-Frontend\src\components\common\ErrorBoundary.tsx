'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { requestManager } from '@/utils/requestManager';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  isRetrying: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });

    // Log request manager status for debugging
    console.log('Request Manager Status:', requestManager.getStatus());
  }

  handleRetry = async () => {
    const { retryCount } = this.state;

    if (retryCount >= 3) {
      console.warn('Max retry attempts reached');
      return;
    }

    this.setState({ isRetrying: true, retryCount: retryCount + 1 });

    // Clear request queue if it's a rate limit error
    if (this.isRateLimitError()) {
      requestManager.clearQueue();
    }

    // Wait before retrying
    const delay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s
    await new Promise(resolve => setTimeout(resolve, delay));

    // Reset error state and retry
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isRetrying: false
    });
  };

  isRateLimitError = (): boolean => {
    const error = this.state.error;
    return !!(
      error?.message?.includes('429') ||
      error?.message?.includes('Rate limit') ||
      error?.message?.includes('Too Many Requests') ||
      (error as any)?.status === 429 ||
      (error as any)?.isRateLimit
    );
  };

  getRetryAfterTime = (): number => {
    const error = this.state.error as any;
    if (error?.retryAfter) {
      return parseInt(error.retryAfter) * 1000;
    }
    return 60000; // Default 1 minute
  };

  render() {
    if (this.state.hasError) {
      const isRateLimitError = this.isRateLimitError();
      const { retryCount, isRetrying } = this.state;
      const canRetry = retryCount < 3;

      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                isRateLimitError 
                  ? 'bg-yellow-100 dark:bg-yellow-900/20' 
                  : 'bg-red-100 dark:bg-red-900/20'
              }`}>
                <i className={`text-2xl ${
                  isRateLimitError 
                    ? 'ri-time-line text-yellow-600 dark:text-yellow-400' 
                    : 'ri-error-warning-line text-red-600 dark:text-red-400'
                }`}></i>
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {isRateLimitError ? 'Rate Limit Exceeded' : 'Something went wrong'}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isRateLimitError 
                    ? 'Too many requests have been made. Please wait a moment before trying again.'
                    : 'An unexpected error occurred while loading the application.'
                  }
                </p>
              </div>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <div className="mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
                <details>
                  <summary className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                    Error Details (Development)
                  </summary>
                  <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                    <p><strong>Error:</strong> {this.state.error?.message}</p>
                    {this.state.errorInfo && (
                      <pre className="mt-2 whitespace-pre-wrap">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </div>
                </details>
              </div>
            )}

            <div className="flex space-x-3">
              {isRateLimitError && canRetry ? (
                <button
                  onClick={this.handleRetry}
                  disabled={isRetrying}
                  className="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isRetrying ? (
                    <>
                      <i className="ri-loader-4-line mr-2 animate-spin"></i>
                      Retrying... ({retryCount}/3)
                    </>
                  ) : (
                    <>
                      <i className="ri-refresh-line mr-2"></i>
                      Retry ({retryCount}/3)
                    </>
                  )}
                </button>
              ) : (
                <button
                  onClick={() => window.location.reload()}
                  className="flex-1 bg-primary text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-primary-dark transition-colors"
                >
                  <i className="ri-refresh-line mr-2"></i>
                  Reload Page
                </button>
              )}
              <button
                onClick={() => window.history.back()}
                className="flex-1 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 py-2 px-4 rounded-md text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                Go Back
              </button>
            </div>

            {isRateLimitError && (
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                <div className="flex items-start">
                  <i className="ri-information-line text-blue-600 dark:text-blue-400 text-sm mr-2 mt-0.5"></i>
                  <div className="text-xs text-blue-700 dark:text-blue-300">
                    <p className="font-medium mb-1">What happened?</p>
                    <p>
                      The application made too many requests to the server in a short period. 
                      This is a protective measure to ensure system stability.
                    </p>
                    <p className="mt-2">
                      <strong>What to do:</strong> Wait a few minutes and try again. 
                      The system will automatically allow new requests after the rate limit period expires.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
