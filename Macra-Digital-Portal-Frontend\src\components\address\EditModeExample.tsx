// Example: Testing SequentialAddressBuilder in Edit Mode
// This demonstrates how the refactored component handles existing address data

'use client';

import React, { useState } from 'react';
import SequentialAddressBuilder from './SequentialAddressBuilder';
import { SequentialAddressData } from '@/types/address_types';

const EditModeExample: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<string>('complete-malawi');
  const [addressData, setAddressData] = useState<Partial<SequentialAddressData>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Test scenarios for edit mode
  const scenarios = {
    'complete-malawi': {
      name: 'Complete Malawi Address',
      data: {
        country: 'Malawi',
        region: 'Northern',
        district: 'Karonga',
        location: 'Karonga Boma',
        postal_code: '101',
        address_line_1: '123 Main Street',
        address_line_2: 'Apartment 4B'
      }
    },
    'partial-malawi': {
      name: 'Partial Malawi Address (Missing Region/District)',
      data: {
        country: 'Malawi',
        region: '',
        district: '',
        location: '',
        postal_code: '101',
        address_line_1: '123 Main Street',
        address_line_2: ''
      }
    },
    'international': {
      name: 'International Address',
      data: {
        country: 'United States',
        region: '',
        district: '',
        location: 'New York',
        postal_code: '10001',
        address_line_1: '123 Broadway',
        address_line_2: 'Suite 456'
      }
    },
    'empty': {
      name: 'New Address (Empty)',
      data: {
        country: '',
        region: '',
        district: '',
        location: '',
        postal_code: '',
        address_line_1: '',
        address_line_2: ''
      }
    }
  };

  const handleScenarioChange = (scenario: string) => {
    setSelectedScenario(scenario);
    setAddressData({});
    setErrors({});
  };

  const handleAddressComplete = (address: SequentialAddressData) => {
    console.log('Address completed:', address);
    setAddressData(address);
  };

  const handleAddressChange = (address: Partial<SequentialAddressData>) => {
    console.log('Address changed:', address);
    setAddressData(address);
  };

  const handleFieldChange = (field: string, value: string) => {
    console.log(`Field changed: ${field} = ${value}`);
  };

  const currentScenario = scenarios[selectedScenario as keyof typeof scenarios];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          SequentialAddressBuilder Edit Mode Testing
        </h1>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          This example demonstrates how the SequentialAddressBuilder handles different edit mode scenarios.
          Select a scenario below to see how the component initializes and behaves with existing data.
        </p>

        {/* Scenario Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Test Scenario
          </label>
          <select
            value={selectedScenario}
            onChange={(e) => handleScenarioChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            {Object.entries(scenarios).map(([key, scenario]) => (
              <option key={key} value={key}>
                {scenario.name}
              </option>
            ))}
          </select>
        </div>

        {/* Current Scenario Info */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
            Current Scenario: {currentScenario.name}
          </h3>
          <pre className="text-xs text-blue-700 dark:text-blue-400 overflow-x-auto">
            {JSON.stringify(currentScenario.data, null, 2)}
          </pre>
        </div>

        {/* SequentialAddressBuilder */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Address Builder
          </h3>
          
          <SequentialAddressBuilder
            key={selectedScenario} // Force re-mount when scenario changes
            initialData={currentScenario.data}
            onAddressComplete={handleAddressComplete}
            onAddressChange={handleAddressChange}
            onFieldChange={handleFieldChange}
            validationErrors={errors}
            disabled={false}
            className="space-y-4"
          />
        </div>

        {/* Current State Display */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-700 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Current Address Data
          </h3>
          <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-x-auto">
            {JSON.stringify(addressData, null, 2)}
          </pre>
        </div>

        {/* Testing Instructions */}
        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-2">
            Testing Instructions
          </h3>
          <ul className="text-xs text-yellow-700 dark:text-yellow-400 space-y-1">
            <li>• <strong>Complete Malawi Address:</strong> All dropdowns should be pre-populated and address lines enabled</li>
            <li>• <strong>Partial Malawi Address:</strong> Address lines should remain enabled, dropdowns should load based on postal code</li>
            <li>• <strong>International Address:</strong> All fields should be enabled immediately, no dropdown loading</li>
            <li>• <strong>New Address:</strong> Standard behavior, fields enable progressively</li>
            <li>• Check browser console for detailed logging of edit mode initialization</li>
            <li>• Try changing dropdown values to see intelligent field clearing behavior</li>
          </ul>
        </div>

        {/* Expected Behaviors */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
              ✅ Expected Behaviors
            </h4>
            <ul className="text-xs text-green-700 dark:text-green-400 space-y-1">
              <li>• Dropdowns pre-populate in edit mode</li>
              <li>• Address lines stay enabled when they have data</li>
              <li>• Fields only clear when logically necessary</li>
              <li>• Smooth initialization without flickering</li>
              <li>• Console shows edit mode detection</li>
            </ul>
          </div>

          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <h4 className="text-sm font-medium text-red-800 dark:text-red-300 mb-2">
              ❌ Issues to Watch For
            </h4>
            <ul className="text-xs text-red-700 dark:text-red-400 space-y-1">
              <li>• Address lines getting disabled unexpectedly</li>
              <li>• Dropdowns not loading in edit mode</li>
              <li>• Fields clearing when they shouldn't</li>
              <li>• Multiple initialization attempts</li>
              <li>• Console errors during dropdown loading</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditModeExample;
