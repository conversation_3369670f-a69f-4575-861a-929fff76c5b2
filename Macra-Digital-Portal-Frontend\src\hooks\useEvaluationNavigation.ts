'use client';

import { useMemo } from 'react';
import { 
  getStepsByLicenseTypeCode,
  isLicenseTypeCodeSupported,
  getLicenseTypeStepConfig,
  StepConfig 
} from '@/config/licenseTypeStepConfig';

interface UseEvaluationNavigationReturn {
  getSteps: () => string[];
  getNextStep: (currentStep: string) => string | null;
  getPreviousStep: (currentStep: string) => string | null;
  getCurrentStepIndex: (currentStep: string) => number;
  getTotalSteps: () => number;
  isFirstStep: (currentStep: string) => boolean;
  isLastStep: (currentStep: string) => boolean;
  isStepValid: (stepRoute: string) => boolean;
}

export const useEvaluationNavigation = (licenseTypeCode: string): UseEvaluationNavigationReturn => {
  // Get steps based on license type code
  const steps = useMemo(() => {
    let licenseSteps: StepConfig[] = [];

    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      console.log('✅ Using optimized steps for supported license type:', licenseTypeCode);
      licenseSteps = getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      console.log('⚠️ Using fallback steps for license type:', licenseTypeCode);
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      licenseSteps = config.steps;
    }

    console.log(`🔍 Evaluation steps for ${licenseTypeCode}:`, licenseSteps.map(step => step.route));
    return licenseSteps;
  }, [licenseTypeCode]);

  const getSteps = () => {
    return steps.map(step => step.route);
  };

  const isStepValid = (stepRoute: string): boolean => {
    return steps.some(step => step.route === stepRoute);
  };

  const getNextStep = (currentStep: string) => {
    const currentIndex = steps.findIndex(step => step.route === currentStep);
    if (currentIndex >= 0 && currentIndex < steps.length - 1) {
      return steps[currentIndex + 1].route;
    }
    return null;
  };

  const getPreviousStep = (currentStep: string) => {
    const currentIndex = steps.findIndex(step => step.route === currentStep);
    if (currentIndex > 0) {
      return steps[currentIndex - 1].route;
    }
    return null;
  };

  const getCurrentStepIndex = (currentStep: string) => {
    return steps.findIndex(step => step.route === currentStep);
  };

  const getTotalSteps = () => {
    return steps.length;
  };

  const isFirstStep = (currentStep: string) => {
    return getCurrentStepIndex(currentStep) === 0;
  };

  const isLastStep = (currentStep: string) => {
    return getCurrentStepIndex(currentStep) === steps.length - 1;
  };

  return {
    getSteps,
    getNextStep,
    getPreviousStep,
    getCurrentStepIndex,
    getTotalSteps,
    isFirstStep,
    isLastStep,
    isStepValid,
  };
};
