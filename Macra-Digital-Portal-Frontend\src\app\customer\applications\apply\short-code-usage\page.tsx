'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { ShortCodeUsage } from '@/components/customer/application';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { shortcodeService } from '@/services/shortcodeService';
import { useApplicationData } from '@/hooks/useApplicationData';
import { ShortcodeFormData, DEFAULT_SHORTCODE_FORM_DATA } from '@/types/shortcode';

const ShortCodeUsagePage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  
  // Get URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'short-code-usage',
    licenseCategoryId,
    applicationId
  });

  // Application data hook
  const {
    application: applicationData,
    loading: applicationLoading,
    error: applicationError,
    saveFormData: updateApplicationData
  } = useApplicationData({
    applicationId,
    stepName: 'short-code-usage',
    autoLoad: true
  });

  // Form data state
  const [formData, setFormData] = useState<ShortcodeFormData>(DEFAULT_SHORTCODE_FORM_DATA);
  const [isFormValid, setIsFormValid] = useState(false);

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!licenseCategoryId || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load existing shortcode data if application exists
        if (applicationId && applicationData?.shortcode_data) {
          console.log('Loading existing shortcode data:', applicationData.shortcode_data);
          setFormData({
            ...DEFAULT_SHORTCODE_FORM_DATA,
            ...applicationData.shortcode_data
          });
        }

      } catch (err: any) {
        console.error('Error loading shortcode data:', err);
        setError(err.message || 'Failed to load shortcode information');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [licenseCategoryId, applicationId, applicationData, authLoading]);

  // Handle form data changes
  const handleDataChange = useCallback((data: ShortcodeFormData) => {
    setFormData(data);
    setHasUnsavedChanges(true);
    setSuccessMessage(null);
  }, []);

  // Handle validation changes
  const handleValidationChange = useCallback((isValid: boolean) => {
    setIsFormValid(isValid);
  }, []);

  // Save data
  const saveData = useCallback(async (showSuccessMessage = true) => {
    if (!licenseCategoryId || !applicationId) return false;

    try {
      setIsSaving(true);
      setError(null);

      console.log('Saving shortcode data:', formData);

      // Update application with shortcode data
      await updateApplicationData('short-code-usage', formData);

      setHasUnsavedChanges(false);
      
      if (showSuccessMessage) {
        setSuccessMessage('Short code usage information saved successfully');
        setTimeout(() => setSuccessMessage(null), 3000);
      }

      return true;
    } catch (err: any) {
      console.error('Error saving shortcode data:', err);
      setError(err.message || 'Failed to save shortcode information');
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [licenseCategoryId, applicationId, formData, updateApplicationData]);

  // Handle next step
  const handleNext = useCallback(async () => {
    if (!isFormValid) {
      setError('Please complete all required fields before proceeding');
      return;
    }

    const saved = await saveData(false);
    if (saved) {
      dynamicHandleNext();
    }
  }, [isFormValid, saveData, dynamicHandleNext]);

  // Handle previous step
  const handlePrevious = useCallback(async () => {
    if (hasUnsavedChanges) {
      await saveData(false);
    }
    dynamicHandlePrevious();
  }, [hasUnsavedChanges, saveData, dynamicHandlePrevious]);

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges || !isFormValid) return;

    const autoSaveTimer = setTimeout(() => {
      saveData(false);
    }, 2000);

    return () => clearTimeout(autoSaveTimer);
  }, [hasUnsavedChanges, isFormValid, saveData]);

  // Loading state
  if (authLoading || isLoading || applicationLoading) {
    return (
      <CustomerLayout>
        <ApplicationLayout
          title="Short Code Usage"
          subtitle="Loading..."
          currentStep="short-code-usage"
          licenseCategoryId={licenseCategoryId}
          applicationId={applicationId}
        >
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading short code usage information...</span>
          </div>
        </ApplicationLayout>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        title="Short Code Usage"
        subtitle="Provide details about how you intend to use the short code service"
        currentStep="short-code-usage"
        licenseCategoryId={licenseCategoryId}
        applicationId={applicationId}
        onNext={handleNext}
        onPrevious={handlePrevious}
        nextLabel={`Continue to ${nextStep?.name || 'Next Step'}`}
        isNextDisabled={!isFormValid || isSaving}
        isSaving={isSaving}
        hasUnsavedChanges={hasUnsavedChanges}
      >
        {/* Success Message */}
        {successMessage && (
          <FormMessages
            type="success"
            messages={[successMessage]}
            className="mb-6"
          />
        )}

        {/* Error Message */}
        {(error || applicationError) && (
          <FormMessages
            type="error"
            messages={[error || applicationError || 'An error occurred']}
            className="mb-6"
          />
        )}

        {/* Loading Warning */}
        {loadingWarning && (
          <FormMessages
            type="warning"
            messages={[loadingWarning]}
            className="mb-6"
          />
        )}

        {/* Short Code Usage Form */}
        <ShortCodeUsage
          initialData={formData}
          onDataChange={handleDataChange}
          onValidationChange={handleValidationChange}
          disabled={isSaving}
          showValidation={true}
        />
      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ShortCodeUsagePage;
