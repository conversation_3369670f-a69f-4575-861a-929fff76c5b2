'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';

interface ResourceCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  borderColor: string;
  route: string;
  isAvailable: boolean;
  comingSoon?: boolean;
}

const ResourcesPage: React.FC = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');

  const resources: ResourceCard[] = [
    {
      id: 'tariffs',
      title: 'Tariffs',
      description: 'View current tariff structures, pricing information, and fee schedules for various MACRA services and licenses.',
      icon: 'ri-money-dollar-circle-line',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      route: '/customer/resources/tariffs',
      isAvailable: true
    },
    {
      id: 'premises',
      title: 'Premises',
      description: 'Access information about premises requirements, facility standards, and location compliance for telecommunications operations.',
      icon: 'ri-building-line',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      route: '/customer/resources/premises',
      isAvailable: true
    },
    {
      id: 'spectrum',
      title: 'Spectrum Request',
      description: 'Submit spectrum allocation requests, view available frequencies, and manage your spectrum assignments.',
      icon: 'ri-radio-line',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      route: '/customer/resources/spectrum-request',
      isAvailable: true
    },
    {
      id: 'promotion',
      title: 'Promotion Management',
      description: 'Manage promotional campaigns, submit marketing materials for approval, and track promotion compliance.',
      icon: 'ri-megaphone-line',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      route: '/customer/resources/promotion-management',
      isAvailable: true
    }
  ];

  const filteredResources = resources.filter(resource =>
    resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleResourceClick = (resource: ResourceCard) => {
    if (resource.isAvailable && !resource.comingSoon) {
      router.push(resource.route);
    }
  };



  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Resources & Services
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Access essential resources, submit requests, and manage your telecommunications services with MACRA's comprehensive digital platform.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400 text-xl"></i>
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                placeholder="Search resources..."
              />
            </div>
          </div>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 mb-8">
          {filteredResources.map((resource) => (
            <div
              key={resource.id}
              onClick={() => handleResourceClick(resource)}
              className={`relative group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl ${
                resource.isAvailable && !resource.comingSoon
                  ? 'cursor-pointer'
                  : 'cursor-not-allowed opacity-75'
              }`}
            >
              <div className={`p-6 rounded-xl border-2 ${resource.borderColor} ${resource.bgColor} dark:bg-gray-800 dark:border-gray-600 h-full flex flex-col transition-all duration-300 group-hover:shadow-lg`}>
                {/* Icon and Title */}
                <div className="flex items-start space-x-4 mb-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-lg ${resource.bgColor} dark:bg-gray-700 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${resource.icon} text-2xl ${resource.color} dark:text-gray-300`}></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {resource.title}
                    </h3>
                    {resource.comingSoon && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                        Coming Soon
                      </span>
                    )}
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed flex-1 mb-4">
                  {resource.description}
                </p>

                {/* Action Button */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {resource.isAvailable && !resource.comingSoon ? (
                      <span className="inline-flex items-center text-sm font-medium text-green-600 dark:text-green-400">
                        <i className="ri-check-circle-line mr-1"></i>
                        Available
                      </span>
                    ) : (
                      <span className="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400">
                        <i className="ri-time-line mr-1"></i>
                        Coming Soon
                      </span>
                    )}
                  </div>

                  {resource.isAvailable && !resource.comingSoon && (
                    <div className="flex items-center text-blue-600 dark:text-blue-400 group-hover:translate-x-1 transition-transform duration-300">
                      <span className="text-sm font-medium mr-1">Access</span>
                      <i className="ri-arrow-right-line"></i>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredResources.length === 0 && (
          <div className="text-center py-12">
            <i className="ri-search-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No resources found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search terms or browse all available resources.
            </p>
          </div>
        )}

        {/* Additional Information Section */}
        <div className="mt-12 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-200 dark:border-gray-600">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <i className="ri-information-line text-2xl text-blue-600 dark:text-blue-400"></i>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Need Help?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                If you need assistance with any of these resources or have questions about MACRA services, our support team is here to help.
              </p>
              <div className="flex flex-wrap gap-3">
                <button className="inline-flex items-center px-4 py-2 border border-blue-300 dark:border-blue-600 rounded-lg text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-200">
                  <i className="ri-phone-line mr-2"></i>
                  Contact Support
                </button>
                <button className="inline-flex items-center px-4 py-2 border border-blue-300 dark:border-blue-600 rounded-lg text-sm font-medium text-blue-700 dark:text-blue-300 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-200">
                  <i className="ri-book-line mr-2"></i>
                  Documentation
                </button>
              </div>
            </div>
          </div>
        </div>

      </div>
    </CustomerLayout>
  );
};

export default ResourcesPage;