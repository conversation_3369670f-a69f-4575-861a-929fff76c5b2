'use client';

import React, { useState, useEffect } from 'react';
import { Search, Shield, AlertCircle, Info } from 'lucide-react';
import { VerificationFormData, LicenseNumberValidation, VerificationCodeValidation } from '@/types/public';

interface LicenseVerificationFormProps {
  onSubmit: (data: VerificationFormData) => void;
  loading?: boolean;
  initialLicenseNumber?: string;
  initialVerificationCode?: string;
}

export default function LicenseVerificationForm({
  onSubmit,
  loading = false,
  initialLicenseNumber = '',
  initialVerificationCode = ''
}: LicenseVerificationFormProps) {
  const [formData, setFormData] = useState<VerificationFormData>({
    licenseNumber: initialLicenseNumber,
    verificationCode: initialVerificationCode
  });

  const [validation, setValidation] = useState({
    licenseNumber: { isValid: true, error: '' },
    verificationCode: { isValid: true, error: '' }
  });

  const [showVerificationCode, setShowVerificationCode] = useState(false);

  // Validate license number format (LIC-YYYY-MM-NNN)
  const validateLicenseNumber = (licenseNumber: string): LicenseNumberValidation => {
    if (!licenseNumber.trim()) {
      return { isValid: false, error: 'License number is required' };
    }

    const pattern = /^LIC-\d{4}-\d{2}-\d{3}$/;
    if (!pattern.test(licenseNumber)) {
      return { 
        isValid: false, 
        error: 'Invalid format. Expected: LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001)' 
      };
    }

    return { isValid: true };
  };

  // Validate verification code format (12 characters, alphanumeric, uppercase)
  const validateVerificationCode = (code: string): VerificationCodeValidation => {
    if (!code.trim()) {
      return { isValid: true }; // Optional field
    }

    const pattern = /^[A-Z0-9]{12}$/;
    if (!pattern.test(code)) {
      return { 
        isValid: false, 
        error: 'Verification code must be 12 characters (letters and numbers only)' 
      };
    }

    return { isValid: true };
  };

  // Handle license number change with real-time validation
  const handleLicenseNumberChange = (value: string) => {
    // Auto-format as user types
    let formatted = value.toUpperCase().replace(/[^A-Z0-9-]/g, '');
    
    // Add hyphens automatically
    if (formatted.length > 3 && !formatted.includes('-')) {
      formatted = 'LIC-' + formatted.substring(3);
    }
    if (formatted.length > 8 && formatted.split('-').length === 2) {
      const parts = formatted.split('-');
      formatted = parts[0] + '-' + parts[1].substring(0, 4) + '-' + parts[1].substring(4, 6) + '-' + parts[1].substring(6);
    }

    setFormData(prev => ({ ...prev, licenseNumber: formatted }));
    
    // Validate on change
    const licenseValidation = validateLicenseNumber(formatted);
    setValidation(prev => ({ ...prev, licenseNumber: licenseValidation }));
  };

  // Handle verification code change
  const handleVerificationCodeChange = (value: string) => {
    const formatted = value.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 12);
    setFormData(prev => ({ ...prev, verificationCode: formatted }));
    
    // Validate on change
    const codeValidation = validateVerificationCode(formatted);
    setValidation(prev => ({ ...prev, verificationCode: codeValidation }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const licenseValidation = validateLicenseNumber(formData.licenseNumber);
    const codeValidation = validateVerificationCode(formData.verificationCode || '');
    
    setValidation({
      licenseNumber: licenseValidation,
      verificationCode: codeValidation
    });

    // Submit if valid
    if (licenseValidation.isValid && codeValidation.isValid) {
      onSubmit({
        licenseNumber: formData.licenseNumber,
        verificationCode: formData.verificationCode || undefined
      });
    }
  };

  // Update form when initial values change
  useEffect(() => {
    setFormData({
      licenseNumber: initialLicenseNumber,
      verificationCode: initialVerificationCode
    });
  }, [initialLicenseNumber, initialVerificationCode]);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Shield className="h-6 w-6 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900">Verify License</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* License Number Input */}
        <div>
          <label htmlFor="licenseNumber" className="block text-sm font-medium text-gray-700 mb-2">
            License Number *
          </label>
          <div className="relative">
            <input
              type="text"
              id="licenseNumber"
              value={formData.licenseNumber}
              onChange={(e) => handleLicenseNumberChange(e.target.value)}
              placeholder="LIC-2024-01-001"
              className={`w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                !validation.licenseNumber.isValid ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={loading}
            />
            <Search className="absolute right-3 top-3 h-5 w-5 text-gray-400" />
          </div>
          {!validation.licenseNumber.isValid && (
            <div className="flex items-center space-x-1 mt-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{validation.licenseNumber.error}</span>
            </div>
          )}
          <p className="text-xs text-gray-500 mt-1">
            Format: LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001)
          </p>
        </div>

        {/* Verification Code Toggle */}
        <div>
          <button
            type="button"
            onClick={() => setShowVerificationCode(!showVerificationCode)}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 text-sm"
          >
            <Info className="h-4 w-4" />
            <span>
              {showVerificationCode ? 'Hide' : 'Add'} verification code (optional)
            </span>
          </button>
        </div>

        {/* Verification Code Input */}
        {showVerificationCode && (
          <div>
            <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-2">
              Verification Code (Optional)
            </label>
            <input
              type="text"
              id="verificationCode"
              value={formData.verificationCode}
              onChange={(e) => handleVerificationCodeChange(e.target.value)}
              placeholder="ABC123DEF456"
              className={`w-full px-4 py-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                !validation.verificationCode.isValid ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={loading}
              maxLength={12}
            />
            {!validation.verificationCode.isValid && (
              <div className="flex items-center space-x-1 mt-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>{validation.verificationCode.error}</span>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              12-character code for enhanced verification (if available)
            </p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={loading || !validation.licenseNumber.isValid || !validation.verificationCode.isValid}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Verifying...</span>
            </div>
          ) : (
            <div className="flex items-center justify-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Verify License</span>
            </div>
          )}
        </button>
      </form>

      {/* Help Text */}
      <div className="mt-6 p-4 bg-blue-50 rounded-md">
        <div className="flex items-start space-x-2">
          <Info className="h-5 w-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">How to verify a license:</p>
            <ul className="list-disc list-inside space-y-1 text-blue-700">
              <li>Enter the license number in the format LIC-YYYY-MM-NNN</li>
              <li>Optionally add the verification code if available</li>
              <li>Click "Verify License" to check authenticity</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
