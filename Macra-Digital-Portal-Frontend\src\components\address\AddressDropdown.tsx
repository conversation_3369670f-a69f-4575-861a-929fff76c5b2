import { useState, useRef, useEffect } from 'react';
import { Address, AddressDropdownProps } from '@/types/address_types';

function AddressDropdown({
  addresses,
  selectedAddressId,
  onSelect,
  placeholder = 'Select address...',
  disabled = false,
  error = '',
  className = ''
}: AddressDropdownProps) {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selected = addresses.find(addr => addr.address_id === selectedAddressId);

  const formatAddressDisplay = (address: Address): string => {
    const parts = [
      address.address_line_1,
      address.city,
      address.postal_code
    ].filter(Boolean);
    return parts.join(', ');
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setOpen(!open)}
          disabled={disabled}
          className={`w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left
            ${disabled
              ? 'bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-600'
            }
            focus:outline-none focus:ring-2 focus:ring-primary
            ${error
              ? 'border-red-500 ring-2 ring-red-500'
              : open
                ? 'border-red-500 ring-2 ring-red-500'
                : 'border-gray-300 dark:border-gray-600'
            }
          `}
          aria-haspopup="listbox"
          aria-expanded={open}
          aria-invalid={!!error}
        >
          <span className="block truncate">
            {selected ? formatAddressDisplay(selected) : placeholder}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {open && !disabled && (
          <ul
            className="absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto"
            role="listbox"
          >
            {addresses.length > 0 ? (
              addresses.map((address) => (
                <li
                  key={address.address_id}
                  onClick={() => {
                    onSelect(address.address_id);
                    setOpen(false);
                  }}
                  className={`cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 ${
                    selectedAddressId === address.address_id
                      ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 font-medium'
                      : 'text-gray-900 dark:text-gray-100'
                  }`}
                  role="option"
                  aria-selected={selectedAddressId === address.address_id}
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{address.address_line_1}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {[address.city, address.postal_code, address.country].filter(Boolean).join(', ')}
                    </span>
                    {address.address_type && (
                      <span className="text-xs text-blue-600 dark:text-blue-400 capitalize">
                        {address.address_type}
                      </span>
                    )}
                  </div>
                </li>
              ))
            ) : (
              <li className="px-3 py-2 text-gray-500 dark:text-gray-400 text-center">
                No addresses available
              </li>
            )}
          </ul>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
}

export default AddressDropdown;