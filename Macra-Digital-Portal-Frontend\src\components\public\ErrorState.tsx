'use client';

import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  RefreshCw, 
  Home, 
  HelpCircle, 
  Wifi, 
  Server,
  Clock,
  Shield
} from 'lucide-react';
import Link from 'next/link';

interface ErrorStateProps {
  title?: string;
  message?: string;
  type?: 'network' | 'server' | 'not-found' | 'validation' | 'timeout' | 'general';
  onRetry?: () => void;
  retryLabel?: string;
  showHomeButton?: boolean;
  showHelpButton?: boolean;
  className?: string;
  children?: React.ReactNode;
  retryCount?: number;
  maxRetries?: number;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  type = 'general',
  onRetry,
  retryLabel = 'Try Again',
  showHomeButton = true,
  showHelpButton = true,
  className = '',
  children,
  retryCount = 0,
  maxRetries = 3
}) => {
  const [isRetrying, setIsRetrying] = useState(false);

  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: <Wifi className="h-12 w-12" />,
          defaultTitle: 'Connection Problem',
          defaultMessage: 'Unable to connect to the server. Please check your internet connection and try again.',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          iconColor: 'text-blue-600 dark:text-blue-400',
          textColor: 'text-blue-800 dark:text-blue-200'
        };
      case 'server':
        return {
          icon: <Server className="h-12 w-12" />,
          defaultTitle: 'Server Error',
          defaultMessage: 'The server is currently experiencing issues. Please try again in a few moments.',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          iconColor: 'text-red-600 dark:text-red-400',
          textColor: 'text-red-800 dark:text-red-200'
        };
      case 'not-found':
        return {
          icon: <Shield className="h-12 w-12" />,
          defaultTitle: 'Not Found',
          defaultMessage: 'The requested resource could not be found. Please check your input and try again.',
          bgColor: 'bg-gray-50 dark:bg-gray-800/50',
          borderColor: 'border-gray-200 dark:border-gray-700',
          iconColor: 'text-gray-600 dark:text-gray-400',
          textColor: 'text-gray-800 dark:text-gray-200'
        };
      case 'validation':
        return {
          icon: <AlertTriangle className="h-12 w-12" />,
          defaultTitle: 'Invalid Input',
          defaultMessage: 'Please check your input and ensure all required fields are filled correctly.',
          bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          iconColor: 'text-yellow-600 dark:text-yellow-400',
          textColor: 'text-yellow-800 dark:text-yellow-200'
        };
      case 'timeout':
        return {
          icon: <Clock className="h-12 w-12" />,
          defaultTitle: 'Request Timeout',
          defaultMessage: 'The request took too long to complete. Please try again.',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800',
          iconColor: 'text-orange-600 dark:text-orange-400',
          textColor: 'text-orange-800 dark:text-orange-200'
        };
      default:
        return {
          icon: <AlertTriangle className="h-12 w-12" />,
          defaultTitle: 'Something went wrong',
          defaultMessage: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-800',
          iconColor: 'text-red-600 dark:text-red-400',
          textColor: 'text-red-800 dark:text-red-200'
        };
    }
  };

  const handleRetry = async () => {
    if (!onRetry || isRetrying || retryCount >= maxRetries) return;
    
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  const config = getErrorConfig();
  const displayTitle = title || config.defaultTitle;
  const displayMessage = message || config.defaultMessage;
  const canRetry = onRetry && retryCount < maxRetries;

  return (
    <div className={`max-w-2xl mx-auto ${className}`}>
      <div className={`${config.bgColor} ${config.borderColor} border rounded-xl p-8 text-center`}>
        {/* Error Icon */}
        <div className="flex justify-center mb-6">
          <div className={`${config.iconColor} animate-pulse`}>
            {config.icon}
          </div>
        </div>

        {/* Error Title */}
        <h2 className={`text-xl font-semibold ${config.textColor} mb-4`}>
          {displayTitle}
        </h2>

        {/* Error Message */}
        <p className={`${config.textColor} mb-6 leading-relaxed`}>
          {displayMessage}
        </p>

        {/* Retry Information */}
        {retryCount > 0 && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Attempt {retryCount} of {maxRetries}
          </p>
        )}

        {/* Custom Content */}
        {children && (
          <div className="mb-6">
            {children}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {/* Retry Button */}
          {canRetry && (
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className="inline-flex items-center space-x-2 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
            >
              <RefreshCw className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
              <span>{isRetrying ? 'Retrying...' : retryLabel}</span>
            </button>
          )}

          {/* Home Button */}
          {showHomeButton && (
            <Link
              href="/public"
              className="inline-flex items-center space-x-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-300 dark:border-gray-600 shadow-sm"
            >
              <Home className="h-4 w-4" />
              <span>Go Home</span>
            </Link>
          )}

          {/* Help Button */}
          {showHelpButton && (
            <Link
              href="/public/help"
              className="inline-flex items-center space-x-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-300 dark:border-gray-600 shadow-sm"
            >
              <HelpCircle className="h-4 w-4" />
              <span>Get Help</span>
            </Link>
          )}
        </div>

        {/* Additional Help Text */}
        {retryCount >= maxRetries && (
          <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              If you continue to experience issues, please contact MACRA support at{' '}
              <a 
                href="tel:+2651770100" 
                className="text-primary hover:text-red-700 transition-colors font-medium"
              >
                +265 1 770 100
              </a>
              {' '}or{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:text-red-700 transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorState;
