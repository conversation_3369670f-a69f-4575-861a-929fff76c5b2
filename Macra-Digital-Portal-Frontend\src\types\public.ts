// Public License Verification Types
export interface PublicLicenseInfo {
  licenseNumber: string;
  licenseType: string;
  status: string;
  issueDate: Date;
  expiryDate: Date;
  organizationName: string;
  isValid: boolean;
  verifiedAt: Date;
}

// Verification Statistics
export interface VerificationStats {
  totalLicenses: number;
  activeLicenses: number;
  expiredLicenses: number;
  suspendedLicenses: number;
}

// License Verification Response
export interface LicenseVerificationResponse {
  success: boolean;
  data?: PublicLicenseInfo;
  message: string;
  error?: string;
}

// License Status Check Response
export interface LicenseStatusResponse {
  success: boolean;
  exists: boolean;
  verifiable: boolean;
  message: string;
}

// Verification Statistics Response
export interface VerificationStatsResponse {
  success: boolean;
  data: VerificationStats;
  message: string;
}

// License Status Enum
export enum PublicLicenseStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  REVOKED = 'revoked',
  UNDER_REVIEW = 'under_review',
}

// Verification Form Data
export interface VerificationFormData {
  licenseNumber: string;
  verificationCode?: string;
}

// Verification Error Types
export enum VerificationErrorType {
  INVALID_FORMAT = 'INVALID_FORMAT',
  INVALID_VERIFICATION_CODE = 'INVALID_VERIFICATION_CODE',
  LICENSE_NOT_FOUND = 'LICENSE_NOT_FOUND',
  VERIFICATION_ERROR = 'VERIFICATION_ERROR',
}

// License Number Validation
export interface LicenseNumberValidation {
  isValid: boolean;
  error?: string;
}

// Verification Code Validation
export interface VerificationCodeValidation {
  isValid: boolean;
  error?: string;
}

// Public API Error Response
export interface PublicApiError {
  success: false;
  message: string;
  error: string;
  statusCode?: number;
}

// Chart Data for Statistics
export interface LicenseStatsChartData {
  name: string;
  value: number;
  color: string;
}

// Verification History (for future use)
export interface VerificationHistory {
  licenseNumber: string;
  verifiedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Public Page Meta Data
export interface PublicPageMeta {
  title: string;
  description: string;
  keywords: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
}
