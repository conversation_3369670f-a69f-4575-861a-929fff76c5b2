'use client';

import React, { useState, useEffect } from 'react';
import { customerApi } from '@/lib/customer-api';
import { Invoice } from '@/types/invoice';
import { formatAmount, formatDate, formatDateTime, formatNumber } from '@/utils/formatters';

interface InvoiceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: Invoice;
}

const InvoiceDetailsModal = ({ isOpen, onClose, invoice }: InvoiceDetailsModalProps) => {
  const [downloading, setDownloading] = useState(false);
  const [payments, setPayments] = useState<any[]>([]);
  const [loadingPayments, setLoadingPayments] = useState(false);

  // Fetch payments for this invoice
  useEffect(() => {
    if (isOpen && invoice?.invoice_id) {
      fetchInvoicePayments();
    }
  }, [isOpen, invoice?.invoice_id]);

  const fetchInvoicePayments = async () => {
    if (!invoice?.invoice_id) return;

    setLoadingPayments(true);
    try {
      const response = await customerApi.getInvoicePayments(invoice.invoice_id);
      setPayments(response.data || response || []);
    } catch (error) {
      setPayments([]);
    } finally {
      setLoadingPayments(false);
    }
  };

  if (!isOpen || !invoice) return null;

  const handleDownload = async () => {
    try {
      setDownloading(true);
      const blob = await customerApi.downloadInvoice(invoice.invoice_id ?? '') ;

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice-${invoice.invoice_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('✅ Invoice downloaded successfully');
    } catch (error) {
      console.error('❌ Error downloading invoice:', error);
      alert('Failed to download invoice. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusClasses = {
      'PAID': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'PENDING': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'OVERDUE': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'CANCELLED': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
      'PROCESSING': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses.PENDING}`}>
        {status}
      </span>
    );
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Invoice Details
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Invoice #{invoice.invoice_number}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Invoice Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Invoice Information
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Invoice Number:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {invoice.invoice_number}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Status:</span>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Amount:</span>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full  ${getStatusColor(invoice.status)} `}>
                      ${formatNumber(invoice.amount)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Issue Date:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatDate(invoice.issue_date)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Due Date:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatDate(invoice.due_date)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="mb-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Description
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {invoice.description}
                </p>
              </div>
            </div>
          </div>

          {/* Invoice Items */}
          {invoice.items && invoice.items.length > 0 && (
            <div className="mb-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Invoice Items
                </h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                    <thead>
                      <tr>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                      {invoice.items.map((item: any, index: number) => (
                        <tr key={index}>
                          <td className="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">
                            {item.description}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900 dark:text-gray-100 text-right">
                            {item.quantity}
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900 dark:text-gray-100 text-right">
                            {formatAmount(item.unit_price, 'MWK')}
                          </td>
                          <td className="px-3 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right">
                            {formatAmount(item.quantity * item.unit_price, 'MWK')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Payments Section */}
          <div className="mb-6">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Payments Made to This Invoice
                </h4>
                {loadingPayments && (
                  <div className="flex items-center text-sm text-gray-500">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading payments...
                  </div>
                )}
              </div>

              {!loadingPayments && payments.length === 0 && (
                <div className="text-center py-6">
                  <div className="text-gray-400 dark:text-gray-500 mb-2">
                    <i className="ri-money-dollar-circle-line text-3xl"></i>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    No payments have been made to this invoice yet.
                  </p>
                </div>
              )}

              {!loadingPayments && payments.length > 0 && (
                <div className="overflow-hidden">
                  {/* Clean Table Layout */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Payment Details
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Date & Method
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Amount
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                        {payments.map((payment, index) => (
                          <tr key={payment.payment_id || index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <td className="px-4 py-4">
                              <div className="flex flex-col">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  Invoice Number #{payment.invoice_number || `P${index + 1}`}
                                </div>
                                {payment.transaction_reference && (
                                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    Ref: <span className="font-mono bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-xs">
                                      {payment.transaction_reference}
                                    </span>
                                  </div>
                                )}
                                {payment.notes && (
                                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-xs truncate" title={payment.notes}>
                                    {payment.notes}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-4">
                              <div className="flex flex-col">
                                <div className="text-sm text-gray-900 dark:text-gray-100">
                                  {payment.payment_date ? formatDate(payment.payment_date) : 'Not specified'}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {payment.payment_method || 'Not specified'}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-4">
                              {getPaymentStatusBadge(payment.status || 'PENDING')}
                            </td>
                            <td className="px-4 py-4 text-right">
                              <div className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                                {formatAmount(payment.amount || 0, payment.currency || 'MWK')}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Payment Summary */}
                  <div className="mt-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                    <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Payment Summary</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Total Payments Made:
                        </span>
                        <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                          {formatAmount(payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0), 'MWK')}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Invoice Total:
                        </span>
                        <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                          {formatAmount(invoice.amount || 0, 'MWK')}
                        </span>
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Outstanding Balance:
                        </span>
                        <span className={`text-sm font-bold ${
                          (invoice.amount || 0) - payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0) <= 0
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          {formatAmount(Math.max(0, (invoice.amount || 0) - payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0)), 'MWK')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDownload}
              disabled={downloading}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {downloading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Downloading...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download Invoice
                </>
              )}
            </button>

            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailsModal;
