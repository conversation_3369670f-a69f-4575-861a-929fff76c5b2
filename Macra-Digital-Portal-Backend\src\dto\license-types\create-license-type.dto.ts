import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLicenseTypeDto {
  @ApiProperty({
    description: 'Name of the license type',
    example: 'Internet Service Provider License',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Code of the license type',
    example: 'Internet Service Provider License',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  code: string;

  @ApiProperty({
    description: 'Description of the license type',
    example: 'License for providing internet services to customers',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

}
