'use client';

import React, { useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import Loader from '@/components/Loader';

export default function DirectLicenseVerificationPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    // Extract license number from URL params
    const licenseNumber = params.licenseNumber as string;
    const verificationCode = searchParams.get('code');

    // Redirect to main verification page with parameters
    const queryParams = new URLSearchParams();
    if (licenseNumber) {
      queryParams.set('license', licenseNumber);
    }
    if (verificationCode) {
      queryParams.set('code', verificationCode);
    }

    const redirectUrl = `/public/verify?${queryParams.toString()}`;
    router.replace(redirectUrl);
  }, [params.licenseNumber, searchParams, router]);

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-64">
      <Loader message="Redirecting to verification..." />
    </div>
  );
}
