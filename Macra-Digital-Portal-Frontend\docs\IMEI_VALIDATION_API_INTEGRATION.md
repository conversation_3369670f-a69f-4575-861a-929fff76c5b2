# IMEI Validation API Integration

This document describes the integration of the `/standards/devices/imei/:imei` endpoint into the IMEI validation system for standards compliance applications.

## Overview

The IMEI validation system now integrates with backend APIs to provide real-time validation of device IMEI numbers against standards databases. This ensures that only valid, non-blacklisted devices are used in standards compliance applications.

## API Endpoints

### Primary Validation Endpoint

**GET `/standards/devices/imei/{imei}`**

Validates a single IMEI number against the standards database.

**Request:**
```
GET /standards/devices/imei/490154203237518
```

**Response (Success):**
```json
{
  "imei": "490154203237518",
  "isValid": true,
  "deviceInfo": {
    "manufacturer": "Apple",
    "model": "iPhone 12",
    "type": "smartphone",
    "tac": "49015420"
  },
  "status": "valid",
  "message": "IMEI is valid and registered",
  "validatedAt": "2024-01-01T12:00:00Z",
  "source": "gsma"
}
```

**Response (Invalid/Blacklisted):**
```json
{
  "imei": "490154203237518",
  "isValid": false,
  "status": "blacklisted",
  "message": "Device is blacklisted",
  "validatedAt": "2024-01-01T12:00:00Z"
}
```

**Status Values:**
- `valid` - IMEI is valid and can be used
- `invalid` - IMEI format or checksum is invalid
- `blacklisted` - Device is blacklisted and cannot be used
- `unknown` - IMEI not found in database

### Application Data Endpoints

**POST `/applications/{applicationId}/imei`**

Save IMEI validation data for an application.

**Request:**
```json
{
  "imei": "490154203237518",
  "validation_result": {
    "imei": "490154203237518",
    "isValid": true,
    "status": "valid",
    "validatedAt": "2024-01-01T12:00:00Z"
  }
}
```

**GET `/applications/{applicationId}/imei`**

Retrieve IMEI validation data for an application.

**PUT `/applications/{applicationId}/imei`**

Update IMEI validation data for an application.

**DELETE `/applications/{applicationId}/imei`**

Delete IMEI validation data for an application.

### Batch Validation

**POST `/devices/imei/batch-validate`**

Validate multiple IMEI numbers in a single request.

**Request:**
```json
{
  "imeis": ["490154203237518", "356938035643809"]
}
```

## Service Implementation

### IMEI Validation Service

The `imeiValidationService` provides a clean interface for interacting with the IMEI validation APIs:

```typescript
import { imeiValidationService } from '@/services/imeiValidationService';

// Validate single IMEI
const result = await imeiValidationService.validateIMEI('490154203237518');

// Save validation data
await imeiValidationService.saveIMEIForApplication(applicationId, {
  imei: '490154203237518',
  validation_result: result
});

// Load existing data
const existingData = await imeiValidationService.getIMEIForApplication(applicationId);
```

### Error Handling

The service handles various error scenarios:

1. **Network Errors**: Throws error for network connectivity issues
2. **404 Not Found**: Returns `unknown` status for IMEIs not in database
3. **400 Bad Request**: Returns `invalid` status for malformed IMEIs
4. **500 Server Error**: Throws error for server issues

### Fallback Mechanism

If API calls fail, the system falls back to localStorage for temporary storage:

```typescript
try {
  await imeiValidationService.saveIMEIForApplication(applicationId, data);
} catch (apiError) {
  // Fallback to localStorage
  localStorage.setItem(`imei_validation_${applicationId}`, JSON.stringify(data));
}
```

## User Interface Integration

### Validation Flow

1. **User Input**: User enters 15-digit IMEI number
2. **Format Validation**: Client-side validation using Luhn algorithm
3. **API Validation**: Real-time validation against standards database
4. **Result Display**: Show validation status and device information
5. **Save Data**: Store validation result with application

### UI Components

**IMEI Input Component** (`src/components/forms/IMEIInput.tsx`):
- Real-time format validation
- Loading state during API validation
- Visual feedback for validation status

**Validation Page** (`src/app/customer/applications/apply/imei-validation/page.tsx`):
- Validation button for API check
- Detailed validation result display
- Device information presentation
- Navigation control based on validation status

### Validation States

The UI displays different states based on validation results:

- **Loading**: Spinner icon during API validation
- **Valid**: Green checkmark with device information
- **Invalid**: Red error icon with error message
- **Blacklisted**: Red warning with blacklist notice
- **Unknown**: Yellow warning for unknown IMEI

## Security Considerations

### Data Protection

- IMEI numbers are sensitive device identifiers
- Validation results are stored securely
- API communications use HTTPS
- Access controlled by authentication

### Rate Limiting

- API calls are rate-limited to prevent abuse
- Batch validation available for multiple IMEIs
- Caching implemented to reduce API calls

## Testing

### Unit Tests

Comprehensive test coverage includes:

- IMEI validation service methods
- Error handling scenarios
- Fallback mechanisms
- API response parsing

**Test File**: `src/services/__tests__/imeiValidationService.test.ts`

### Integration Tests

- End-to-end validation flow
- API endpoint connectivity
- Error scenario handling
- UI state management

## Performance Optimization

### Caching Strategy

- Validation results cached locally
- Reduced API calls for repeated validations
- Cache invalidation after 24 hours

### Batch Processing

- Multiple IMEIs validated in single request
- Reduced network overhead
- Improved user experience for bulk operations

## Monitoring and Logging

### API Monitoring

- Request/response logging
- Error rate tracking
- Performance metrics
- Validation success rates

### User Analytics

- Validation attempt tracking
- Error pattern analysis
- User experience metrics

## Configuration

### Environment Variables

```env
# API Base URL
NEXT_PUBLIC_API_BASE_URL=https://api.macra.mw

# IMEI Validation Settings
IMEI_VALIDATION_TIMEOUT=30000
IMEI_CACHE_DURATION=86400000
```

### Feature Flags

- `ENABLE_IMEI_VALIDATION`: Enable/disable IMEI validation
- `USE_BATCH_VALIDATION`: Enable batch validation for multiple IMEIs
- `FALLBACK_TO_LOCAL`: Enable localStorage fallback

## Troubleshooting

### Common Issues

1. **API Timeout**: Increase timeout settings or check network connectivity
2. **Invalid Response**: Verify API endpoint and response format
3. **Authentication Error**: Check API credentials and permissions
4. **Rate Limit Exceeded**: Implement request throttling or use batch validation

### Debug Steps

1. Check browser console for API errors
2. Verify IMEI format (15 digits, numeric only)
3. Test API endpoint directly
4. Check localStorage fallback data
5. Verify authentication token validity

## Future Enhancements

### Planned Features

1. **Real-time Blacklist Updates**: Live updates from blacklist databases
2. **Device History Tracking**: Track device usage across applications
3. **Bulk Import**: CSV upload for multiple IMEI validation
4. **Advanced Analytics**: Detailed reporting and analytics
5. **Mobile App Integration**: QR code scanning for IMEI input

### API Improvements

1. **Webhook Support**: Real-time notifications for blacklist changes
2. **GraphQL Interface**: More flexible data querying
3. **Caching Headers**: Better client-side caching control
4. **Compression**: Reduced payload sizes for better performance
