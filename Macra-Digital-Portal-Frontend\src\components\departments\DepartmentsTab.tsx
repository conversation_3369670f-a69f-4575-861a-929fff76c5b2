'use client';

import { useState, useEffect, useCallback } from 'react';
import { departmentService, Department, DepartmentsResponse, PaginateQuery } from '../../services/departmentService';
import DataTable from '../common/DataTable';
import ConfirmationModal from '../common/ConfirmationModal';

interface DepartmentsTabProps {
  onEditDepartment: (department: Department) => void;
  onCreateDepartment: () => void;
}

const DepartmentsTab = ({ onEditDepartment, onCreateDepartment }: DepartmentsTabProps) => {
  const [departmentsData, setDepartmentsData] = useState<DepartmentsResponse | null>(null);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const loadDepartments = useCallback(async (query: PaginateQuery = {}) => {
    try {
      setLoading(true);
      setError(null);

      // Set default sorting by code ascending
      const queryWithDefaults = {
        page: 1,
        limit: 50,
        sortBy: ['code:ASC'],
        searchBy: ['code', 'name'],
        ...query,
      };

      const response = await departmentService.getDepartments(queryWithDefaults);
      setDepartmentsData(response);
    } catch (err) {
      console.error('Error loading departments:', err);
      setError('Failed to load departments');
      setDepartmentsData(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadDepartments();
  }, [loadDepartments]);





  const handleDeleteDepartment = (department: Department) => {
    setDepartmentToDelete(department);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!departmentToDelete) return;

    try {
      setIsDeleting(true);
      await departmentService.deleteDepartment(departmentToDelete.department_id);

      // Reload departments after deletion
      await loadDepartments();

      setShowDeleteModal(false);
      setDepartmentToDelete(null);
    } catch (error) {
      console.error('Error deleting department:', error);
      setError('Failed to delete department');
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setDepartmentToDelete(null);
  };

  // Get departments array from response
  const departments = departmentsData?.data || [];



  const departmentColumns = [
    {
      key: 'code',
      label: 'Code',
      render: (value: string) => (
        <span className="font-mono text-sm font-medium text-gray-900 dark:text-gray-100">
          {value}
        </span>
      ),
    },
    {
      key: 'name',
      label: 'Name',
      render: (value: string) => (
        <span className="font-medium text-gray-900 dark:text-gray-100">
          {value}
        </span>
      ),
    },
    {
      key: 'manager',
      label: 'Manager',
      render: (_: unknown, department: Department) => {
        if (department.department_manager) {
          const manager = department.department_manager;
          return (
            <span className="text-gray-900 dark:text-gray-100">
              {manager.first_name} {manager.last_name}
            </span>
          );
        }
        return (
          <span className="text-gray-500 dark:text-gray-400 italic">
            No manager assigned
          </span>
        );
      },
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <span className="text-gray-600 dark:text-gray-400 max-w-xs truncate">
          {value}
        </span>
      ),
    },
    {
      key: 'email',
      label: 'Email',
      render: (value: string) => (
        <span className="text-blue-600 dark:text-blue-400">
          {value}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, department: Department) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditDepartment(department)}
            className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900"
            title="Edit department"
          >
            <i className="ri-edit-line"></i>
          </button>
          <button
            onClick={() => handleDeleteDepartment(department)}
            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900"
            title="Delete department"
          >
            <i className="ri-delete-bin-line"></i>
          </button>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 dark:text-red-400 mb-4">
          <i className="ri-error-warning-line text-4xl"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Error Loading Departments
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <button
          onClick={() => loadDepartments()}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <i className="ri-refresh-line mr-2"></i>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Departments
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage organizational departments and their information.
          </p>
        </div>
        <div>
          <button
            onClick={onCreateDepartment}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <i className="ri-add-line mr-2"></i>
            Add Department
          </button>
        </div>
      </div>

      {/* Departments Table */}
      <DataTable
        columns={departmentColumns}
        data={departmentsData}
        loading={loading}
        onQueryChange={(query) => {
          loadDepartments({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search departments by code or name..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        title="Delete Department"
        message={
          departmentToDelete
            ? `Are you sure you want to delete the department "${departmentToDelete.name}" (${departmentToDelete.code})? This action cannot be undone.`
            : ''
        }
        confirmText="Delete"
        cancelText="Cancel"
        loading={isDeleting}
        confirmVariant="danger"

      />
    </div>
  );
};

export default DepartmentsTab;
