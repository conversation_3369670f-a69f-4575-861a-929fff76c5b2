'use client';

import { useState, useEffect } from 'react';
import { IdentificationType, identificationTypeService, IdentificationTypesResponse } from '../../services/identificationTypeService';
import DataTable from '../common/DataTable';
import { formatDate } from '../../utils/formatters';
import { PaginateQuery } from '@/types';

interface IdentificationTypesTabProps {
  onEditIdentificationType: (identificationType: IdentificationType) => void;
  onCreateIdentificationType: () => void;
  refreshTrigger?: number;
}

const IdentificationTypesTab = ({ onEditIdentificationType, onCreateIdentificationType, refreshTrigger }: IdentificationTypesTabProps) => {
  const [identificationTypesData, setIdentificationTypesData] = useState<IdentificationTypesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadIdentificationTypes({ page: 1, limit: 10 });
  }, [refreshTrigger]);

  const loadIdentificationTypes = async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError('');
      const response = await identificationTypeService.getIdentificationTypes(query);
      setIdentificationTypesData(response);
    } catch (err: any) {
      console.error('Error loading identification types:', err);
      setError(err.response?.data?.message || 'Failed to load identification types');
      // Set empty data structure to prevent undefined errors
      setIdentificationTypesData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (identificationType: IdentificationType) => {
    const usageCount = identificationType.user_identifications?.length || 0;
    let confirmMessage = `Are you sure you want to delete the identification type "${identificationType.name}"?\n\nThis action cannot be undone.`;

    if (usageCount > 0) {
      confirmMessage += `\n\nWarning: This identification type is currently being used by ${usageCount} user(s). Deleting it may affect user profiles.`;
    }

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      await identificationTypeService.deleteIdentificationType(identificationType.identification_type_id);
      await loadIdentificationTypes({ page: 1, limit: 10 }); // Reload the list
    } catch (err: any) {
      console.error('Error deleting identification type:', err);
      const errorMessage = err.response?.data?.message || 'Failed to delete identification type';

      // Check if it's a constraint error (related records exist)
      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {
        setError('Cannot delete this identification type because it is being used by one or more users. Please remove or update the related user identifications first.');
      } else {
        setError(errorMessage);
      }
    }
  };

  // Define columns for DataTable
  const identificationTypeColumns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      searchable: true,
      render: (value: unknown, item: IdentificationType) => (
        <div className="text-sm font-medium text-gray-900 dark:text-white">
          {item.name}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: unknown, item: IdentificationType) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatDate(item.created_at)}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      render: (value: unknown, item: IdentificationType) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditIdentificationType(item)}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            title="Edit identification type"
          >
            <i className="ri-edit-line text-lg"></i>
          </button>
          <button
            onClick={() => handleDelete(item)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Delete identification type"
          >
            <i className="ri-delete-bin-line text-lg"></i>
          </button>
        </div>
      ),
    },
  ];



  return (
    <div className="space-y-6">
      {/* Header with Search and Add Button */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Identification Types</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage identification document types accepted by the system
          </p>
        </div>
        <button
          onClick={onCreateIdentificationType}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Identification Type
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Identification Types Table */}
      <DataTable
        columns={identificationTypeColumns as any}
        data={identificationTypesData as any}
        loading={loading}
        onQueryChange={(query) => {
          loadIdentificationTypes({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search identification types by name..."
        emptyStateMessage="No identification types found"
        emptyStateIcon="ri-id-card-line"
      />
    </div>
  );
};

export default IdentificationTypesTab;
