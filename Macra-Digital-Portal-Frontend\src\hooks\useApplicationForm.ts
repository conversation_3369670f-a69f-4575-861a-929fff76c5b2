import { useState, useEffect, useCallback, useMemo } from 'react';
import { applicationService } from '@/services/applicationService';
import { validateSection } from '@/utils/formValidation';
import { useAuth } from '@/contexts/AuthContext';

interface ApplicationFormState {
  applicationId: string | null;
  formData: Record<string, any>;
  validationErrors: Record<string, any>;
  isSaving: boolean;
  isLoading: boolean;
  lastSaved: string | null;
  hasUnsavedChanges: boolean;
}

interface UseApplicationFormOptions {
  licenseTypeId?: string;
  licenseCategoryId?: string;
  initialApplicationId?: string;
}

export const useApplicationForm = (options: UseApplicationFormOptions) => {
  const { licenseTypeId, licenseCategoryId, initialApplicationId } = options;
  const { user } = useAuth();

  const [state, setState] = useState<ApplicationFormState>({
    applicationId: initialApplicationId || null,
    formData: {},
    validationErrors: {},
    isSaving: false,
    isLoading: false,
    lastSaved: null,
    hasUnsavedChanges: false
  });

  // Load existing application data
  const loadApplication = useCallback(async (applicationId: string) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const application = await applicationService.getApplication(applicationId);

      // Initialize empty form data since backend doesn't store form_data directly
      // Form data will be loaded separately when needed
      setState(prev => ({
        ...prev,
        applicationId: application.application_id,
        formData: {}, // Initialize empty - will be populated as sections are loaded
        isLoading: false
      }));

      console.log('Application loaded:', application);
    } catch (error) {
      console.error('Error loading application:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Create new application
  const createApplication = useCallback(async (applicantData: Record<string, any>) => {
    setState(prev => ({ ...prev, isSaving: true }));

    try {
      if (!user?.user_id) {
        throw new Error('User not authenticated');
      }

      if (!licenseTypeId || !licenseCategoryId) {
        throw new Error('License type and category are required');
      }

      const applicationData = {
        license_type_id: licenseTypeId,
        license_category_id: licenseCategoryId,
        applicant_data: applicantData,
        user_id: user.user_id
      };

      const newApplication = await applicationService.createApplicationWithApplicant(applicationData);
      
      setState(prev => ({
        ...prev,
        applicationId: newApplication.application_id,
        formData: {
          ...prev.formData,
          applicantInfo: applicantData
        },
        isSaving: false,
        lastSaved: new Date().toISOString(),
        hasUnsavedChanges: false
      }));

      return newApplication.application_id;
    } catch (error) {
      setState(prev => ({ ...prev, isSaving: false }));
      throw error;
    }
  }, [licenseTypeId, licenseCategoryId]);

  // Save section data
  const saveSection = useCallback(async (sectionName: string, data: Record<string, any>) => {
    setState(prev => ({ ...prev, isSaving: true }));

    try {
      let currentAppId = state.applicationId;

      // If no application exists and this is the first section, create application
      if (!currentAppId && sectionName === 'applicantInfo') {
        if (!user?.user_id) {
          throw new Error('User not authenticated');
        }

        if (!licenseTypeId || !licenseCategoryId) {
          throw new Error('License type and category are required');
        }

        const applicationData = {
          license_type_id: licenseTypeId,
          license_category_id: licenseCategoryId,
          applicant_data: data,
          user_id: user.user_id
        };

        const newApplication = await applicationService.createApplicationWithApplicant(applicationData);
        currentAppId = newApplication.application_id;
        
        setState(prev => ({
          ...prev,
          applicationId: currentAppId,
          formData: {
            ...prev.formData,
            [sectionName]: data
          },
          isSaving: false,
          lastSaved: new Date().toISOString(),
          hasUnsavedChanges: false
        }));

        return currentAppId;
      } else {
        // Save section data to existing application
        if (!currentAppId) {
          throw new Error('No application ID available for saving section data');
        }

        await applicationService.saveApplicationSection(currentAppId, sectionName, data);
        
        setState(prev => ({
          ...prev,
          formData: {
            ...prev.formData,
            [sectionName]: data
          },
          isSaving: false,
          lastSaved: new Date().toISOString(),
          hasUnsavedChanges: false
        }));

        return currentAppId;
      }
    } catch (error) {
      setState(prev => ({ ...prev, isSaving: false }));
      throw error;
    }
  }, [licenseTypeId, licenseCategoryId, state.applicationId]);

  // Update form data (for real-time changes)
  const updateFormData = useCallback((section: string, field: string, value: any) => {
    setState(prev => ({
      ...prev,
      formData: {
        ...prev.formData,
        [section]: {
          ...prev.formData[section],
          [field]: value
        }
      },
      hasUnsavedChanges: true
    }));
  }, []);

  // Validate form section
  const validateFormSection = useCallback((sectionName: string, data: Record<string, any>) => {
    const validation = validateSection(data, sectionName);
    
    setState(prev => ({
      ...prev,
      validationErrors: {
        ...prev.validationErrors,
        [sectionName]: validation.errors
      }
    }));

    return validation;
  }, []);

  // Load application on mount if ID provided
  useEffect(() => {
    if (initialApplicationId && !state.applicationId) {
      loadApplication(initialApplicationId);
    }
  }, [initialApplicationId, loadApplication, state.applicationId]);

  // Memoize helper functions to prevent re-renders
  const getSectionData = useCallback((sectionName: string) => {
    return state.formData[sectionName] || {};
  }, [state.formData]);

  const getSectionErrors = useCallback((sectionName: string) => {
    return state.validationErrors[sectionName] || {};
  }, [state.validationErrors]);

  const isFormValid = useMemo(() => {
    return Object.values(state.validationErrors).every(sectionErrors => 
      Object.keys(sectionErrors).length === 0
    );
  }, [state.validationErrors]);

  return {
    // State
    applicationId: state.applicationId,
    formData: state.formData,
    validationErrors: state.validationErrors,
    isSaving: state.isSaving,
    isLoading: state.isLoading,
    lastSaved: state.lastSaved,
    hasUnsavedChanges: state.hasUnsavedChanges,

    // Actions
    createApplication,
    saveSection,
    updateFormData,
    validateFormSection,
    loadApplication,

    // Helpers
    getSectionData,
    getSectionErrors,
    isFormValid
  };
};
