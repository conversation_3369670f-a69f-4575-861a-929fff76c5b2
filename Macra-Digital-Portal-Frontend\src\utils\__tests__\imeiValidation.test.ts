import { validateIMEIChecksum, patterns } from '../formValidation';

describe('IMEI Validation', () => {
  describe('validateIMEIChecksum', () => {
    test('should validate correct IMEI numbers', () => {
      // Valid IMEI numbers (using <PERSON><PERSON> algorithm)
      const validIMEIs = [
        '490154203237518', // Example valid IMEI
        '356938035643809', // Another valid IMEI
        '490154203237510'  // Another valid IMEI
      ];

      validIMEIs.forEach(imei => {
        expect(validateIMEIChecksum(imei)).toBe(true);
      });
    });

    test('should reject invalid IMEI numbers', () => {
      const invalidIMEIs = [
        '490154203237519', // Invalid checksum
        '356938035643808', // Invalid checksum
        '123456789012345'  // Invalid checksum
      ];

      invalidIMEIs.forEach(imei => {
        expect(validateIMEIChecksum(imei)).toBe(false);
      });
    });

    test('should reject IMEI numbers with wrong length', () => {
      expect(validateIMEIChecksum('12345')).toBe(false);
      expect(validateIMEIChecksum('1234567890123456')).toBe(false);
      expect(validateIMEIChecksum('')).toBe(false);
    });

    test('should reject IMEI numbers with non-digits', () => {
      expect(validateIMEIChecksum('49015420323751a')).toBe(false);
      expect(validateIMEIChecksum('490-154-203-237-518')).toBe(false);
      expect(validateIMEIChecksum('490 154 203 237 518')).toBe(false);
    });
  });

  describe('IMEI pattern validation', () => {
    test('should match valid 15-digit IMEI format', () => {
      expect(patterns.imei.test('490154203237518')).toBe(true);
      expect(patterns.imei.test('356938035643809')).toBe(true);
    });

    test('should reject invalid IMEI formats', () => {
      expect(patterns.imei.test('12345')).toBe(false);
      expect(patterns.imei.test('1234567890123456')).toBe(false);
      expect(patterns.imei.test('49015420323751a')).toBe(false);
      expect(patterns.imei.test('490-154-203-237-518')).toBe(false);
      expect(patterns.imei.test('')).toBe(false);
    });
  });

  describe('Luhn algorithm implementation', () => {
    test('should correctly calculate checksum for known examples', () => {
      // Test with a known IMEI where we can manually verify the Luhn algorithm
      // IMEI: 490154203237518
      // Digits: 4 9 0 1 5 4 2 0 3 2 3 7 5 1 8
      // Process: 4 18 0 2 5 8 2 0 3 4 3 14 5 2 ?
      // Sum of processed: 4+1+8+0+2+5+8+2+0+3+4+3+1+4+5+2 = 52
      // Check digit: (10 - (52 % 10)) % 10 = 8
      expect(validateIMEIChecksum('490154203237518')).toBe(true);
    });
  });
});

// Helper function to generate a valid IMEI for testing
export const generateValidIMEI = (prefix: string = '490154203237'): string => {
  if (prefix.length !== 14) {
    throw new Error('Prefix must be exactly 14 digits');
  }

  // Calculate the check digit using Luhn algorithm
  const digits = prefix.split('').map(Number);
  let sum = 0;
  
  for (let i = 0; i < 14; i++) {
    let digit = digits[i];
    
    // Double every second digit from right to left (starting from second-to-last)
    if ((14 - i) % 2 === 0) {
      digit *= 2;
      if (digit > 9) {
        digit = Math.floor(digit / 10) + (digit % 10);
      }
    }
    
    sum += digit;
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return prefix + checkDigit.toString();
};
