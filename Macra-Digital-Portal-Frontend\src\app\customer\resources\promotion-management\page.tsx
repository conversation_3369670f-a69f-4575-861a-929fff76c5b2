'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';

interface Promotion {
  id: string;
  title: string;
  description: string;
  type: 'data' | 'voice' | 'sms' | 'bundle' | 'special';
  status: 'active' | 'pending' | 'approved' | 'rejected' | 'expired';
  startDate: string;
  endDate: string;
  targetAudience: string;
  submittedDate: string;
  approvalDate?: string;
}

interface PromotionTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  bgColor: string;
  requirements: string[];
}

const PromotionManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'active' | 'create' | 'templates' | 'history'>('active');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const promotions: Promotion[] = [
    {
      id: 'promo-001',
      title: 'Weekend Data Bonanza',
      description: 'Double data on all weekend purchases',
      type: 'data',
      status: 'active',
      startDate: '2025-01-01',
      endDate: '2025-03-31',
      targetAudience: 'All customers',
      submittedDate: '2024-12-15',
      approvalDate: '2024-12-20'
    },
    {
      id: 'promo-002',
      title: 'Student Voice Package',
      description: 'Discounted voice calls for students',
      type: 'voice',
      status: 'pending',
      startDate: '2025-02-01',
      endDate: '2025-06-30',
      targetAudience: 'Students',
      submittedDate: '2025-01-10'
    },
    {
      id: 'promo-003',
      title: 'SMS Marathon',
      description: 'Unlimited SMS for 7 days',
      type: 'sms',
      status: 'approved',
      startDate: '2025-02-14',
      endDate: '2025-02-21',
      targetAudience: 'Premium customers',
      submittedDate: '2025-01-05',
      approvalDate: '2025-01-12'
    }
  ];

  const templates: PromotionTemplate[] = [
    {
      id: 'data-promo',
      name: 'Data Promotion',
      description: 'Templates for data-based promotional campaigns',
      category: 'data',
      icon: 'ri-wifi-line',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      requirements: [
        'Data allocation details',
        'Validity period',
        'Target customer segment',
        'Pricing structure',
        'Terms and conditions'
      ]
    },
    {
      id: 'voice-promo',
      name: 'Voice Promotion',
      description: 'Templates for voice call promotional offers',
      category: 'voice',
      icon: 'ri-phone-line',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      requirements: [
        'Call rate discounts',
        'Free minutes allocation',
        'Peak/off-peak rates',
        'Geographic coverage',
        'Customer eligibility'
      ]
    },
    {
      id: 'bundle-promo',
      name: 'Bundle Promotion',
      description: 'Templates for combined service packages',
      category: 'bundle',
      icon: 'ri-gift-line',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      requirements: [
        'Service combinations',
        'Package pricing',
        'Subscription terms',
        'Renewal conditions',
        'Cancellation policy'
      ]
    },
    {
      id: 'seasonal-promo',
      name: 'Seasonal Promotion',
      description: 'Templates for holiday and seasonal campaigns',
      category: 'special',
      icon: 'ri-calendar-event-line',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      requirements: [
        'Seasonal theme',
        'Limited time offers',
        'Special pricing',
        'Marketing materials',
        'Compliance documentation'
      ]
    }
  ];

  const promotionTypes = [
    { id: 'all', name: 'All Types', count: promotions.length },
    { id: 'data', name: 'Data', count: promotions.filter(p => p.type === 'data').length },
    { id: 'voice', name: 'Voice', count: promotions.filter(p => p.type === 'voice').length },
    { id: 'sms', name: 'SMS', count: promotions.filter(p => p.type === 'sms').length },
    { id: 'bundle', name: 'Bundle', count: promotions.filter(p => p.type === 'bundle').length },
    { id: 'special', name: 'Special', count: promotions.filter(p => p.type === 'special').length }
  ];

  const filteredPromotions = promotions.filter(promotion => {
    const matchesType = selectedType === 'all' || promotion.type === selectedType;
    const matchesSearch = promotion.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         promotion.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'approved':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'data':
        return 'ri-wifi-line';
      case 'voice':
        return 'ri-phone-line';
      case 'sms':
        return 'ri-message-line';
      case 'bundle':
        return 'ri-gift-line';
      case 'special':
        return 'ri-star-line';
      default:
        return 'ri-megaphone-line';
    }
  };

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-blue-600">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <Link href="/customer/resources" className="hover:text-blue-600">Resources</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Promotion Management</span>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Promotion Management
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Create, manage, and track your promotional campaigns and marketing materials.
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <button 
                onClick={() => setActiveTab('create')}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                <i className="ri-add-line mr-2"></i>
                Create Promotion
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('active')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'active'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-megaphone-line mr-2"></i>
                Active Promotions
              </button>
              <button
                onClick={() => setActiveTab('create')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'create'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-add-line mr-2"></i>
                Create New
              </button>
              <button
                onClick={() => setActiveTab('templates')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'templates'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-file-copy-line mr-2"></i>
                Templates
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-history-line mr-2"></i>
                History
              </button>
            </nav>
          </div>
        </div>

        {/* Active Promotions Tab */}
        {activeTab === 'active' && (
          <div>
            {/* Search and Filter */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-search-line text-gray-400"></i>
                  </div>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Search promotions..."
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {promotionTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} ({type.count})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Promotions Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredPromotions.map((promotion) => (
                <div
                  key={promotion.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-blue-50 dark:bg-gray-700 flex items-center justify-center">
                        <i className={`${getTypeIcon(promotion.type)} text-lg text-blue-600 dark:text-gray-300`}></i>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                          {promotion.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {promotion.description}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(promotion.status)}`}>
                      {promotion.status.charAt(0).toUpperCase() + promotion.status.slice(1)}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">Start Date:</p>
                        <p className="text-gray-600 dark:text-gray-400">{new Date(promotion.startDate).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">End Date:</p>
                        <p className="text-gray-600 dark:text-gray-400">{new Date(promotion.endDate).toLocaleDateString()}</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">Target Audience:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{promotion.targetAudience}</p>
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                          promotion.type === 'data' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          promotion.type === 'voice' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          promotion.type === 'sms' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {promotion.type.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                          <i className="ri-edit-line mr-1"></i>
                          Edit
                        </button>
                        <button className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                          <i className="ri-eye-line mr-1"></i>
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-start space-x-4 mb-4">
                    <div className={`flex-shrink-0 w-12 h-12 rounded-lg ${template.bgColor} dark:bg-gray-700 flex items-center justify-center`}>
                      <i className={`${template.icon} text-2xl ${template.color} dark:text-gray-300`}></i>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {template.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {template.description}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                        Required Information:
                      </p>
                      <ul className="space-y-1">
                        {template.requirements.slice(0, 3).map((req, index) => (
                          <li key={index} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                            <i className="ri-check-line text-green-500 flex-shrink-0"></i>
                            <span>{req}</span>
                          </li>
                        ))}
                        {template.requirements.length > 3 && (
                          <li className="text-sm text-gray-500 dark:text-gray-400 ml-6">
                            +{template.requirements.length - 3} more requirements
                          </li>
                        )}
                      </ul>
                    </div>

                    <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                      <button className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                        <i className="ri-file-copy-line mr-2"></i>
                        Use Template
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Create New Tab */}
        {activeTab === 'create' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
                Create New Promotion
              </h3>
              
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Promotion Title
                  </label>
                  <input
                    type="text"
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter promotion title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Promotion Type
                  </label>
                  <select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select promotion type</option>
                    <option value="data">Data Promotion</option>
                    <option value="voice">Voice Promotion</option>
                    <option value="sms">SMS Promotion</option>
                    <option value="bundle">Bundle Promotion</option>
                    <option value="special">Special Promotion</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                  </label>
                  <textarea
                    rows={4}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe your promotion in detail..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Date
                    </label>
                    <input
                      type="date"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Date
                    </label>
                    <input
                      type="date"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Target Audience
                  </label>
                  <select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select target audience</option>
                    <option value="all">All Customers</option>
                    <option value="new">New Customers</option>
                    <option value="existing">Existing Customers</option>
                    <option value="premium">Premium Customers</option>
                    <option value="students">Students</option>
                    <option value="seniors">Senior Citizens</option>
                  </select>
                </div>

                <div className="flex justify-end space-x-3 pt-6">
                  <button
                    type="button"
                    onClick={() => setActiveTab('active')}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    <i className="ri-save-line mr-2"></i>
                    Save Draft
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
                  >
                    <i className="ri-send-plane-line mr-2"></i>
                    Submit for Approval
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Promotion History
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  All your promotional campaigns and their status
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Promotion
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {promotions.map((promotion) => (
                      <tr key={promotion.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {promotion.title}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {promotion.description}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                            promotion.type === 'data' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                            promotion.type === 'voice' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                            promotion.type === 'sms' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          }`}>
                            <i className={`${getTypeIcon(promotion.type)} mr-1`}></i>
                            {promotion.type.toUpperCase()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <div>
                            <div>{new Date(promotion.startDate).toLocaleDateString()}</div>
                            <div>to {new Date(promotion.endDate).toLocaleDateString()}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(promotion.status)}`}>
                            {promotion.status.charAt(0).toUpperCase() + promotion.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                              <i className="ri-eye-line"></i>
                            </button>
                            <button className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                              <i className="ri-edit-line"></i>
                            </button>
                            <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                              <i className="ri-delete-bin-line"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </CustomerLayout>
  );
};

export default PromotionManagementPage;
