import axios, { AxiosError, AxiosInstance, AxiosResponse } from "axios";
import { processApiResponse } from "./authUtils";


// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Public API Client (no authentication required)
const publicApiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add debug logging to public client (only in development)
publicApiClient.interceptors.request.use(
  (config) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Public API Request:', {
        url: `${config.baseURL}${config.url}`,
        method: config.method,
        headers: config.headers,
        data: config.data
      });
    }
    return config;
  },
  (error) => {
    console.error('Public API Request Error:', error);
    return Promise.reject(error);
  }
);

// Public API response interceptor
publicApiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Public API Response:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      });
    }
    return response;
  },
  (error: AxiosError) => {
    console.error('Public API Response Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

// Public API Service Class
export class PublicApiService {
  public api: AxiosInstance;

  constructor() {
    this.api = publicApiClient;
  }

  // Verify license authenticity
  async verifyLicense(licenseNumber: string, verificationCode?: string) {
    const params = verificationCode ? { code: verificationCode } : {};
    const response = await this.api.get(`/public/verify/${licenseNumber}`, { params });
    return processApiResponse(response);
  }

  // Check license status (lightweight check)
  async checkLicenseStatus(licenseNumber: string) {
    const response = await this.api.get(`/public/verify-status/${licenseNumber}`);
    return processApiResponse(response);
  }

  // Get verification statistics
  async getVerificationStats() {
    const response = await this.api.get('/public/verification-stats');
    return processApiResponse(response);
  }
}

export const publicApi = new PublicApiService();

// Export axios instance for direct use if needed
export { publicApiClient };
