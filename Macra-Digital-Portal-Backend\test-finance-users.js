const mysql = require('mysql2/promise');

async function testFinanceUsers() {
  let connection;
  
  try {
    console.log('🔌 Connecting to database...');
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'macra_db'
    });
    
    console.log('✅ Connected to database');
    
    // Check if finance role exists
    console.log('\n🔍 Checking finance role...');
    const [financeRoles] = await connection.execute(
      'SELECT role_id, name, description FROM roles WHERE name = ? AND deleted_at IS NULL',
      ['finance']
    );
    console.log(`📊 Finance roles found: ${financeRoles.length}`);
    if (financeRoles.length > 0) {
      console.log('📋 Finance role details:', financeRoles[0]);
    }
    
    // Check users with finance role
    console.log('\n👤 Checking users with finance role...');
    const [financeUsers] = await connection.execute(`
      SELECT u.user_id, u.email, u.first_name, u.last_name, u.status, r.name as role_name
      FROM users u
      JOIN user_roles ur ON u.user_id = ur.user_id
      JOIN roles r ON ur.role_id = r.role_id
      WHERE r.name = 'finance' AND u.deleted_at IS NULL AND u.status = 'active'
    `);
    console.log(`📊 Active finance users found: ${financeUsers.length}`);
    financeUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.first_name} ${user.last_name}) - Status: ${user.status}`);
    });
    
    // Check all users and their roles
    console.log('\n👥 Checking all active users and their roles...');
    const [allUsers] = await connection.execute(`
      SELECT u.user_id, u.email, u.first_name, u.last_name, u.status, 
             GROUP_CONCAT(r.name) as roles
      FROM users u
      LEFT JOIN user_roles ur ON u.user_id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.role_id
      WHERE u.deleted_at IS NULL AND u.status = 'active'
      GROUP BY u.user_id, u.email, u.first_name, u.last_name, u.status
      ORDER BY u.created_at DESC
      LIMIT 10
    `);
    console.log(`📊 Active users found: ${allUsers.length}`);
    allUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} - Roles: ${user.roles || 'No roles'} - Status: ${user.status}`);
    });
    
    // Check if 'system' user exists
    console.log('\n🤖 Checking for system user...');
    const [systemUsers] = await connection.execute(
      'SELECT user_id, email, first_name, last_name FROM users WHERE email = ? OR user_id = ? AND deleted_at IS NULL',
      ['<EMAIL>', 'system']
    );
    console.log(`📊 System users found: ${systemUsers.length}`);
    systemUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.user_id})`);
    });
    
    // Check recent tasks
    console.log('\n📋 Checking recent tasks...');
    const [recentTasks] = await connection.execute(`
      SELECT task_id, task_type, title, status, assigned_to, created_by, created_at
      FROM tasks 
      WHERE deleted_at IS NULL 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    console.log(`📊 Recent tasks found: ${recentTasks.length}`);
    recentTasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task.task_type}: ${task.title} - Status: ${task.status} - Created: ${task.created_at}`);
    });
    
    // Check payment verification tasks specifically
    console.log('\n💳 Checking payment verification tasks...');
    const [paymentTasks] = await connection.execute(`
      SELECT task_id, title, status, assigned_to, created_by, created_at
      FROM tasks 
      WHERE task_type = 'payment_verification' AND deleted_at IS NULL 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    console.log(`📊 Payment verification tasks found: ${paymentTasks.length}`);
    paymentTasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task.title} - Status: ${task.status} - Created: ${task.created_at}`);
    });
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

testFinanceUsers().catch(console.error);
