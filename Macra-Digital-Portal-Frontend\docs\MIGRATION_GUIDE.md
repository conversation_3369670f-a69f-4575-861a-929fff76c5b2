# TypeORM Migration Guide - Focused on Immediate Changes

This guide explains how to use the enhanced TypeORM scripts that focus on immediate changes only, avoiding unnecessary schema modifications.

## 🎯 Quick Start - Recommended Workflow

### 1. **Safe Migration Generation** (Recommended)
```bash
npm run migration:safe-generate <migration-name>
```
This command:
- Runs pre-migration checks
- Validates database connection
- Checks for pending migrations
- Generates focused migration only if changes exist

### 2. **Quick Migration Helper**
```bash
npm run migration:helper generate <migration-name>
npm run migration:helper status
npm run migration:helper validate
```

## 📋 Available Scripts

### Core Migration Scripts
| Script | Description | Use Case |
|--------|-------------|----------|
| `migration:run` | Run pending migrations | Apply migrations to database |
| `migration:show` | Show migration status | Check which migrations are applied |
| `migration:revert` | Revert last migration | Undo the most recent migration |

### Focused Generation Scripts
| Script | Description | Benefits |
|--------|-------------|----------|
| `migration:safe-generate` | Pre-check + generate | **Recommended** - Safest option |
| `migration:generate:clean` | Clean generation with pretty output | Focused on actual changes |
| `migration:generate:focused` | Generate with pretty formatting | Better readability |
| `migration:helper generate` | Interactive helper | Guided migration creation |

### Validation & Checking Scripts
| Script | Description | Purpose |
|--------|-------------|---------|
| `migration:pre-check` | Run all pre-migration validations | Ensure system is ready |
| `db:check` | Check for schema differences | See what would change |
| `db:validate` | Full database validation | Comprehensive check |
| `migration:diff` | Show schema differences | Preview changes |

### Development Scripts
| Script | Description | When to Use |
|--------|-------------|-------------|
| `migration:generate:dev` | Generate using dev config | Development environment |
| `migration:dry-run` | Simulate migration run | Test before applying |
| `schema:log` | Log current schema state | Debug schema issues |

## 🔄 Recommended Workflow

### For New Features/Changes:

1. **Make your entity changes**
   ```typescript
   // Update your entity files
   @Entity()
   export class User {
     @Column()
     newField: string; // Add new field
   }
   ```

2. **Check what will change**
   ```bash
   npm run db:check
   ```

3. **Generate focused migration**
   ```bash
   npm run migration:safe-generate add-user-new-field
   ```

4. **Review the generated migration**
   ```bash
   # Check the generated file in src/migrations/
   ```

5. **Apply the migration**
   ```bash
   npm run migration:run
   ```

### For Troubleshooting:

1. **Check current status**
   ```bash
   npm run migration:show
   npm run db:validate
   ```

2. **Run pre-checks**
   ```bash
   npm run migration:pre-check
   ```

3. **If issues found, fix them before generating migrations**

## 🛡️ Safety Features

### Pre-Migration Checks
The `migration:pre-check` script validates:
- ✅ Database connection
- ✅ No pending migrations
- ✅ Entity file integrity
- ✅ Schema change detection

### Focused Generation
- Only generates migrations for actual changes
- Avoids recreating existing constraints
- Uses pretty formatting for readability
- Includes validation steps

### Development Safety
- `synchronize: false` prevents automatic schema changes
- Explicit migration requirement
- Validation before generation
- Dry-run capabilities

## 🔧 Configuration Files

### Data Source Configurations
- `src/data-source.ts` - Production configuration
- `src/data-source.dev.ts` - Development with enhanced logging
- `src/typeorm.config.ts` - Migration-focused configuration

### Helper Scripts
- `scripts/migration-helper.ts` - Interactive migration generation
- `scripts/pre-migration-check.ts` - Comprehensive validation

## 💡 Best Practices

1. **Always run pre-checks first**
   ```bash
   npm run migration:pre-check
   ```

2. **Use descriptive migration names**
   ```bash
   npm run migration:safe-generate add-user-email-verification
   ```

3. **Review generated migrations before applying**
   ```bash
   # Check the file content before running
   npm run migration:run
   ```

4. **Keep migrations focused**
   - One logical change per migration
   - Avoid mixing unrelated changes
   - Use the helper scripts for guidance

5. **Test in development first**
   ```bash
   npm run migration:dry-run  # Test the migration
   npm run migration:run      # Apply if test passes
   ```

## 🚨 Troubleshooting

### Common Issues

**"No changes detected"**
```bash
npm run db:check  # Verify your entity changes are correct
```

**"Pending migrations exist"**
```bash
npm run migration:show  # Check status
npm run migration:run   # Apply pending migrations
```

**"Database connection failed"**
```bash
# Check your .env file database configuration
npm run migration:pre-check  # Detailed connection check
```

### Emergency Commands

**Revert last migration**
```bash
npm run migration:revert
```

**Check what went wrong**
```bash
npm run migration:show
npm run db:validate
```

## 📈 Migration Workflow Summary

```mermaid
graph TD
    A[Make Entity Changes] --> B[Run Pre-Check]
    B --> C{Pre-Check Pass?}
    C -->|No| D[Fix Issues]
    D --> B
    C -->|Yes| E[Generate Migration]
    E --> F[Review Migration File]
    F --> G[Apply Migration]
    G --> H[Verify Success]
```

This workflow ensures that only immediate, necessary changes are included in your migrations, avoiding the common TypeORM issue of generating migrations with existing constraints or unnecessary modifications.
