import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';

// Types following backend entity structure
export interface ConsumerAffairsComplaint extends BaseEntity {
  complaint_id: string;
  complaint_number: string;
  complainant_id: string;
  title: string;
  description: string;
  category: ComplaintCategory;
  status: ComplaintStatus;
  priority: ComplaintPriority;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
  deleted_at?: string;

  // Related data
  complainant?: UserReference;
  assignee?: UserReference;

  attachments?: ConsumerAffairsComplaintAttachment[];
  status_history?: ConsumerAffairsComplaintStatusHistory[];
}

export interface ConsumerAffairsComplaintAttachment {
  attachment_id: string;
  complaint_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_path: string;
  uploaded_at: string;
  uploaded_by: string;
}

export interface ConsumerAffairsComplaintStatusHistory {
  history_id: string;
  complaint_id: string;
  status: ComplaintStatus;
  comment?: string;
  created_at: string;
  created_by: string;
}

// Enums matching backend
export enum ComplaintCategory {
  BILLING_CHARGES = 'Billing & Charges',
  SERVICE_QUALITY = 'Service Quality',
  NETWORK_ISSUES = 'Network Issues',
  CUSTOMER_SERVICE = 'Customer Service',
  CONTRACT_DISPUTES = 'Contract Disputes',
  ACCESSIBILITY = 'Accessibility',
  FRAUD_SCAMS = 'Fraud & Scams',
  OTHER = 'Other',
}

export enum ComplaintStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum ComplaintPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface CreateConsumerAffairsComplaintData {
  title: string;
  description: string;
  category: ComplaintCategory;
  priority?: ComplaintPriority;
  attachments?: File[];
}

export interface UpdateConsumerAffairsComplaintData {
  title?: string;
  description?: string;
  category?: ComplaintCategory;
  status?: ComplaintStatus;
  priority?: ComplaintPriority;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
}

// Use centralized pagination types from @/types

export type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;

export const consumerAffairsService = {

  // Create new complaint
  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {
    try {
      console.log('🔄 Creating consumer affairs complaint:', {
        title: data.title,
        category: data.category,
        hasAttachments: data.attachments && data.attachments.length > 0
      });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);

      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Add attachments if provided
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post('/consumer-affairs-complaints', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Get all complaints with pagination
  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/consumer-affairs-complaints?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get complaint by ID
  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.get(`/consumer-affairs-complaints/${id}`);
    return processApiResponse(response);
  },

  // Get complaint by ID (alias for consistency)
  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {
    return this.getComplaint(id);
  },

  // Update complaint
  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs-complaints/${id}`, data);
    return processApiResponse(response);
  },

  // Delete complaint
  async deleteComplaint(id: string): Promise<void> {
    await apiClient.delete(`/consumer-affairs-complaints/${id}`);
  },

  // Update complaint status (for staff)
  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {
    const response = await apiClient.put(`/consumer-affairs-complaints/${id}/status`, {
      status,
      comment
    });
    return processApiResponse(response);
  },

  // Add attachment to complaint
  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment> {
    const formData = new FormData();
    formData.append('files', file);

    const response = await apiClient.post(`/consumer-affairs-complaints/${id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(response);
  },

  // Remove attachment from complaint
  async removeAttachment(complaintId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`/consumer-affairs-complaints/${complaintId}/attachments/${attachmentId}`);
  },



  // Helper methods
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getPriorityColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'investigating', label: 'Investigating' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' }
    ];
  },

  getCategoryOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'Billing & Charges', label: 'Billing & Charges' },
      { value: 'Service Quality', label: 'Service Quality' },
      { value: 'Network Issues', label: 'Network Issues' },
      { value: 'Customer Service', label: 'Customer Service' },
      { value: 'Contract Disputes', label: 'Contract Disputes' },
      { value: 'Accessibility', label: 'Accessibility' },
      { value: 'Fraud & Scams', label: 'Fraud & Scams' },
      { value: 'Other', label: 'Other' }
    ];
  },

  getPriorityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ];
  },
};
