'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import InvoiceStatusCard from '@/components/evaluation/InvoiceStatusCard';
import ApplicationStatusButtons from '@/components/evaluation/ApplicationStatusButtons';

import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { evaluationService, type Evaluation, type EvaluationCriteria } from '@/services/evaluationService';
import { ApplicationStatus } from '@/types/license';

// Helper function to map license type to evaluation type
const mapLicenseTypeToEvaluationType = (licenseType: string): string => {
  const mapping: Record<string, string> = {
    'individual-license-a': 'individual_license_a',
    'class-license-b': 'class_license_b',
    'network-service': 'network_service',
    'postal-service': 'postal_service',
    'postal_services': 'postal_service', // Handle both formats
    'postal-services': 'postal_service', // Handle hyphenated format
    'radio-communication': 'radio_communication',
    'satellite-service': 'satellite_service',
    'tv-broadcasting': 'tv_broadcasting',
    'university-radio': 'university_radio',
    'type_approval_certificate': 'type_approval_certificate',
    'type-approval-certificate': 'type_approval_certificate',
  };

  const mappedType = mapping[licenseType];
  if (!mappedType) {
    console.warn(`⚠️ Unknown license type: ${licenseType}. Available mappings:`, Object.keys(mapping));
    return licenseType; // Fallback to original value
  }

  return mappedType;
};




interface EvaluateSubmitPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateSubmitPage: React.FC<EvaluateSubmitPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Final evaluation state
  const [evaluation, setEvaluation] = useState<Evaluation | null>(null);
  const [evaluationComment, setEvaluationComment] = useState('');
  const [finalRecommendation, setFinalRecommendation] = useState<'approve' | 'conditional_approve' | 'reject' | ''>('');
  const [totalScore, setTotalScore] = useState<number>(0);
  const [evaluationCriteria, setEvaluationCriteria] = useState<EvaluationCriteria[]>([
    { category: 'Technical Capability', subcategory: 'Infrastructure', score: 0, weight: 0.3, max_marks: 100, awarded_marks: 0 },
    { category: 'Financial Capability', subcategory: 'Capital Requirements', score: 0, weight: 0.25, max_marks: 100, awarded_marks: 0 },
    { category: 'Legal Compliance', subcategory: 'Regulatory Requirements', score: 0, weight: 0.2, max_marks: 100, awarded_marks: 0 },
    { category: 'Experience', subcategory: 'Industry Experience', score: 0, weight: 0.15, max_marks: 100, awarded_marks: 0 },
    { category: 'Documentation', subcategory: 'Completeness', score: 0, weight: 0.1, max_marks: 100, awarded_marks: 0 }
  ]);
  const [shareholdingCompliance, setShareholdingCompliance] = useState<boolean>(true);
  const [invoiceStatus, setInvoiceStatus] = useState<{
    hasInvoice: boolean;
    status?: 'paid' | 'pending' | 'overdue' | 'none';
  }>({ hasInvoice: false, status: 'none' });

  // Dynamic navigation hook - same as apply pages
  const {
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'submit',
    licenseCategoryId,
    applicationId
  });

  // Calculate total score from criteria
  const calculateTotalScore = (criteria: EvaluationCriteria[]): number => {
    return evaluationService.calculateTotalScore(criteria);
  };



  // Load application data and existing evaluation
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || !user) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Try to load existing evaluation for this application
        try {
          const evaluations = await evaluationService.getEvaluationByApplication(applicationId);
          const currentUserEvaluation = Array.isArray(evaluations)
            ? evaluations.find((evaluation: Evaluation) => evaluation.evaluator_id === user.user_id && evaluation.status !== 'completed')
            : (evaluations && evaluations.evaluator_id === user.user_id && evaluations.status !== 'completed' ? evaluations : null);

          if (currentUserEvaluation) {
            setEvaluation(currentUserEvaluation);
            setEvaluationComment(currentUserEvaluation.evaluators_notes || '');
            setFinalRecommendation(currentUserEvaluation.recommendation || '');
            setTotalScore(currentUserEvaluation.total_score || 0);
            setShareholdingCompliance(currentUserEvaluation.shareholding_compliance ?? true);

            if (currentUserEvaluation.criteria && currentUserEvaluation.criteria.length > 0) {
              setEvaluationCriteria(currentUserEvaluation.criteria);
            }
          }
        } catch (evalError) {
          console.log('No existing evaluation found, will create new one');
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, user]);

  // Navigation handlers - modified for evaluation
  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Final evaluation submission handler
  const handleSubmitFinalEvaluation = async (recommendation: 'approve' | 'conditional_approve' | 'reject') => {
    if (!applicationId || !user) return;

    if (!evaluationComment.trim()) {
      setError('Please provide evaluation comments before submitting');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const evaluationData = {
        total_score: totalScore,
        recommendation,
        evaluators_notes: evaluationComment,
        criteria: evaluationCriteria.map(c => ({
          category: c.category,
          subcategory: c.subcategory,
          score: c.score,
          weight: c.weight,
          max_marks: c.max_marks,
          awarded_marks: c.awarded_marks
        }))
      };

      if (evaluation) {
        // Update existing evaluation
        await evaluationService.submitEvaluation(evaluation.evaluation_id, evaluationData);
      } else {
        // Create new evaluation
        const evaluationType = mapLicenseTypeToEvaluationType(licenseType);
        const newEvaluation = await evaluationService.createEvaluation({
          application_id: applicationId,
          evaluator_id: user.user_id,
          evaluation_type: evaluationType,
          ...evaluationData,
          shareholding_compliance: shareholdingCompliance
        });
        setEvaluation(newEvaluation);
      }

      // Update application status based on recommendation
      let newStatus: string;
      switch (recommendation) {
        case 'approve':
          newStatus = ApplicationStatus.APPROVED;
          break;
        case 'conditional_approve':
          newStatus = ApplicationStatus.APPROVED; // Use APPROVED for conditional approval
          break;
        case 'reject':
          newStatus = ApplicationStatus.REJECTED;
          break;
        default:
          newStatus = ApplicationStatus.UNDER_REVIEW;
      }

      await applicationService.updateApplicationStatus(applicationId, newStatus);

      // Show success message and redirect
      console.log(`Final evaluation submitted successfully! Application ${recommendation.replace('_', ' ')}.`);
      router.push('/dashboard');
    } catch (err: any) {
      console.error('Error submitting final evaluation:', err);
      setError('Failed to submit final evaluation');
    } finally {
      setIsSubmitting(false);
    }
  };



  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="submit"
        onNext={undefined}
        onPrevious={handlePrevious}
        showNextButton={false}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText="Continue"
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >

      {/* Application Review Summary */}
      <div className="space-y-6">
        {/* Review Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center">
            <i className="ri-information-line mr-2"></i>
            Review Instructions
          </h3>
          <div className="space-y-3">
            <p className="text-blue-700 dark:text-blue-300">
              Before making your final decision, please ensure you have:
            </p>
            <ul className="list-disc list-inside text-blue-700 dark:text-blue-300 space-y-2 ml-4">
              <li>Reviewed all application sections for completeness and accuracy</li>
              <li>Verified all required documents have been submitted and are valid</li>
              <li>Checked that the applicant meets all licensing requirements</li>
              <li>Assessed the technical and financial capabilities of the applicant</li>
              <li>Considered any regulatory or compliance concerns</li>
              <li>Reviewed any previous evaluation comments from other steps</li>
            </ul>
          </div>
        </div>



        {!invoiceStatus.hasInvoice && application.status === ApplicationStatus.PASS_EVALUATION && (
          // Invoice Status - Always visible for invoice generation
          <InvoiceStatusCard
            application={application!}
            onInvoiceGenerated={(invoice) => {
              console.log('Invoice generated:', invoice);
              // Update invoice status when invoice is generated
              setInvoiceStatus({
                hasInvoice: true,
                status: 'pending'
              });
            }}
            onStatusChange={(status) => {
              // Update invoice status when it changes
              setInvoiceStatus(status);
            }}
          />
        )}


        {application.status !== ApplicationStatus.PASS_EVALUATION && (
            <ApplicationStatusButtons
              application={application}
              onStatusChange={(newStatus, updatedApplication) => {
                // Update the application state with new status
                setApplication(updatedApplication);

                // If status changed to pass_evaluation, show invoice generation
                if (newStatus === ApplicationStatus.PASS_EVALUATION) {
                  setInvoiceStatus({ hasInvoice: false, status: 'none' });
                }
              }}
              disabled={isSubmitting}
            />
          )}


        {/* Final Evaluation Decision - Only show if invoice is paid */}
        {invoiceStatus.hasInvoice && invoiceStatus.status === 'paid' ? (
          <ApplicationStatusButtons
            application={application}
            onStatusChange={(newStatus, updatedApplication) => {
              // Update the application state with new status
              setApplication(updatedApplication);

              // If status changed to pass_evaluation, show invoice generation
              if (newStatus === 'pass_evaluation') {
                setInvoiceStatus({ hasInvoice: false, status: 'none' });
              }

              console.log(`Application status changed to: ${newStatus}`);
            }}
            disabled={isSubmitting}
          />
        ) : null}



        
        {invoiceStatus.hasInvoice && invoiceStatus.status !== 'paid' ? (
          /* Payment Required Message */
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6 border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center mb-4">
              <i className="ri-lock-line text-2xl text-yellow-600 dark:text-yellow-400 mr-3"></i>
              <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200">
                Payment Required
              </h3>
            </div>
            <div className="space-y-4">
              <p className="text-yellow-700 dark:text-yellow-300">
                The Final Approval will be done once the applicant paid and will be done by Director General:
              </p>
              <div className="bg-yellow-100 dark:bg-yellow-900/30 rounded-lg p-4">
                <ul className="space-y-2 text-yellow-800 dark:text-yellow-200">
                  <li className="flex items-center">
                    <i className="ri-error-warning-line mr-2"></i>
                    The application invoice has not been paid yet
                  </li>
                </ul>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                  <i className="ri-information-line mr-2"></i>
                  Next Steps:
                </h4>
                <ol className="list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm">
                  {!invoiceStatus.hasInvoice && (
                    <li>Generate an invoice for this application using the form above</li>
                  )}
                  <li>Wait for the applicant to complete payment</li>
                  <li>Once payment is confirmed, the Final Evaluation Decision form will become available</li>
                </ol>
              </div>
            </div>
          </div>
        ) : null}

      </div>

      </EvaluationLayout>
  );
};

export default EvaluateSubmitPage;
