/**
 * Utility functions for dispatching task-related events
 * These events are used to update the task count badge in real-time
 */

/**
 * Dispatch a task updated event
 * Call this when a task is modified (status change, assignment, etc.)
 */
export const dispatchTaskUpdated = (taskId?: string) => {
  const event = new CustomEvent('taskUpdated', {
    detail: { taskId, timestamp: new Date().toISOString() }
  });
  window.dispatchEvent(event);
  console.log('📋 Task updated event dispatched', { taskId });
};

/**
 * Dispatch a task completed event
 * Call this when a task is marked as completed
 */
export const dispatchTaskCompleted = (taskId: string) => {
  const event = new CustomEvent('taskCompleted', {
    detail: { taskId, timestamp: new Date().toISOString() }
  });
  window.dispatchEvent(event);
  console.log('✅ Task completed event dispatched', { taskId });
};

/**
 * Dispatch a task assigned event
 * Call this when a new task is assigned to a user
 */
export const dispatchTaskAssigned = (taskId: string, assignedTo?: string) => {
  const event = new CustomEvent('taskAssigned', {
    detail: { taskId, assignedTo, timestamp: new Date().toISOString() }
  });
  window.dispatchEvent(event);
  console.log('📌 Task assigned event dispatched', { taskId, assignedTo });
};

/**
 * Dispatch a general task refresh event
 * Call this when you want to force a refresh of task counts
 */
export const dispatchTaskRefresh = () => {
  const event = new CustomEvent('taskRefresh', {
    detail: { timestamp: new Date().toISOString() }
  });
  window.dispatchEvent(event);
  console.log('🔄 Task refresh event dispatched');
};
