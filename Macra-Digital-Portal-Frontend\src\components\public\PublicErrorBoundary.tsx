'use client';

import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface PublicErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface PublicErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class PublicErrorBoundary extends React.Component<
  PublicErrorBoundaryProps,
  PublicErrorBoundaryState
> {
  constructor(props: PublicErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): PublicErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Public page error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return (
          <FallbackComponent 
            error={this.state.error!} 
            retry={this.handleRetry} 
          />
        );
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-red-100 p-3 rounded-full">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
            
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              Something went wrong
            </h1>
            
            <p className="text-gray-600 mb-6">
              We encountered an error while loading this page. Please try again or return to the home page.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mb-6 p-3 bg-gray-100 rounded-md text-left">
                <p className="text-xs text-gray-700 font-mono">
                  {this.state.error.message}
                </p>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleRetry}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
              </button>
              
              <a
                href="/public"
                className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors inline-flex items-center justify-center space-x-2"
              >
                <Home className="h-4 w-4" />
                <span>Go Home</span>
              </a>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Loading component for public pages
export function PublicLoadingSpinner({ 
  message = 'Loading...', 
  size = 'medium' 
}: { 
  message?: string; 
  size?: 'small' | 'medium' | 'large' 
}) {
  const sizeClasses = {
    small: 'h-4 w-4',
    medium: 'h-8 w-8',
    large: 'h-12 w-12'
  };

  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]} mb-4`}></div>
      <p className="text-gray-600">{message}</p>
    </div>
  );
}

// Network error component
export function NetworkError({ 
  onRetry, 
  message = 'Network error. Please check your connection and try again.' 
}: { 
  onRetry: () => void; 
  message?: string 
}) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-6 text-center">
      <div className="flex justify-center mb-4">
        <AlertTriangle className="h-8 w-8 text-red-600" />
      </div>
      <h3 className="text-lg font-medium text-red-800 mb-2">Connection Error</h3>
      <p className="text-red-700 mb-4">{message}</p>
      <button
        onClick={onRetry}
        className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors inline-flex items-center space-x-2"
      >
        <RefreshCw className="h-4 w-4" />
        <span>Try Again</span>
      </button>
    </div>
  );
}

// Generic error message component
export function ErrorMessage({ 
  title = 'Error', 
  message, 
  onRetry,
  showRetry = true 
}: { 
  title?: string; 
  message: string; 
  onRetry?: () => void;
  showRetry?: boolean;
}) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4">
      <div className="flex items-start space-x-2">
        <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-red-800 font-medium">{title}</h4>
          <p className="text-red-700 text-sm mt-1">{message}</p>
          {showRetry && onRetry && (
            <button
              onClick={onRetry}
              className="mt-3 text-red-600 hover:text-red-700 text-sm font-medium inline-flex items-center space-x-1"
            >
              <RefreshCw className="h-3 w-3" />
              <span>Try again</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// Success message component
export function SuccessMessage({ 
  title = 'Success', 
  message 
}: { 
  title?: string; 
  message: string 
}) {
  return (
    <div className="bg-green-50 border border-green-200 rounded-md p-4">
      <div className="flex items-start space-x-2">
        <svg className="h-5 w-5 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
        <div>
          <h4 className="text-green-800 font-medium">{title}</h4>
          <p className="text-green-700 text-sm mt-1">{message}</p>
        </div>
      </div>
    </div>
  );
}

// Warning message component
export function WarningMessage({ 
  title = 'Warning', 
  message 
}: { 
  title?: string; 
  message: string 
}) {
  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
      <div className="flex items-start space-x-2">
        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
        <div>
          <h4 className="text-yellow-800 font-medium">{title}</h4>
          <p className="text-yellow-700 text-sm mt-1">{message}</p>
        </div>
      </div>
    </div>
  );
}
