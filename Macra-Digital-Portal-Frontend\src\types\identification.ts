import { BaseEntity, UserReference } from './index';

// Identification Type interfaces
export interface IdentificationType extends BaseEntity {
  identification_type_id: string;
  name: string;
  creator?: UserReference;
  updater?: UserReference;
  user_identifications?: any[];
}

export interface CreateIdentificationTypeDto {
  name: string;
}

export interface UpdateIdentificationTypeDto {
  name?: string;
}

export interface IdentificationTypeFilters {
  search?: string;
  status?: string;
  created_by?: string;
}

// Identification Type responses
export type IdentificationTypesResponse = {
  data: IdentificationType[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
};
