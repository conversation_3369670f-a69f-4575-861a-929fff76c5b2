'use client';

import React, { useState, useEffect } from 'react';
import { validateIMEIChecksum, patterns } from '@/utils/formValidation';

interface IMEIInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  label?: string;
  placeholder?: string;
  helpText?: string;
  isValidating?: boolean;
}

const IMEIInput: React.FC<IMEIInputProps> = ({
  value,
  onChange,
  error,
  disabled = false,
  required = true,
  label = 'IMEI Number',
  placeholder = 'Enter 15-digit IMEI number',
  helpText = 'IMEI (International Mobile Equipment Identity) is a unique 15-digit number that identifies your mobile device.',
  isValidating = false
}) => {
  const [localError, setLocalError] = useState<string>('');

  // Format IMEI as user types (add spaces for readability)
  const formatIMEI = (input: string): string => {
    // Remove all non-digits
    const digits = input.replace(/\D/g, '');
    
    // Limit to 15 digits
    const limitedDigits = digits.slice(0, 15);
    
    // Add spaces for readability: XXX XXX XXX XXX XXX
    return limitedDigits.replace(/(\d{3})(?=\d)/g, '$1 ');
  };

  // Get raw IMEI without spaces
  const getRawIMEI = (formattedValue: string): string => {
    return formattedValue.replace(/\s/g, '');
  };

  // Validate IMEI
  const validateIMEI = (imeiValue: string): string => {
    const rawIMEI = getRawIMEI(imeiValue);
    
    if (!rawIMEI && required) {
      return 'IMEI number is required';
    }
    
    if (!rawIMEI) {
      return '';
    }
    
    if (!patterns.imei.test(rawIMEI)) {
      if (rawIMEI.length < 15) {
        return 'IMEI must be exactly 15 digits';
      }
      return 'IMEI must contain only digits';
    }
    
    if (!validateIMEIChecksum(rawIMEI)) {
      return 'Invalid IMEI number - checksum verification failed';
    }
    
    return '';
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const formattedValue = formatIMEI(inputValue);
    const rawIMEI = getRawIMEI(formattedValue);
    
    // Update the value
    onChange(rawIMEI);
    
    // Clear local error when user starts typing
    if (localError) {
      setLocalError('');
    }
  };

  // Handle blur for validation
  const handleBlur = () => {
    setIsValidating(true);
    const validationError = validateIMEI(value);
    setLocalError(validationError);
    setIsValidating(false);
  };

  // Get display value (formatted)
  const displayValue = formatIMEI(value);

  // Determine which error to show
  const displayError = error || localError;

  // Get input styling based on state
  const getInputClasses = () => {
    const baseClasses = 'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';
    
    if (displayError) {
      return `${baseClasses} border-red-300 focus:border-red-500 focus:ring-red-500`;
    }
    
    if (value && !displayError) {
      return `${baseClasses} border-green-300 focus:border-green-500 focus:ring-green-500`;
    }
    
    return `${baseClasses} border-gray-300 focus:border-blue-500 focus:ring-blue-500`;
  };

  return (
    <div className="space-y-2">
      {/* Label */}
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {/* Input */}
      <div className="relative">
        <input
          type="text"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={disabled}
          placeholder={placeholder}
          className={getInputClasses()}
          maxLength={19} // 15 digits + 4 spaces
        />
        
        {/* Validation indicator */}
        {value && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            {isValidating ? (
              <i className="ri-loader-4-line animate-spin h-5 w-5 text-blue-500"></i>
            ) : displayError ? (
              <svg className="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        )}
      </div>
      
      {/* Help text */}
      {helpText && !displayError && (
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {helpText}
        </p>
      )}
      
      {/* Error message */}
      {displayError && (
        <p className="text-sm text-red-600 dark:text-red-400">
          {displayError}
        </p>
      )}
      
      {/* IMEI info */}
      {value && !displayError && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <p>✓ Valid IMEI format</p>
          <p>✓ Checksum verified</p>
        </div>
      )}
    </div>
  );
};

export default IMEIInput;
