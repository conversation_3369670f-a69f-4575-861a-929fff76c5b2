import { Application, Applicant } from '@/types/license';
import { applicantService } from '@/services/applicantService';
import { userService } from '@/services/userService';

/**
 * Utility function to get applicant data from an application
 * Prioritizes embedded applicant data and only fetches additional data if needed
 */
export const getApplicantFromApplication = async (application: Application): Promise<Applicant | null> => {
  // If applicant data is already embedded in the application, use it
  if (application.applicant) {
    console.log('✅ Using embedded applicant data from application');
    return application.applicant as Applicant;
  }

  // If no embedded applicant data but we have an applicant_id, try to fetch it
  if (application.applicant_id) {
    try {
      // First try to get from applicant service
      const applicantResponse = await applicantService.getApplicant(application.applicant_id);
      console.log('✅ Applicant details fetched from applicant service:', applicantResponse);
      return applicantResponse;
    } catch (applicantError) {
      console.warn('⚠️ Could not fetch applicant details from applicant service:', applicantError);
      
      // Fallback to user service
      try {
        const userResponse = await userService.getUser(application.applicant_id);
        // Convert user data to applicant-like structure
        const applicantFromUser: Applicant = {
          applicant_id: userResponse.user_id,
          name: `${userResponse.first_name} ${userResponse.last_name}`,
          email: userResponse.email,
          phone: userResponse.phone,
          business_registration_number: '',
          tpin: '',
          website: '',
          fax: '',
          date_incorporation: '',
          place_incorporation: '',
          created_at: userResponse.created_at,
          updated_at: userResponse.updated_at
        };
        console.log('✅ User details fetched as fallback and converted to applicant:', applicantFromUser);
        return applicantFromUser;
      } catch (userError) {
        console.warn('⚠️ Could not fetch user details either:', userError);
        return null;
      }
    }
  }

  console.warn('⚠️ No applicant data available - no embedded data and no applicant_id');
  return null;
};

/**
 * Get the applicant ID from various sources in the application
 */
export const getApplicantId = (application: Application, separateApplicant?: Applicant | null): string | undefined => {
  return application.applicant?.applicant_id || 
         separateApplicant?.applicant_id || 
         application.applicant_id;
};