'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

const CustomerRecoverPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [userName, setUserName] = useState('');

  useEffect(() => {
    // Get email and user name from URL parameters
    const emailParam = searchParams.get('email');
    const nameParam = searchParams.get('name');

    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
    if (nameParam) {
      setUserName(decodeURIComponent(nameParam));
    }

    // Auto-redirect to verify-login after 3 seconds
    const timer = setTimeout(() => {
      router.replace('/customer/auth/verify-login');
    }, 3000);

    return () => clearTimeout(timer);
  }, [searchParams, router]);

  const handleContinueToVerification = () => {
    setLoading(true);
    router.replace('/customer/auth/verify-login');
  };

  const handleBackToLogin = () => {
    router.replace('/customer/auth/login');
  };

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Image
            src="/images/macra-logo.png"
            alt="MACRA Logo"
            width={64}
            height={64}
            className="h-16 w-auto animate-fadeLoop"
            priority
          />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">
          Account Recovery Required
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Your account has been deactivated and needs to be recovered
        </p>
      </div>

      {/* Main Content */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">

          {/* Recovery Information */}
          <div className="mb-6">
            <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full">
              <i className="ri-mail-send-line text-blue-600 dark:text-blue-400 text-2xl"></i>
            </div>

            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Verification Email Sent
              </h3>
              {userName && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Hello {userName},
                </p>
              )}
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                We've sent a verification code to:
              </p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-md">
                {email || 'your registered email address'}
              </p>
            </div>
          </div>

          {/* Recovery Process Information */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <i className="ri-information-line text-blue-500 text-lg"></i>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Account Recovery Process
                </h4>
                <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                  <p>1. Check your email for the verification code</p>
                  <p>2. Click the verification link in the email</p>
                  <p>3. Your account will be automatically reactivated</p>
                  <p>4. You'll be redirected to the customer dashboard</p>
                </div>
              </div>
            </div>
          </div>

          {/* What Happens Next */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <i className="ri-check-line text-green-500 text-lg"></i>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
                  After Recovery
                </h4>
                <div className="text-xs text-green-700 dark:text-green-300 space-y-1">
                  <p>• All your data will be restored immediately</p>
                  <p>• Your applications and licenses remain intact</p>
                  <p>• Payment history and documents are preserved</p>
                  <p>• Full access to all MACRA services</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleContinueToVerification}
              disabled={loading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <>
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Redirecting...
                </>
              ) : (
                <>
                  <i className="ri-mail-check-line mr-2"></i>
                  Continue to Email Verification
                </>
              )}
            </button>

            <button
              onClick={handleBackToLogin}
              className="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Login
            </button>
          </div>

          {/* Help Text */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Didn't receive the email? Check your spam folder or{' '}
              <Link
                href="/customer/auth/login"
                className="text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 font-medium"
              >
                try logging in again
              </Link>
            </p>
          </div>
        </div>
      </div>

      {/* Auto-redirect notice */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          You will be automatically redirected to email verification in a few seconds...
        </p>
      </div>
    </div>
  );
};

export default CustomerRecoverPage;