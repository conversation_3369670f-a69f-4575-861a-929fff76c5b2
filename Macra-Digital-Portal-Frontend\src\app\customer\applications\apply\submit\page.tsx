'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import { licenseCategoryService } from '@/services/licenseCategoryService';
import { Application, ApplicationStatus, LicenseType } from '@/types/license';
import { documentService } from '@/services/documentService';
import { imeiValidationService } from '@/services/imeiValidationService';
import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';


const SubmitPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [confirmationAccepted, setConfirmationAccepted] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [submissionAttempts, setSubmissionAttempts] = useState(0);

  // License data
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);
  const [application, setApplication] = useState<Application | null>(null);

  // Application summary data

  // Load application data and summary
  useEffect(() => {
    const loadData = async () => {

      if (!applicationId || !isAuthenticated || authLoading) {
        return;
      }

      const application = await applicationService.getApplication(applicationId);
      setApplication(application)

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);

        // Validate application ID format
        if (!applicationId || applicationId.trim() === '') {
          throw new Error('Application ID is missing from the URL');
        }

        // Check if it looks like a valid UUID
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(applicationId)) {
          throw new Error('Invalid application ID format');
        }

        // Load license category and type data
        if (licenseCategoryId) {
          try {
            const categoryData = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
            if (categoryData && categoryData.license_type) {
              setLicenseType(categoryData.license_type);
            }
          } catch (licenseError) {
            console.warn('Could not load license category data:', licenseError);
          }
        }

        // Load application data
        try {
          if (!applicationId) {
            throw new Error('Application ID is null or undefined');
          }
          const application = await applicationService.getApplication(applicationId);
          // Check if application exists
          if (!application) {
            throw new Error('Application not found');
          }

        } catch (appError: any) {
          // Handle specific error cases
          if (appError.response?.status === 404) {
            throw new Error('Application not found. The application may have been deleted or the ID is incorrect.');
          } else if (appError.response?.status === 403) {
            throw new Error('You do not have permission to access this application.');
          } else if (appError.response?.status === 401) {
            throw new Error('Authentication required. Please log in again.');
          } else if (appError.response?.status === 500) {
            throw new Error('Server error occurred while loading the application. Please try again later.');
          } else if (appError.response?.data?.message) {
            throw new Error(`Failed to load application: ${appError.response.data.message}`);
          } else {
            throw new Error(`Failed to load application data: ${appError.message || 'Unknown error'}`);
          }
        }

      } catch (err: any) {
        setError(err.message || 'Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Comprehensive validation for type approval certificate applications
  const validateTypeApprovalApplication = async (): Promise<string[]> => {
    const errors: string[] = [];

    if (!application) {
      errors.push('Application data not loaded');
      return errors;
    }

    const licenseTypeCode = application.license_category?.license_type?.code;
    const isTypeApprovalCertificate = licenseTypeCode === 'type_approval_certificate' ||
                                     application.license_category?.license_type?.name?.toLowerCase().includes('type approval');

    if (!isTypeApprovalCertificate) {
      // For non-type approval applications, just check basic requirements
      return errors;
    }

    try {
      // 1. Validate Applicant Information
      try {
        const applicantData = application.applicant;
        if (!applicantData) {
          errors.push('Applicant information is missing');
        } else {
          // Check required applicant fields
          const requiredApplicantFields = [
            { field: 'name', label: 'Business/Organization Name' },
            { field: 'business_registration_number', label: 'Business Registration Number' },
            { field: 'tpin', label: 'TPIN' },
            { field: 'email', label: 'Email Address' },
            { field: 'phone', label: 'Phone Number' },
            { field: 'date_incorporation', label: 'Date of Incorporation' },
            { field: 'place_incorporation', label: 'Place of Incorporation' }
          ];

          requiredApplicantFields.forEach(({ field, label }) => {
            if (!applicantData[field as keyof typeof applicantData] ||
                String(applicantData[field as keyof typeof applicantData]).trim() === '') {
              errors.push(`${label} is required in applicant information`);
            }
          });
        }
      } catch (applicantError) {
        console.error('Applicant validation error:', applicantError);
        errors.push('Unable to verify applicant information - please ensure Step 1 is completed');
      }

      // 2. Validate Equipment Details (IMEI Validation) with retry logic
      try {
        let imeiData = null;
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries && !imeiData) {
          try {
            imeiData = await imeiValidationService.getIMEIForApplication(applicationId!);
            break;
          } catch (imeiError: any) {
            retryCount++;
            console.warn(`IMEI validation attempt ${retryCount} failed:`, imeiError);
            
            if (retryCount <= maxRetries) {
              // Wait before retry (exponential backoff)
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }

        if (!imeiData) {
          errors.push('Equipment details are missing');
        } else {
          // Check IMEI validation
          if (!imeiData.imei || imeiData.imei.length !== 15) {
            errors.push('Valid IMEI number is required');
          }

          if (!imeiData.validation_result) {
            errors.push('IMEI validation is required');
          } else if (imeiData.validation_result.status === 'blacklisted') {
            errors.push('Device is blacklisted and cannot be approved');
          }
        }
      } catch (imeiError) {
        console.error('IMEI validation error:', imeiError);
        errors.push('Unable to verify equipment details - please ensure Step 2 is completed');
      }

      // 3. Validate Required Documents with retry logic
      try {
        let documents = null;
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries && !documents) {
          try {
            documents = await documentService.getDocumentsByApplication(applicationId!);
            break;
          } catch (docError: any) {
            retryCount++;
            console.warn(`Document validation attempt ${retryCount} failed:`, docError);
            
            if (retryCount <= maxRetries) {
              // Wait before retry (exponential backoff)
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
          }
        }

        if (!documents) {
          errors.push('Unable to load documents - please ensure Step 3 is completed');
          return errors;
        }

        const uploadedDocs = documents.data || [];

        // Get required documents from configuration
        const licenseConfig = getLicenseTypeStepConfig('type_approval_certificate');
        const requiredDocuments = licenseConfig.requirements || [];

        if (requiredDocuments.length === 0) {
          errors.push('No document requirements configured for type approval certificate');
        } else {
          // Check each required document
          requiredDocuments.forEach(docName => {
            const docType = docName.toLowerCase().replace(/\s+/g, '_');
            const hasDocument = uploadedDocs.some((doc: any) =>
              doc.document_type === docType ||
              doc.document_type === documentService.mapDocumentNameToType(docName)
            );

            if (!hasDocument) {
              errors.push(`Required document missing: ${docName}`);
            }
          });

          if (uploadedDocs.length === 0) {
            errors.push('No documents have been uploaded - please complete Step 3');
          }
        }
      } catch (documentsError) {
        console.error('Document validation error:', documentsError);
        errors.push('Unable to verify documents - please ensure Step 3 is completed');
      }

    } catch (validationError) {
      console.error('Validation error:', validationError);
      errors.push('Unable to validate application - please try again');
    }

    return errors;
  };

  // Submit application with improved error handling and retry logic
  const handleSubmit = async (): Promise<boolean> => {
    if (!applicationId) {
      setError('Application ID is required');
      return false;
    }

    if (!confirmationAccepted) {
      setError('You must confirm the declaration before submitting');
      return false;
    }

    // Refresh application data to get latest status
    try {
      const latestApplication = await applicationService.getApplication(applicationId);
      setApplication(latestApplication);
      
      // Check if application is already submitted
      if (latestApplication?.status === ApplicationStatus.SUBMITTED) {
        console.log('Application already submitted, redirecting...');
        router.push(`/customer/applications/submitted?application_id=${applicationId}`);
        return true;
      }
    } catch (refreshError) {
      console.warn('Could not refresh application data:', refreshError);
      // Continue with submission attempt using cached data
    }

    // Validate application before submission
    setIsValidating(true);
    setValidationErrors([]);
    setError(null);

    try {
      const errors = await validateTypeApprovalApplication();

      if (errors.length > 0) {
        setValidationErrors(errors);
        setError('Please fix the following issues before submitting your application:');
        setIsValidating(false);
        return false;
      }
    } catch (validationError) {
      console.error('Validation failed:', validationError);
      setError('Unable to validate application. Please try again.');
      setIsValidating(false);
      return false;
    }

    setIsValidating(false);
    setIsSubmitting(true);
    setSubmissionAttempts(prev => prev + 1);
    
    try {
      // Implement retry logic for submission
      let submitSuccess = false;
      let retryCount = 0;
      const maxRetries = 2;
      let lastError: any = null;

      while (retryCount <= maxRetries && !submitSuccess) {
        try {
          console.log(`Submission attempt ${retryCount + 1} for application:`, applicationId);
          
          // Update application status to submitted
          const submissionData = {
            status: ApplicationStatus.SUBMITTED,
            submitted_at: new Date().toISOString(),
            progress_percentage: 100,
            current_step: 8 // Final step
          };
          
          const updatedApplication = await applicationService.updateApplication(applicationId, submissionData);
          
          // Verify the submission was successful
          if (updatedApplication.status === ApplicationStatus.SUBMITTED) {
            console.log('Application submitted successfully:', updatedApplication);
            submitSuccess = true;

            // Small delay to ensure backend processing completes
            await new Promise(resolve => setTimeout(resolve, 500));

            // Redirect to success page
            router.push(`/customer/applications/submitted?application_id=${applicationId}`);
            return true;
          } else {
            throw new Error(`Submission failed: Application status is ${updatedApplication.status}, expected ${ApplicationStatus.SUBMITTED}`);
          }

        } catch (error: any) {
          lastError = error;
          retryCount++;
          console.error(`Submission attempt ${retryCount} failed:`, error);

          if (retryCount <= maxRetries) {
            // Wait before retry (exponential backoff)
            console.log(`Retrying submission in ${1000 * retryCount}ms...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          }
        }
      }

      // If all retries failed
      if (!submitSuccess) {
        console.error('All submission attempts failed. Last error:', lastError);
        
        // Provide more specific error messages based on the error type
        let errorMessage = 'Failed to submit application. Please try again.';
        
        if (lastError?.response?.status === 400) {
          errorMessage = 'Invalid application data. Please check your information and try again.';
        } else if (lastError?.response?.status === 409) {
          errorMessage = 'Application has already been submitted or is in an invalid state.';
        } else if (lastError?.response?.status === 429) {
          errorMessage = 'Too many requests. Please wait a moment and try again.';
        } else if (lastError?.code === 'ECONNABORTED' || lastError?.message?.includes('timeout')) {
          errorMessage = 'Request timeout. Please check your connection and try again.';
        } else if (lastError?.response?.data?.message) {
          errorMessage = `Submission failed: ${lastError.response.data.message}`;
        }
        
        setError(errorMessage);
        return false;
      }

      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Navigation functions
  const handleNext = async () => {
    // Prevent double-clicking
    if (isSubmitting || isValidating) {
      return;
    }
    await handleSubmit();
  };

  const handlePrevious = () => {
    router.push(`/customer/applications/apply/documents?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application for submission...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Application</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                {application && (
                  <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/30 rounded text-xs text-red-600 dark:text-red-400">
                    Application Number: {application?.application_number}
                  </div>
                )}
                <div className="mt-4 space-x-2">
                  <button
                    onClick={() => router.back()}
                    className="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                  >
                    <i className="ri-arrow-left-line mr-2"></i>
                    Go Back
                  </button>
                  <button
                    onClick={() => window.location.reload()}
                    className="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                  >
                    <i className="ri-refresh-line mr-2"></i>
                    Retry
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={async () => true} // No save needed on submit page
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={false}
        nextButtonText={isValidating ? "Validating..." : (isSubmitting ? "Submitting..." : "Submit Application")}
        previousButtonText="Back to Documents"
        nextButtonDisabled={!confirmationAccepted || isSubmitting || isValidating || validationErrors.length > 0}
        isSaving={isSubmitting || isValidating}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Submit Application
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Review your application details and submit for review by MACRA.
          </p>
          {application && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-file-text-line mr-1"></i>
                Application Number: {application?.application_number}
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
          {submissionAttempts > 0 && (
            <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <i className="ri-information-line mr-1"></i>
                Submission attempts: {submissionAttempts}
              </p>
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3 mt-0.5"></i>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                  Application Incomplete
                </h3>
                <p className="text-red-700 dark:text-red-300 text-sm mb-3">
                  Please complete the following required items before submitting your application:
                </p>
                <ul className="text-sm text-red-700 dark:text-red-300 list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
                <div className="mt-3 p-3 bg-red-100 dark:bg-red-800/30 rounded-md">
                  <p className="text-xs text-red-600 dark:text-red-400">
                    💡 <strong>Tip:</strong> Use the navigation buttons to go back to previous steps and complete the missing information.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading Validation */}
        {isValidating && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Validating application completeness...
              </p>
            </div>
          </div>
        )}

        {/* Important Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6">
          <h4 className="text-md font-medium text-blue-900 dark:text-blue-100 mb-2">
            <i className="ri-information-line mr-2"></i>
            Important Information
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Your application will be reviewed by MACRA within 30 business days</li>
            <li>• You will receive email notifications about the status of your application</li>
            <li>• Additional documentation may be requested during the review process</li>
            <li>• Application fees are non-refundable</li>
            <li>• You can track your application status in your dashboard</li>
          </ul>
        </div>

        {/* Warning */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6">
          <div className="flex">
            <i className="ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Before Submitting
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Please ensure all information is accurate and complete. Once submitted, 
                you will not be able to modify your application without contacting MACRA support.
              </p>
            </div>
          </div>
        </div>

        {/* Final Declaration */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            <i className="ri-file-list-3-line mr-2"></i>
            Final Declaration
          </h3>
          <div className="p-5 bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-lg shadow-sm">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <input
                  type="checkbox"
                  id="confirmation"
                  checked={confirmationAccepted}
                  onChange={(e) => setConfirmationAccepted(e.target.checked)}
                  className="h-6 w-6 text-primary focus:ring-primary border-gray-300 rounded cursor-pointer"
                />
              </div>
              <label htmlFor="confirmation" className="ml-3 block text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer">
                I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. 
                I understand that providing false or misleading information may result in the rejection of my application or 
                revocation of any license granted. I agree to the terms and conditions of the licensing process. 
                <span className="text-red-500 font-bold ml-1">*</span>
              </label>
            </div>
            {!confirmationAccepted && (
              <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/30 rounded-md border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <i className="ri-error-warning-line mr-2"></i>
                  You must accept this declaration to submit your application.
                </p>
              </div>
            )}
          </div>
        </div>

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default SubmitPage;
