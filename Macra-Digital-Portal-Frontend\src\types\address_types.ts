import { BaseEntity, UserReference } from './index';

// Core Address interface based on backend entity

// Address interface
export interface AddressEntity {
  address_id: string;
  street: string;
  city: string;
  state?: string;
  postal_code: string;
  country: string;
  address_type: 'physical' | 'postal';
}
export interface Address extends AddressEntity {
  address_id: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code: string;
  country: string;
  city: string;
  deleted_at?: string;

  // Related data
  creator?: UserReference;
  updater?: UserReference;
}

export interface PostalCodeData {
  postal_code_id: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}


export interface SequentialAddressData {
  country: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  address_line_1: string;
  address_line_2: string;
}


export interface SequentialAddressBuilderProps {
  onAddressComplete: (address: SequentialAddressData) => void;
  onAddressChange?: (address: Partial<SequentialAddressData>) => void;
  initialData?: Partial<SequentialAddressData>;
  disabled?: boolean;
  className?: string;
  // Additional props for form integration
  onFieldChange?: (field: string, value: string) => void;
  validationErrors?: Record<string, string>;
}


// DTO interfaces for API operations
export interface CreateAddressDto {
  address_type?: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code: string;
  country: string;
  city: string;
}

export interface UpdateAddressDto {
  address_type?: string;
  entity_type?: string;
  entity_id?: string;
  address_line_1?: string;
  address_line_2?: string;
  address_line_3?: string;
  postal_code?: string;
  country?: string;
  city?: string;
}

// Extended interface for hook usage that includes address_id
export interface EditAddressData extends UpdateAddressDto {
  address_id: string;
}

// Search and filter interfaces
export interface SearchPostcodes {
  region?: string;
  district?: string;
  location?: string;
  postal_code?: string;
}

export interface PostalCodeLookupResult {
  postal_code_id: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

// Address filters for queries
export interface AddressFilters {
  entity_type?: string;
  entity_id?: string;
  address_type?: string;
  country?: string;
  city?: string;
  postal_code?: string;
}

// Address service response types
export interface AddressResponse {
  data: Address[];
  total: number;
  page?: number;
  limit?: number;
}

export interface SingleAddressResponse {
  data: Address;
}

// Address validation types
export interface AddressValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Address type constants
export const ADDRESS_TYPES = {
  BUSINESS: 'business',
  POSTAL: 'postal',
  PHYSICAL: 'physical',
  BILLING: 'billing',
  SHIPPING: 'shipping',
} as const;


export const AddressType  = {
  APPLICANT: 'applicant',
  APPLICATION: 'application',
  STAKEHOLDER: 'stakeholder',
  CONTACT_PERSON: 'contact_person',
  USER: 'user',
} ;



export const EntityType  = {
  APPLICANT: 'applicant',
  APPLICATION: 'application',
  STAKEHOLDER: 'stakeholder',
  CONTACT_PERSON: 'contact_person',
  USER: 'user',
} ;

// Component prop types
export interface AddressDropdownProps {
  addresses: Address[];
  selectedAddressId: string | null;
  onSelect: (id: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
}

export interface AddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (address: Address) => void;
  address?: Address | null;
  entityType?: string;
  entityId?: string;
}
