'use client';

import React from 'react';
import Link from 'next/link';
import { Shield, Search, BarChart3, HelpCircle, ArrowRight } from 'lucide-react';
import PageTransition, { StaggeredAnimation } from '@/components/public/PageTransition';

export default function PublicHomePage() {
  return (
    <PageTransition>
      <div className="space-y-12">
        {/* Hero Section */}
        <StaggeredAnimation delay={100}>
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-primary/10 p-4 rounded-full animate-pulse">
                <Shield className="h-12 w-12 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              MACRA License Verification Portal
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto mb-8">
              Verify the authenticity of licenses issued by the Malawi Communications
              Regulatory Authority. Ensure compliance and trust in regulated services.
            </p>
            <Link
              href="/public/verify"
              className="inline-flex items-center space-x-2 bg-primary text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-red-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105"
            >
              <Shield className="h-5 w-5" />
              <span>Verify License Now</span>
              <ArrowRight className="h-5 w-5" />
            </Link>
          </div>
        </StaggeredAnimation>

        {/* Features Grid */}
        <StaggeredAnimation delay={200}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* License Verification */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="bg-primary/10 p-3 rounded-full w-fit mx-auto mb-4">
                <Search className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                License Verification
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Instantly verify the authenticity and validity of any MACRA-issued license
                using the license number.
              </p>
              <Link
                href="/public/verify"
                className="text-primary hover:text-red-700 font-medium inline-flex items-center space-x-1 transition-colors"
              >
                <span>Start Verification</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>

            {/* Statistics */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="bg-green-100 dark:bg-green-900/20 p-3 rounded-full w-fit mx-auto mb-4">
                <BarChart3 className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                License Statistics
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                View public statistics about licenses in the system, including active,
                expired, and suspended licenses.
              </p>
              <Link
                href="/public/statistics"
                className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium inline-flex items-center space-x-1 transition-colors"
              >
                <span>View Statistics</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>

            {/* Help & Support */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center hover:shadow-md transition-all duration-200 hover:scale-105">
              <div className="bg-purple-100 dark:bg-purple-900/20 p-3 rounded-full w-fit mx-auto mb-4">
                <HelpCircle className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Help & Support
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Get help with license verification, understand the process, and find
                answers to frequently asked questions.
              </p>
              <Link
                href="/public/help"
                className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium inline-flex items-center space-x-1 transition-colors"
              >
                <span>Get Help</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </StaggeredAnimation>

        {/* How It Works */}
        <StaggeredAnimation delay={300}>
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-8 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
              How License Verification Works
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-sm animate-pulse">
                  1
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Enter License Number
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Input the license number in the format LIC-YYYY-MM-NNN
                </p>
              </div>
              <div className="text-center">
                <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-sm animate-pulse" style={{animationDelay: '0.5s'}}>
                  2
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  System Verification
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Our system checks the license against MACRA&apos;s official database
                </p>
              </div>
              <div className="text-center">
                <div className="bg-primary text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-sm animate-pulse" style={{animationDelay: '1s'}}>
                  3
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Get Results
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Receive instant verification results with license details
                </p>
              </div>
            </div>
          </div>
        </StaggeredAnimation>

        {/* Quick Access */}
        <StaggeredAnimation delay={400}>
          <div className="bg-primary/5 dark:bg-primary/10 rounded-xl p-8 border border-primary/20 hover:shadow-md transition-shadow duration-200">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Need to Access Your Account?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                If you&apos;re a license holder or MACRA staff member, access your dedicated portal.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/customer/auth/login"
                  className="bg-white dark:bg-gray-800 text-primary px-6 py-3 rounded-lg font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 border border-primary shadow-sm hover:shadow-md hover:scale-105"
                >
                  Customer Portal
                </Link>
                <Link
                  href="/auth/login"
                  className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 transition-all duration-200 shadow-sm hover:shadow-md hover:scale-105"
                >
                  Staff Portal
                </Link>
              </div>
            </div>
          </div>
        </StaggeredAnimation>
      </div>
    </PageTransition>
  );
}
