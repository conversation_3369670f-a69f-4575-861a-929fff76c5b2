const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log(`🧪 Testing: ${method} ${path}`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        console.log(`✅ Status: ${res.statusCode}`);
        try {
          const parsed = JSON.parse(responseData);
          if (res.statusCode >= 400) {
            console.log(`❌ Error Response: ${JSON.stringify(parsed, null, 2)}`);
          } else {
            console.log(`📊 Success: ${typeof parsed}`);
          }
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          console.log(`📝 Raw response: ${responseData.substring(0, 200)}...`);
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Error: ${err.message}`);
      reject(err);
    });

    req.setTimeout(10000, () => {
      console.log('❌ Request timeout');
      req.destroy();
      reject(new Error('Timeout'));
    });

    if (data && method !== 'GET') {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runTests() {
  console.log('🚀 Testing fixed endpoints (no authentication)...');
  
  try {
    // Test basic endpoints that should work
    await testEndpoint('/payments');
    console.log('');
    
    await testEndpoint('/invoices');
    console.log('');
    
    await testEndpoint('/payments/statistics');
    console.log('');
    
    // Test customer endpoints that previously had req.user errors
    console.log('🔍 Testing customer endpoints that were fixed:');
    await testEndpoint('/payments/customer');
    console.log('');
    
    await testEndpoint('/payments/customer/statistics');
    console.log('');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
  
  console.log('\n🏁 Tests completed!');
  console.log('✅ All req.user null reference errors should now be fixed');
}

runTests();
