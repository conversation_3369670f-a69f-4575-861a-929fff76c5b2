'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingState from './LoadingState';

interface PageTransitionProps {
  children: React.ReactNode;
  isLoading?: boolean;
  loadingMessage?: string;
  loadingSubmessage?: string;
  redirectTo?: string;
  redirectDelay?: number;
  showProgress?: boolean;
  dynamicMessages?: string[];
  className?: string;
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  isLoading = false,
  loadingMessage = 'Loading...',
  loadingSubmessage,
  redirectTo,
  redirectDelay = 2000,
  showProgress = false,
  dynamicMessages = [],
  className = ''
}) => {
  const router = useRouter();
  const [progress, setProgress] = useState(0);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Handle redirect with progress
  useEffect(() => {
    if (redirectTo && !isRedirecting) {
      setIsRedirecting(true);
      
      if (showProgress) {
        const progressInterval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 100) {
              clearInterval(progressInterval);
              return 100;
            }
            return prev + (100 / (redirectDelay / 100));
          });
        }, 100);

        const redirectTimeout = setTimeout(() => {
          router.push(redirectTo);
        }, redirectDelay);

        return () => {
          clearInterval(progressInterval);
          clearTimeout(redirectTimeout);
        };
      } else {
        const redirectTimeout = setTimeout(() => {
          router.push(redirectTo);
        }, redirectDelay);

        return () => clearTimeout(redirectTimeout);
      }
    }
  }, [redirectTo, redirectDelay, router, showProgress, isRedirecting]);

  if (isLoading || isRedirecting) {
    return (
      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`}>
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <LoadingState
            message={loadingMessage}
            submessage={loadingSubmessage}
            showProgress={showProgress}
            progress={progress}
            size="lg"
            dynamicMessages={dynamicMessages}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`transition-all duration-300 ease-in-out ${className}`}>
      {children}
    </div>
  );
};

export default PageTransition;
