import { LicenseType , User} from "./";

// Types
export interface LicenseCategory {
  license_category_id: string;
  license_type_id: string;
  parent_id?: string;
  name: string;
  fee?: number;
  description?: string;
  authorizes?: string;
  created_at: string;
  validity?: number;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  license_type?: LicenseType;
  parent?: LicenseCategory;
  children?: LicenseCategory[];
  creator?: User;
  updater?: User;
  // Generated code for URL-friendly routing
  code?: string;
}

export interface CreateLicenseCategoryDto {
  license_type_id: string;
  parent_id?: string;
  name: string;
  fee: number;
  validity?: number;
  description: string;
  authorizes: string;
}

export interface UpdateLicenseCategoryDto {
  license_type_id?: string;
  parent_id?: string;
  name?: string;
  fee?: number;
  validity?: number;
  description?: string;
  authorizes?: string;
}


// Types
export interface LicenseCategoryDocument {
  license_category_document_id: string;
  license_category_id: string;
  name: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  license_category?: LicenseCategory;
  creator?: User;
  updater?:User;
}

export interface CreateLicenseCategoryDocumentDto {
  license_category_id: string;
  name: string;
  is_required?: boolean;
}

export interface UpdateLicenseCategoryDocumentDto {
  license_category_id?: string;
  name?: string;
  is_required?: boolean;
}
