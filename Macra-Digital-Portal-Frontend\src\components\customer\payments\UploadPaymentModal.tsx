'use client';

import React, { useState, useEffect } from 'react';
import { customerApi } from '@/lib/customer-api';
import { useToast } from '@/contexts/ToastContext';

interface UploadPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: any;
  onSuccess?: () => void;
}

const UploadPaymentModal = ({ isOpen, onClose, invoice, onSuccess }: UploadPaymentModalProps) => {
  const { showSuccess, showError } = useToast();
  const [formData, setFormData] = useState({
    paymentMethod: '',
    transactionReference: '',
    paymentDate: '',
    amount: '',
    notes: '',
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [payments, setPayments] = useState<any[]>([]);
  const [loadingPayments, setLoadingPayments] = useState(false);
  const [totalPaid, setTotalPaid] = useState(0);

  // Fetch existing payments when modal opens
  useEffect(() => {
    if (isOpen && invoice?.invoice_id) {
      fetchInvoicePayments();
    }
  }, [isOpen, invoice?.invoice_id]);

  // Calculate balance when payments or invoice changes
  useEffect(() => {
    if (invoice && payments) {
      const totalPaid = payments.reduce((sum, payment) => {
        // Only count paid payments
        if (payment.status === 'approved') {
          return Number(sum) + parseFloat(payment.amount || '0');
        }
        return sum;
      }, 0);
      setTotalPaid(totalPaid)


      // Set default amount to remaining balance
      if (invoice.balance > 0 && !formData.amount) {
        setFormData(prev => ({
          ...prev,
          amount: invoice.balance.toString()
        }));
      }
    }
  }, [invoice, payments]);

  const fetchInvoicePayments = async () => {
    if (!invoice?.invoice_id) return;

    setLoadingPayments(true);
    try {
      console.log('🔍 Fetching payments for invoice:', invoice.invoice_id);
      const response = await customerApi.getInvoicePayments(invoice.invoice_id);
      console.log('💰 Invoice payments received:', response);
      setPayments(response.data || response || []);
    } catch (error) {
      console.error('❌ Error fetching invoice payments:', error);
      setPayments([]);
    } finally {
      setLoadingPayments(false);
    }
  };

  if (!isOpen || !invoice) return null;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setError('Please select a valid file type (JPEG, PNG, or PDF)');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
    setError(null);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.paymentMethod || !formData.transactionReference || !formData.paymentDate) {
        throw new Error('Please fill in all required fields');
      }

      if (!selectedFile) {
        throw new Error('Please select a proof of payment file');
      }

      // Create FormData for file upload
      const uploadFormData = new FormData();

      // Add the file
      if (selectedFile) {
        uploadFormData.append('file', selectedFile);
      }

      // Add payment data
      uploadFormData.append('paymentMethod', formData.paymentMethod);
      uploadFormData.append('transactionReference', formData.transactionReference);
      uploadFormData.append('paymentDate', formData.paymentDate);
      uploadFormData.append('amount', formData.amount.toString());
      uploadFormData.append('notes', formData.notes);

      // Upload proof of payment
      const response = await customerApi.uploadProofOfPayment(
        invoice.invoice_id || invoice.payment_id,
        uploadFormData
      );

      console.log('✅ Proof of payment uploaded successfully:', response);

      // Show success state briefly before closing
      setSuccess(true);
      setError(null); // Clear any previous errors

      // Show success toast notification
      showSuccess(
        `Proof of payment uploaded successfully! Your payment for invoice ${invoice.invoice_number} is now under review and will be processed shortly.`,
        6000 // Show for 6 seconds
      );

      // Wait a moment to show success state, then close modal
      setTimeout(() => {
        // Reset form
        setFormData({
          paymentMethod: '',
          transactionReference: '',
          paymentDate: '',
          amount: '',
          notes: '',
        });
        setSelectedFile(null);
        setSuccess(false);

        // Call success callback and close modal
        onSuccess?.();
        onClose();
      }, 1500); // Show success for 1.5 seconds
    } catch (err: any) {
      console.error('❌ Error uploading proof of payment:', err);
      const errorMessage = err.message || 'Failed to upload proof of payment. Please try again.';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return `${currency} ${amount.toLocaleString()}`;
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Upload Proof of Payment
              </h3>
              <div className="space-y-1">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Invoice #{invoice.invoice_number} - {formatAmount(invoice.amount, invoice.currency || 'MWK')}
                </p>
                {loadingPayments ? (
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    <i className="ri-loader-4-line animate-spin mr-1"></i>
                    Loading balance...
                  </p>
                ) : (
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                      Outstanding Balance: {formatAmount(invoice.balance, invoice.currency || 'MWK')}
                    </p>
                    {payments.length > 0 && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        ({payments.filter(p => p.status === 'PAID' || p.status === 'paid').length} payment(s) made)
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Balance Information */}
          {!loadingPayments && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200">
                    Payment Information
                  </h4>
                  <div className="mt-1 space-y-1">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Invoice Total: <span className="font-semibold">{formatAmount(invoice.amount, invoice.currency || 'MWK')}</span>
                    </p>
                    {payments.length > 0 && (
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Amount Paid: <span className="font-semibold">{formatAmount(
                          totalPaid ?? 0,
                          invoice.currency || 'MWK'
                        )}</span>
                      </p>
                    )}
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Outstanding Balance: <span className="font-bold text-lg">{formatAmount(invoice.balance, invoice.currency || 'MWK')}</span>
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  {invoice.balance === 0 ? (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <i className="ri-check-circle-fill mr-1"></i>
                      <span className="text-sm font-medium">Fully Paid</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-orange-600 dark:text-orange-400">
                      <i className="ri-time-line mr-1"></i>
                      <span className="text-sm font-medium">Payment Due</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Show message if invoice is fully paid */}
          {invoice.balance === 0 && !loadingPayments && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center">
                <i className="ri-check-circle-fill text-green-600 dark:text-green-400 mr-2"></i>
                <div>
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    Invoice Fully Paid
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    This invoice has been fully paid. No additional payment is required.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className={`space-y-6 ${invoice.balance === 0 && !loadingPayments ? 'opacity-50 pointer-events-none' : ''}`}>
            {/* Payment Method */}
            <div>
              <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Method *
              </label>
              <select
                id="paymentMethod"
                name="paymentMethod"
                value={formData.paymentMethod}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="">Select payment method</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="mobile_money">Mobile Money</option>
                <option value="cash">Cash</option>
                <option value="online_payment">Online Payment</option>
              </select>
            </div>

            {/* Transaction Reference */}
            <div>
              <label htmlFor="transactionReference" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Transaction Reference *
              </label>
              <input
                type="text"
                id="transactionReference"
                name="transactionReference"
                value={formData.transactionReference}
                onChange={handleInputChange}
                required
                placeholder="Enter transaction reference number"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            {/* Payment Date */}
            <div>
              <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Date *
              </label>
              <input
                type="date"
                id="paymentDate"
                name="paymentDate"
                value={formData.paymentDate}
                onChange={handleInputChange}
                required
                max={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            {/* Amount */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount Paid *
              </label>
              <div className="relative">
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  max={invoice.balance}
                  placeholder="Enter amount paid"
                  required
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 dark:bg-gray-700 dark:text-gray-100 ${
                    parseFloat(formData.amount) > invoice.balance && formData.amount
                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                />
                {invoice.balance > 0 && (
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, amount: invoice.balance.toString() }))}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
                  >
                    Pay Full Balance
                  </button>
                )}
              </div>
              <div className="mt-1 flex justify-between items-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Maximum: {formatAmount(invoice.balance, invoice.currency || 'MWK')}
                </p>
                {parseFloat(formData.amount) > invoice.balance && formData.amount && (
                  <p className="text-xs text-red-600 dark:text-red-400">
                    Amount exceeds outstanding balance
                  </p>
                )}
              </div>
            </div>

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Proof of Payment Document *
              </label>
              <div
                className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
                  dragActive
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  onChange={handleFileChange}
                  accept=".jpg,.jpeg,.png,.pdf"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <div className="text-center">
                  {selectedFile ? (
                    <div className="space-y-2">
                      <svg className="mx-auto h-12 w-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium text-gray-900 dark:text-gray-100">{selectedFile.name}</span>
                        <p className="text-xs">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => setSelectedFile(null)}
                        className="text-sm text-red-600 dark:text-red-400 hover:underline"
                      >
                        Remove file
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium text-blue-600 dark:text-blue-400 hover:underline">Click to upload</span> or drag and drop
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, PDF up to 5MB</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Additional Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                placeholder="Any additional information about the payment..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            {/* Success Message */}
            {success && (
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      Upload Successful!
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                      Your proof of payment has been submitted and is now under review.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && !success && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">Upload Failed</p>
                    <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || success || invoice.balance === 0 || parseFloat(formData.amount) > invoice.balance}
                className={`inline-flex items-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                  success
                    ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                    : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                }`}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Uploading...
                  </>
                ) : success ? (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Upload Successful!
                  </>
                ) : (
                  'Upload Proof of Payment'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadPaymentModal;
