import { ApplicationStatus } from "./license";

export interface AppNotification {
  notification_id: string;
  user_id: string;
  application_id: string;
  application_number: string;
  license_category_name: string;
  title: string;
  message: string;
  type: string;
  status: 'unread' | 'read';
  priority: string;
  created_at: string;
  read_at?: string;
  entity_id?: string;
  entity_type?: string;
  metadata?: {
    old_status?: ApplicationStatus;
    new_status?: ApplicationStatus;
    step?: number;
    progress_percentage?: number;
  };
}

export interface NotificationSummary {
  total_count: number;
  unread_count: number;
  notifications: AppNotification[];
}