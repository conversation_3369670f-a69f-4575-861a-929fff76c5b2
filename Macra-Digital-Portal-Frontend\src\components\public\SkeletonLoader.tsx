'use client';

import React from 'react';

interface SkeletonLoaderProps {
  type: 'card' | 'stats' | 'form' | 'list' | 'text' | 'verification-result' | 'chart';
  count?: number;
  className?: string;
  animated?: boolean;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  type,
  count = 1,
  className = '',
  animated = true
}) => {
  const baseClasses = animated 
    ? 'bg-gray-200 dark:bg-gray-700 animate-pulse rounded' 
    : 'bg-gray-200 dark:bg-gray-700 rounded';

  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
            <div className="flex items-center space-x-4 mb-4">
              <div className={`w-12 h-12 ${baseClasses}`} />
              <div className="flex-1 space-y-2">
                <div className={`h-4 ${baseClasses} w-3/4`} />
                <div className={`h-3 ${baseClasses} w-1/2`} />
              </div>
            </div>
            <div className="space-y-3">
              <div className={`h-3 ${baseClasses} w-full`} />
              <div className={`h-3 ${baseClasses} w-5/6`} />
              <div className={`h-3 ${baseClasses} w-4/6`} />
            </div>
            <div className="mt-4 flex justify-between items-center">
              <div className={`h-8 w-24 ${baseClasses}`} />
              <div className={`h-4 w-16 ${baseClasses}`} />
            </div>
          </div>
        );

      case 'stats':
        return (
          <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
            <div className="flex items-center justify-between mb-4">
              <div className="space-y-2">
                <div className={`h-4 w-24 ${baseClasses}`} />
                <div className={`h-8 w-16 ${baseClasses}`} />
                <div className={`h-3 w-20 ${baseClasses}`} />
              </div>
              <div className={`w-12 h-12 rounded-full ${baseClasses}`} />
            </div>
          </div>
        );

      case 'form':
        return (
          <div className={`space-y-6 ${className}`}>
            <div className="space-y-2">
              <div className={`h-4 w-24 ${baseClasses}`} />
              <div className={`h-12 w-full ${baseClasses}`} />
            </div>
            <div className="space-y-2">
              <div className={`h-4 w-32 ${baseClasses}`} />
              <div className={`h-12 w-full ${baseClasses}`} />
            </div>
            <div className={`h-12 w-full ${baseClasses}`} />
          </div>
        );

      case 'list':
        return (
          <div className={`space-y-4 ${className}`}>
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className={`w-10 h-10 rounded-full ${baseClasses}`} />
                <div className="flex-1 space-y-2">
                  <div className={`h-4 ${baseClasses} w-3/4`} />
                  <div className={`h-3 ${baseClasses} w-1/2`} />
                </div>
                <div className={`h-6 w-16 ${baseClasses}`} />
              </div>
            ))}
          </div>
        );

      case 'text':
        return (
          <div className={`space-y-3 ${className}`}>
            {Array.from({ length: count }).map((_, index) => (
              <div key={index} className={`h-4 ${baseClasses} w-full`} />
            ))}
          </div>
        );

      case 'verification-result':
        return (
          <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
            {/* Header */}
            <div className="text-center mb-6">
              <div className={`w-16 h-16 rounded-full mx-auto mb-4 ${baseClasses}`} />
              <div className={`h-6 w-48 mx-auto mb-2 ${baseClasses}`} />
              <div className={`h-4 w-32 mx-auto ${baseClasses}`} />
            </div>
            
            {/* License Details */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="space-y-2">
                    <div className={`h-3 w-24 ${baseClasses}`} />
                    <div className={`h-4 w-full ${baseClasses}`} />
                  </div>
                ))}
              </div>
            </div>
            
            {/* Action Button */}
            <div className="mt-6 text-center">
              <div className={`h-12 w-40 mx-auto ${baseClasses}`} />
            </div>
          </div>
        );

      case 'chart':
        return (
          <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
            <div className={`h-6 w-48 mb-6 ${baseClasses}`} />
            <div className="space-y-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className={`w-16 h-4 ${baseClasses}`} />
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-6 relative overflow-hidden">
                    <div 
                      className={`h-6 ${baseClasses}`}
                      style={{ width: `${Math.random() * 80 + 20}%` }}
                    />
                  </div>
                  <div className={`w-20 h-4 ${baseClasses}`} />
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div className={`${baseClasses} h-20 w-full ${className}`} />
        );
    }
  };

  if (count === 1) {
    return renderSkeleton();
  }

  return (
    <div className="space-y-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>{renderSkeleton()}</div>
      ))}
    </div>
  );
};

// Specific skeleton components for common use cases
export const StatsSkeleton: React.FC<{ count?: number; className?: string }> = ({ 
  count = 4, 
  className = '' 
}) => (
  <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
    {Array.from({ length: count }).map((_, index) => (
      <SkeletonLoader key={index} type="stats" />
    ))}
  </div>
);

export const CardsSkeleton: React.FC<{ count?: number; className?: string }> = ({ 
  count = 3, 
  className = '' 
}) => (
  <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
    {Array.from({ length: count }).map((_, index) => (
      <SkeletonLoader key={index} type="card" />
    ))}
  </div>
);

export const FormSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <SkeletonLoader type="form" className={className} />
);

export const VerificationResultSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <SkeletonLoader type="verification-result" className={className} />
);

export const ChartSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <SkeletonLoader type="chart" className={className} />
);

export default SkeletonLoader;
