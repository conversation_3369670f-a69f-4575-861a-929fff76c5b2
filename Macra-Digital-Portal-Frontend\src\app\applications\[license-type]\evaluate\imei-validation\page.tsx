'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { imeiValidationService } from '@/services/imeiValidationService';
import { Application } from '@/types';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import EvaluationStepValidator from '@/components/evaluation/EvaluationStepValidator';

const IMEIValidationEvaluationPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // URL parameters
  const applicationId = searchParams.get('application_id');
  const licenseCategoryId = searchParams.get('license_category_id');
  const licenseType = searchParams.get('license_type') || 'type_approval_certificate';

  // Early return if required params are missing
  if (!applicationId) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Missing Application ID
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Application ID is required to access this evaluation page.
          </p>
        </div>
      </div>
    );
  }

  // State
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<Application | null>(null);
  const [imeiData, setImeiData] = useState<any>(null);
  const [evaluationStatus, setEvaluationStatus] = useState<string>('pending');
  const [evaluationComment, setEvaluationComment] = useState<string>('');

  // Dynamic navigation
  const {
    nextStep,
    previousStep,
    handleNext,
    handlePrevious
  } = useDynamicNavigation({
    currentStepRoute: 'imei-validation',
    licenseCategoryId,
    applicationId
  });

  // Load application and IMEI data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setLoading(true);
        setError(null);

        // Load application data
        const app = await applicationService.getApplication(applicationId);
        setApplication(app);

        // Load IMEI validation data
        try {
          const imeiValidationData = await imeiValidationService.getIMEIForApplication(applicationId);
          setImeiData(imeiValidationData);
        } catch (imeiError) {
          console.warn('API IMEI data not found, checking localStorage:', applicationId);

          // Fallback to localStorage
          try {
            const localStorageKey = `imei_validation_${applicationId}`;
            const localData = localStorage.getItem(localStorageKey);
            if (localData) {
              const parsedData = JSON.parse(localData);
              console.log('✅ Found IMEI data in localStorage:', parsedData);
              setImeiData({
                application_id: applicationId,
                imei: parsedData.imei,
                validation_result: parsedData.validation_result,
                modify_device: parsedData.modify_device,
                created_at: parsedData.timestamp,
                updated_at: parsedData.timestamp
              });
            } else {
              console.log('ℹ️ No IMEI data found in localStorage either');
              setImeiData(null);
            }
          } catch (localError) {
            console.error('Error reading from localStorage:', localError);
            setImeiData(null);
          }
        }

      } catch (err: any) {
        console.error('Error loading data:', err);
        setError(err.message || 'Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Handle evaluation status update
  const handleStatusUpdate = async (status: string, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      setEvaluationStatus(status);
      setEvaluationComment(comment);
      
      // Here you would typically save the evaluation to the backend
      console.log('IMEI Validation Evaluation:', {
        applicationId,
        status,
        comment,
        imeiData
      });

      // For now, just show success
      console.log('IMEI validation evaluation updated successfully');
    } catch (err) {
      console.error('Error updating evaluation:', err);
      setError('Failed to update evaluation status');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle navigation
  const handleNextStep = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePreviousStep = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  if (authLoading || loading) {
    return (
      <EvaluationLayout
        title="Equipment Details Evaluation"
        onNext={handleNextStep}
        onPrevious={handlePreviousStep}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        isLoading={true}
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading equipment details...</span>
        </div>
      </EvaluationLayout>
    );
  }

  if (error) {
    return (
      <EvaluationLayout
        title="Equipment Details Evaluation"
        onNext={handleNextStep}
        onPrevious={handlePreviousStep}
        showNextButton={false}
        showPreviousButton={!!previousStep}
      >
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-500 text-xl mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800">Error Loading Data</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </EvaluationLayout>
    );
  }

  return (
    <EvaluationStepValidator
      licenseType={licenseType}
      currentStepRoute="imei-validation"
      applicationId={applicationId}
      licenseCategoryId={licenseCategoryId || undefined}
    >
      <EvaluationLayout
        title="Equipment Details Evaluation"
        subtitle="Review and evaluate the equipment details and IMEI validation"
        onNext={handleNextStep}
        onPrevious={handlePreviousStep}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        isLoading={isSubmitting}
      >
      <div className="space-y-6">
        {/* Application Info */}
        {application && (
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Application Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700 dark:text-blue-300">Application Number:</span>
                <span className="ml-2 font-medium">{application.application_number}</span>
              </div>
              <div>
                <span className="text-blue-700 dark:text-blue-300">License Type:</span>
                <span className="ml-2 font-medium">{application.license_category?.license_type?.name}</span>
              </div>
            </div>
          </div>
        )}

        {/* IMEI Validation Data */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Equipment Details & IMEI Validation
          </h3>

          {imeiData ? (
            <div className="space-y-4">
              {/* IMEI Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    IMEI Number
                  </label>
                  <p className="text-sm text-gray-900 dark:text-gray-100 font-mono bg-gray-50 dark:bg-gray-700 p-2 rounded">
                    {imeiData.imei || 'Not provided'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Validation Status
                  </label>
                  <p className={`text-sm p-2 rounded ${
                    imeiData.validation_result?.isValid 
                      ? 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-200'
                      : 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-200'
                  }`}>
                    {imeiData.validation_result?.status || 'Not validated'}
                  </p>
                </div>
              </div>

              {/* Validation Result Details */}
              {imeiData.validation_result && (
                <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Validation Details
                  </h4>
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    <p><strong>Message:</strong> {imeiData.validation_result.message || 'No message'}</p>
                    {imeiData.validation_result.deviceInfo && (
                      <div className="mt-2">
                        <p><strong>Device Information:</strong></p>
                        <ul className="list-disc list-inside ml-4 mt-1">
                          {imeiData.validation_result.deviceInfo.manufacturer && (
                            <li>Manufacturer: {imeiData.validation_result.deviceInfo.manufacturer}</li>
                          )}
                          {imeiData.validation_result.deviceInfo.model_name && (
                            <li>Model: {imeiData.validation_result.deviceInfo.model_name}</li>
                          )}
                          {imeiData.validation_result.deviceInfo.device_type && (
                            <li>Type: {imeiData.validation_result.deviceInfo.device_type}</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <i className="ri-smartphone-line text-4xl text-gray-400 mb-4"></i>
              <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No Equipment Details Found
              </h4>
              <p className="text-gray-600 dark:text-gray-400">
                No IMEI validation data has been submitted for this application.
              </p>
            </div>
          )}
        </div>

        {/* Evaluation Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Evaluation
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Evaluation Status
              </label>
              <select
                value={evaluationStatus}
                onChange={(e) => setEvaluationStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="pending">Pending Review</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="requires_clarification">Requires Clarification</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Evaluation Comments
              </label>
              <textarea
                value={evaluationComment}
                onChange={(e) => setEvaluationComment(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="Enter your evaluation comments..."
              />
            </div>

            <button
              onClick={() => handleStatusUpdate(evaluationStatus, evaluationComment)}
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <i className="ri-save-line mr-2"></i>
                  Save Evaluation
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </EvaluationLayout>
    </EvaluationStepValidator>
  );
};

export default IMEIValidationEvaluationPage;
