'use client';

import React from 'react';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Calendar, 
  Building, 
  FileText, 
  Clock,
  Shield,
  Copy,
  ExternalLink
} from 'lucide-react';
import { PublicLicenseInfo, PublicLicenseStatus } from '@/types/public';

interface LicenseVerificationResultsProps {
  licenseInfo: PublicLicenseInfo;
  onVerifyAnother?: () => void;
}

export default function LicenseVerificationResults({ 
  licenseInfo, 
  onVerifyAnother 
}: LicenseVerificationResultsProps) {
  
  // Get status color and icon
  const getStatusDisplay = (status: string, isValid: boolean) => {
    if (!isValid) {
      return {
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: <XCircle className="h-5 w-5" />,
        text: 'Invalid/Expired'
      };
    }

    switch (status.toLowerCase()) {
      case PublicLicenseStatus.ACTIVE:
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: <CheckCircle className="h-5 w-5" />,
          text: 'Active'
        };
      case PublicLicenseStatus.EXPIRED:
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: <XCircle className="h-5 w-5" />,
          text: 'Expired'
        };
      case PublicLicenseStatus.SUSPENDED:
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          icon: <AlertTriangle className="h-5 w-5" />,
          text: 'Suspended'
        };
      case PublicLicenseStatus.REVOKED:
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: <XCircle className="h-5 w-5" />,
          text: 'Revoked'
        };
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          icon: <AlertTriangle className="h-5 w-5" />,
          text: status
        };
    }
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Copy license number to clipboard
  const copyLicenseNumber = async () => {
    try {
      await navigator.clipboard.writeText(licenseInfo.licenseNumber);
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy license number:', err);
    }
  };

  const statusDisplay = getStatusDisplay(licenseInfo.status, licenseInfo.isValid);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header with Status */}
      <div className={`${statusDisplay.bgColor} ${statusDisplay.borderColor} border-b px-6 py-4`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={statusDisplay.color}>
              {statusDisplay.icon}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                License Verification Result
              </h2>
              <p className={`text-sm ${statusDisplay.color} font-medium`}>
                Status: {statusDisplay.text}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-6 w-6 text-blue-600" />
            <span className="text-sm text-gray-600">Verified by MACRA</span>
          </div>
        </div>
      </div>

      {/* License Details */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* License Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">License Information</h3>
            
            {/* License Number */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">License Number:</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="font-mono text-sm font-medium">{licenseInfo.licenseNumber}</span>
                <button
                  onClick={copyLicenseNumber}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  title="Copy license number"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* License Type */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">License Type:</span>
              </div>
              <span className="text-sm font-medium">{licenseInfo.licenseType}</span>
            </div>

            {/* Organization */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Organization:</span>
              </div>
              <span className="text-sm font-medium">{licenseInfo.organizationName}</span>
            </div>
          </div>

          {/* Dates and Validity */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Validity Information</h3>
            
            {/* Issue Date */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Issue Date:</span>
              </div>
              <span className="text-sm font-medium">{formatDate(licenseInfo.issueDate)}</span>
            </div>

            {/* Expiry Date */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Expiry Date:</span>
              </div>
              <span className={`text-sm font-medium ${
                licenseInfo.isValid ? 'text-gray-900' : 'text-red-600'
              }`}>
                {formatDate(licenseInfo.expiryDate)}
              </span>
            </div>

            {/* Verification Time */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-600" />
                <span className="text-sm text-gray-600">Verified At:</span>
              </div>
              <span className="text-sm font-medium">
                {new Date(licenseInfo.verifiedAt).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* Validity Status Banner */}
        <div className={`mt-6 p-4 rounded-md border ${statusDisplay.bgColor} ${statusDisplay.borderColor}`}>
          <div className="flex items-center space-x-3">
            <div className={statusDisplay.color}>
              {statusDisplay.icon}
            </div>
            <div>
              <p className={`font-medium ${statusDisplay.color}`}>
                {licenseInfo.isValid 
                  ? 'This license is currently valid and active.'
                  : 'This license is not valid or has expired.'
                }
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Verification performed on {new Date(licenseInfo.verifiedAt).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex flex-col sm:flex-row gap-3">
          {onVerifyAnother && (
            <button
              onClick={onVerifyAnother}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Verify Another License
            </button>
          )}
          <button
            onClick={() => window.print()}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Print Results
          </button>
          <a
            href="https://macra.mw"
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors text-center inline-flex items-center justify-center space-x-2"
          >
            <span>Visit MACRA</span>
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>
      </div>
    </div>
  );
}
