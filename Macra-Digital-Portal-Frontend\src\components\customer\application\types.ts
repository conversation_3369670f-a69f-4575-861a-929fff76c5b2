// Application Form Types
// Import centralized types
export type {
  ApplicantInfoData,
  ShareholderData,
  DirectorData,
  CompanyProfileData,
  ManagementData,
  ProfessionalServicesData,
  BusinessInfoData,
  ServiceScopeData,
  BusinessPlanData,
  LegalHistoryData,
  ApplicationFormData,
  ApplicationFormComponentProps
} from '@/types';

// Additional interfaces specific to customer application components
export interface ManagementTeamMember {
  name: string;
  position: string;
  qualifications: string;
  experience: string;
}

// Independent Step Component Props Interface
export interface IndependentStepProps {
  applicationId?: string | null;
  licenseTypeId?: string;
  licenseCategoryId: string;
  isEditMode?: boolean;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstStep?: boolean;
  isLastStep?: boolean;
  onStepComplete?: (stepId: string, data?: any) => void;
  onStepError?: (stepId: string, errors: any) => void;
  onNavigate?: (direction: 'next' | 'previous') => void;
}

// Legacy Step Component Props Interface (for backward compatibility)
export interface StepComponentProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  isLoading?: boolean;
}

// Step completion status
export interface StepStatus {
  stepId: string;
  completed: boolean;
  hasErrors: boolean;
  lastSaved?: Date;
  data?: any;
}
