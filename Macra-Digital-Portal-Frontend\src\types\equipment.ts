export interface DeviceData {
  device_id?: string;
  application_id?: string;
  manufacturer_id?: string;
  equipment_category_id?: string;
  imei: string;
  device_type: string;
  model_name: string;
  device_serial_number: string;
  approval_status?: string;
  device_approval_number?: string;
  device_approval_date?: string;
  approval_notes?: string;
}

export interface Manufacturer {
  manufacturer_id?: string;
  manufacturer_name: string;
  manufacturer_country_origin: string;
  manufacturer_region?: string;
  manufacturer_email?: string;
  manufacturer_phone?: string;
  manufacturer_website: string;
  approval_status?: string;
  manufacturer_approval_number?: string;
  manufacturer_approval_date?: string;
  approval_certification_standard?: string;
  equipment_types?: string;
  approval_notes?: string;
}

export interface EquipmentDetailsFormData {
  imei: string;
  brand_trade_name: string;
  model_number: string;
  product_type_name: string;
  manufacturer_name: string;
  manufacturer_address: string;
  manufacturer_country: string;
  manufacturer_contact_person: string;
  manufacturer_email: string;
  manufacturer_phone: string;
  manufacturer_website: string;
}