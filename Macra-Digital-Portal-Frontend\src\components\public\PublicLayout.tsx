'use client';

import React from 'react';
import Link from 'next/link';
import Head from 'next/head';
import { Shield, Home, BarChart3, HelpCircle } from 'lucide-react';
import { PublicErrorBoundary } from './PublicErrorBoundary';
import { MacraOrganizationSchema, WebsiteSchema, LicenseVerificationServiceSchema } from './StructuredData';

interface PublicLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export default function PublicLayout({ children, title, description }: PublicLayoutProps) {
  return (
    <>
      {/* Structured Data */}
      <MacraOrganizationSchema />
      <WebsiteSchema />
      <LicenseVerificationServiceSchema />

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Public Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Brand */}
            <div className="flex items-center">
              <Link href="/public" className="flex items-center space-x-2">
                <Shield className="h-8 w-8 text-primary" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">MACRA</h1>
                  <p className="text-xs text-gray-500 dark:text-gray-400">License Verification</p>
                </div>
              </Link>
            </div>

            {/* Public Navigation */}
            <nav className="hidden md:flex space-x-8">
              <Link
                href="/public"
                className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
              >
                <Home className="h-4 w-4" />
                <span>Home</span>
              </Link>
              <Link
                href="/public/verify"
                className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
              >
                <Shield className="h-4 w-4" />
                <span>Verify License</span>
              </Link>
              <Link
                href="/public/statistics"
                className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
              >
                <BarChart3 className="h-4 w-4" />
                <span>Statistics</span>
              </Link>
              <Link
                href="/public/help"
                className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
              >
                <HelpCircle className="h-4 w-4" />
                <span>Help</span>
              </Link>
            </nav>

            {/* Portal Access */}
            <div className="flex items-center space-x-4">
              <Link
                href="/customer/auth/login"
                className="text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
              >
                Customer Portal
              </Link>
              <Link
                href="/auth/login"
                className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors shadow-sm"
              >
                Staff Portal
              </Link>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="max-w-7xl mx-auto px-4 py-2">
            <div className="flex justify-around">
              <Link
                href="/public"
                className="flex flex-col items-center py-2 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
              >
                <Home className="h-5 w-5" />
                <span className="text-xs mt-1">Home</span>
              </Link>
              <Link
                href="/public/verify"
                className="flex flex-col items-center py-2 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
              >
                <Shield className="h-5 w-5" />
                <span className="text-xs mt-1">Verify</span>
              </Link>
              <Link
                href="/public/statistics"
                className="flex flex-col items-center py-2 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
              >
                <BarChart3 className="h-5 w-5" />
                <span className="text-xs mt-1">Stats</span>
              </Link>
              <Link
                href="/public/help"
                className="flex flex-col items-center py-2 text-gray-600 dark:text-gray-400 hover:text-primary transition-colors"
              >
                <HelpCircle className="h-5 w-5" />
                <span className="text-xs mt-1">Help</span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Page Title Section */}
      {(title || description) && (
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {title && (
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">{title}</h1>
            )}
            {description && (
              <p className="text-gray-600 dark:text-gray-400">{description}</p>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <PublicErrorBoundary>
            {children}
          </PublicErrorBoundary>
        </div>
      </main>

      {/* Public Footer */}
      <footer className="bg-gray-800 dark:bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* About Section */}
            <div>
              <h3 className="text-lg font-semibold mb-4">About MACRA</h3>
              <p className="text-gray-300 dark:text-gray-400 text-sm">
                The Malawi Communications Regulatory Authority (MACRA) is responsible for
                regulating the communications, broadcasting, and postal services sectors in Malawi.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/public/verify" className="text-gray-300 dark:text-gray-400 hover:text-white transition-colors">
                    License Verification
                  </Link>
                </li>
                <li>
                  <Link href="/public/statistics" className="text-gray-300 dark:text-gray-400 hover:text-white transition-colors">
                    Statistics
                  </Link>
                </li>
                <li>
                  <Link href="/public/help" className="text-gray-300 dark:text-gray-400 hover:text-white transition-colors">
                    Help & Support
                  </Link>
                </li>
                <li>
                  <a href="https://macra.mw" className="text-gray-300 dark:text-gray-400 hover:text-white transition-colors">
                    Official Website
                  </a>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <div className="text-gray-300 dark:text-gray-400 text-sm space-y-2">
                <p>Malawi Communications Regulatory Authority</p>
                <p>Private Bag 261, Lilongwe 3, Malawi</p>
                <p>Phone: +265 1 770 100</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 dark:border-gray-600 mt-8 pt-8 text-center text-sm text-gray-300 dark:text-gray-400">
            <p>&copy; {new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.</p>
          </div>
        </div>
      </footer>
      </div>
    </>
  );
}
