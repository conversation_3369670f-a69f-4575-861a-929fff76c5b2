'use client';

import React, { useState, useEffect } from 'react';
import { invoiceService } from '@/services/invoiceService';
import { paymentService } from '@/services/paymentService';
import { adminPaymentService } from '@/services/adminPaymentService';
import { documentService } from '@/services/documentService';
import { useToast } from '@/contexts/ToastContext';
import { formatCurrency } from '@/utils/formatters';
import { Invoice, Payment } from '@/types/invoice';
import { Task } from '@/types';
import ActivityHistory from '../common/ActivityHistory';
import ConfirmationModal from '../common/ConfirmationModal';
import DocumentPreviewModal from '../documents/DocumentPreviewModal';
import ActivityNotesModal from '../evaluation/ActivityNotesModal';

interface PaymentTaskComponentProps {
  task: Task;
  onTaskUpdate: () => void;
}


const PaymentTaskComponent: React.FC<PaymentTaskComponentProps> = ({ task, onTaskUpdate }) => {
  const { showToast } = useToast();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingPayment, setProcessingPayment] = useState<string | null>(null);
  const [isCommunicationModalOpen, setIsCommunicationModalOpen] = useState(false);

  // Confirmation modal states
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  // Document preview states
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [isDocumentPreviewOpen, setIsDocumentPreviewOpen] = useState(false);
  const [paymentDocuments, setPaymentDocuments] = useState<Record<string, any[]>>({});

  useEffect(() => {
    if (task && task.entity_type == 'payment' && task.entity_id) {
      loadInvoiceAndPayments();
    }
  }, [task]);

  const loadInvoiceAndPayments = async () => {
    try {
      setLoading(true);

      // Load invoice details
      const payment = await paymentService.getPaymentById(task.entity_id!);
      const invoice = await invoiceService.getInvoiceById(payment.invoice_id!);
      if(invoice) setInvoice(invoice);

      // Load payments for this invoice
      const paymentsResponse = await paymentService.getInvoicePayments(invoice.invoice_id!);
      if (paymentsResponse.success && paymentsResponse.data) {
        setPayments(paymentsResponse.data);
        // Load documents for each payment
        await loadPaymentDocuments(paymentsResponse.data);
      }
    } catch (error) {
      showToast('Failed to load invoice and payment details', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Load documents for payments (proof of payment documents)
  const loadPaymentDocuments = async (payments: Payment[]) => {
    try {
      const documentsMap: Record<string, any[]> = {};

      for (const payment of payments) {
        try {
          // Fetch documents for this payment using polymorphic relationship
          const response = await documentService.getDocumentsByEntity('payment', payment.payment_id);
          if (response.success && response.data) {
            documentsMap[payment.payment_id] = response.data;
          }
        } catch (error) {
          // Continue loading other payment documents even if one fails
          documentsMap[payment.payment_id] = [];
        }
      }

      setPaymentDocuments(documentsMap);
    } catch (error) {
      console.error('Error loading payment documents:', error);
    }
  };

  // Handle document preview
  const handleDocumentPreview = (document: any) => {
    setSelectedDocument(document);
    setIsDocumentPreviewOpen(true);
  };

  // Close document preview
  const handleCloseDocumentPreview = () => {
    setIsDocumentPreviewOpen(false);
    setSelectedDocument(null);
  };

  // Handler to show approve confirmation modal
  const handleApproveClick = (paymentId: string) => {
    setSelectedPaymentId(paymentId);
    setShowApproveModal(true);
  };

  // Handler to show reject confirmation modal
  const handleRejectClick = (paymentId: string) => {
    setSelectedPaymentId(paymentId);
    setRejectionReason('');
    setShowRejectModal(true);
  };

  // Actual approve payment function
  const handleApprovePayment = async () => {
    if (!selectedPaymentId) return;



    try {
      setProcessingPayment(selectedPaymentId);

      // Use the admin payment service for standardized API handling
      const result = await adminPaymentService.approvePayment(selectedPaymentId);

      if (!result.success) {
        // Handle specific error responses from the API
        const errorCode = result.error || 'UNKNOWN_ERROR';

        switch (errorCode) {
          case 'PAYMENT_NOT_FOUND':
            showToast('Payment not found. It may have been deleted or moved.', 'error');
            break;
          case 'PAYMENT_UPDATE_CONFLICT':
            showToast('Payment update conflict. Another user may have modified this payment.', 'error');
            break;
          case 'INVALID_PAYMENT_DATA':
            showToast('Invalid payment data provided. Please check the payment details.', 'error');
            break;
          default:
            showToast(result.message || 'Failed to approve payment', 'error');
        }
        return;
      }

      // Show success message based on the result
      showToast(result.message, 'success');

      await loadInvoiceAndPayments();
      onTaskUpdate();
      setShowApproveModal(false);
      setSelectedPaymentId(null);
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        showToast('Network error. Please check your connection and try again.', 'error');
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Failed to approve payment';
        showToast(errorMessage, 'error');
      }
    } finally {
      setProcessingPayment(null);
    }
  };

  // Actual reject payment function
  const handleRejectPayment = async () => {
    if (!selectedPaymentId || !rejectionReason.trim()) {
      showToast('Please provide a reason for rejection', 'error');
      return;
    }

    try {
      setProcessingPayment(selectedPaymentId);

      // Use the admin payment service for standardized API handling
      const result = await adminPaymentService.rejectPayment(selectedPaymentId, rejectionReason.trim());

      if (!result.success) {
        // Handle specific error responses from the API
        const errorCode = result.error || 'UNKNOWN_ERROR';

        switch (errorCode) {
          case 'PAYMENT_NOT_FOUND':
            showToast('Payment not found. It may have been deleted or moved.', 'error');
            break;
          case 'PAYMENT_UPDATE_CONFLICT':
            showToast('Payment update conflict. Another user may have modified this payment.', 'error');
            break;
          case 'INVALID_PAYMENT_DATA':
            showToast('Invalid payment data provided. Please check the payment details.', 'error');
            break;
          default:
            showToast(result.message || 'Failed to reject payment', 'error');
        }
        return;
      }

      showToast(result.message, 'success');
      await loadInvoiceAndPayments();
      onTaskUpdate();
      setShowRejectModal(false);
      setSelectedPaymentId(null);
      setRejectionReason('');
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        showToast('Network error. Please check your connection and try again.', 'error');
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Failed to reject payment';
        showToast(errorMessage, 'error');
      }
    } finally {
      setProcessingPayment(null);
    }
  };

  const handleEmailClient = () => {
    setIsCommunicationModalOpen(true);
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'processing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getInvoiceStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mr-3"></div>
        <span className="text-gray-600 dark:text-gray-400">Loading payment details...</span>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-8">
        <div className="bg-yellow-100 dark:bg-yellow-900/20 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <i className="ri-error-warning-line text-2xl text-yellow-600 dark:text-yellow-400"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Invoice Not Found</h3>
        <p className="text-gray-600 dark:text-gray-400">
          The invoice associated with this task could not be found.
        </p>
      </div>
    );
  }
  const pendingPayments = payments.filter(payment => payment.status.toLowerCase() === 'pending');
  const totalPaid = invoice.amount - (invoice.balance ?? 0);
  return (
    <div className="space-y-6">
      {/* Invoice Details */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-file-list-3-line mr-2 text-blue-600"></i>
              Invoice Details
            </h2>
            {invoice.client && (
              <button
                onClick={handleEmailClient}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                title="Email Client"
              >
                <i className="ri-mail-line mr-2"></i>
                Email Client
              </button>
            )}
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Invoice Information</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Invoice Number:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{invoice.invoice_number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Amount:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{formatCurrency(invoice.amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getInvoiceStatusColor(invoice.status)}`}>
                    {invoice.status.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Due Date:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {new Date(invoice.due_date).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Client Information */}
            {invoice.client && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Client Information</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Client:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {`${invoice.client.name}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {invoice.client.email}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Payment Summary */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <i className="ri-money-dollar-circle-line text-blue-600 dark:text-blue-400"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Amount</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{formatCurrency(invoice.amount)}</p>
                  </div>
                </div>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <i className="ri-check-line text-green-600 dark:text-green-400"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Paid Amount</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{formatCurrency(totalPaid)}</p>
                  </div>
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                    <i className="ri-time-line text-orange-600 dark:text-orange-400"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Remaining</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{formatCurrency(invoice.balance ?? 0)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pending Payments for Approval */}
      {pendingPayments.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-time-line mr-2 text-orange-600"></i>
              Pending Payments ({pendingPayments.length})
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {pendingPayments.map((payment) => (
                <div key={payment.payment_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Amount:</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{formatCurrency(payment.amount)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Method:</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{payment.payment_method}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Date:</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {payment.paid_date ? new Date(payment.paid_date).toLocaleDateString() : new Date(payment.issue_date).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div className="space-y-2">
                            {payment.transaction_reference && (
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400">Reference:</span>
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{payment.transaction_reference}</span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                                {payment.status.toUpperCase()}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Submitted:</span>
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {new Date(payment.created_at).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {payment.notes && (
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>Notes:</strong> {payment.notes}
                          </p>
                        </div>
                      )}

                      {/* Proof of Payment Documents */}
                      {paymentDocuments[payment.payment_id] && paymentDocuments[payment.payment_id].length > 0 && (
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="space-y-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Proof of Payment:</span>
                            {paymentDocuments[payment.payment_id].map((document) => (
                              <div key={document.document_id} className="flex items-center justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                  {document.file_name}
                                </span>
                                <div className="flex items-center space-x-2">
                                  {documentService.isPreviewable(document.mime_type) && (
                                    <button
                                      onClick={() => handleDocumentPreview(document)}
                                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                      title="Preview document"
                                    >
                                      <i className="ri-eye-line mr-1"></i>
                                      Preview
                                    </button>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button
                        onClick={() => handleApproveClick(payment.payment_id)}
                        disabled={processingPayment === payment.payment_id}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                      >
                        {processingPayment === payment.payment_id ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <i className="ri-check-line mr-1"></i>
                            Approve
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => handleRejectClick(payment.payment_id)}
                        disabled={processingPayment === payment.payment_id}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        <i className="ri-close-line mr-1"></i>
                        Reject
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* All Payments History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <i className="ri-history-line mr-2 text-purple-600"></i>
            Payment History ({payments.length})
          </h2>
        </div>
        <div className="p-6">
          {payments.length === 0 ? (
            <div className="text-center py-8">
              <i className="ri-money-dollar-circle-line text-4xl text-gray-400 mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Payments Yet</h3>
              <p className="text-gray-600 dark:text-gray-400">
                No payments have been submitted for this invoice.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Method
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Reference
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Proof
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {payments.map((payment) => (
                    <tr key={payment.payment_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {formatCurrency(payment.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {payment.payment_method}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {payment.transaction_reference || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                          {payment.status.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {payment.paid_date ? new Date(payment.paid_date).toLocaleDateString() : new Date(payment.issue_date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {paymentDocuments[payment.payment_id] && paymentDocuments[payment.payment_id].length > 0 ? (
                          <div className="flex items-center space-x-2">
                            {paymentDocuments[payment.payment_id].slice(0, 1).map((document) => (
                              <div key={document.document_id} className="flex items-center space-x-1">
                                {documentService.isPreviewable(document.mime_type) && (
                                  <button
                                    onClick={() => handleDocumentPreview(document)}
                                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                    title={`Preview ${document.file_name}`}
                                  >
                                    <i className="ri-eye-line"></i> View
                                  </button>
                                )}

                              </div>
                            ))}
                            {paymentDocuments[payment.payment_id].length > 1 && (
                              <span className="text-xs text-gray-400">
                                +{paymentDocuments[payment.payment_id].length - 1} more
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">No documents</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* All Activity notes History */}
      <ActivityHistory
        entityType="invoice"
        entityId={task.entity_id!}
        title="Payment Activity History"
        showSearch={true}
        showFilters={false}
        maxHeight="max-h-96"
        className="border-0 rounded-none">
      </ActivityHistory>

      {/* Communication Center Modal */}
      {invoice && invoice.invoice_id && (
        <ActivityNotesModal
          isOpen={isCommunicationModalOpen}
          onClose={() => setIsCommunicationModalOpen(false)}
          entityType="invoice"
          entityId={invoice.invoice_id}
          initialEmails={invoice.client?.email}
        />
      )}

      {/* Approve Payment Confirmation Modal */}
      <ConfirmationModal
        isOpen={showApproveModal}
        onClose={() => {
          setShowApproveModal(false);
          setSelectedPaymentId(null);
        }}
        onConfirm={handleApprovePayment}
        title="Approve Payment"
        message={
          <div className="space-y-3">
            <p>Are you sure you want to approve this payment?</p>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-sm">
              <p className="font-medium text-green-900 dark:text-green-100 mb-2">This action will:</p>
              <ul className="text-green-700 dark:text-green-300 space-y-1">
                <li>• Mark the payment as approved and paid</li>
                <li>• Update the invoice status if fully paid</li>
                <li>• Send email notification to the client</li>
                <li>• Create an activity log entry</li>
              </ul>
            </div>
          </div>
        }
        confirmText="Approve Payment"
        confirmVariant="primary"
        loading={processingPayment === selectedPaymentId}
        icon={
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-green-600 dark:text-green-400 text-xl"></i>
            </div>
          </div>
        }
      />

      {/* Reject Payment Confirmation Modal */}
      <ConfirmationModal
        isOpen={showRejectModal}
        onClose={() => {
          setShowRejectModal(false);
          setSelectedPaymentId(null);
          setRejectionReason('');
        }}
        onConfirm={handleRejectPayment}
        title="Reject Payment"
        message={
          <div className="space-y-3">
            <p>Are you sure you want to reject this payment?</p>
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 text-sm">
              <p className="font-medium text-red-900 dark:text-red-100 mb-2">This action will:</p>
              <ul className="text-red-700 dark:text-red-300 space-y-1">
                <li>• Mark the payment as cancelled/rejected</li>
                <li>• Notify the client of the rejection</li>
                <li>• Create an activity log entry with the reason</li>
              </ul>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason for rejection *
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100 resize-none"
                placeholder="Please provide a detailed reason for rejecting this payment..."
                required
              />
            </div>
          </div>
        }
        confirmText="Reject Payment"
        confirmVariant="danger"
        loading={processingPayment === selectedPaymentId}
        icon={
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
              <i className="ri-close-line text-red-600 dark:text-red-400 text-xl"></i>
            </div>
          </div>
        }
      />

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        document={selectedDocument}
        isOpen={isDocumentPreviewOpen}
        onClose={handleCloseDocumentPreview}
      />
    </div>
  );
};

export default PaymentTaskComponent;
