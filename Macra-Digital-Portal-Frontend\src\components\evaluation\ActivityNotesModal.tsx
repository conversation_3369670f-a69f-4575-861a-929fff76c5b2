'use client';

import React, { useState } from 'react';
import { documentService } from '@/services/documentService';
import ActivityHistory from '@/components/common/ActivityHistory';
import { activityNotesService } from '@/services/activityNotesService';
import { userService } from '@/services/userService';
import { useAuth } from '@/contexts/AuthContext';

interface ActivityNotesModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityId: string;
  entityType: string;
  applicationNumber?: string;
  initialEmails?: string;
  title?: string;
  isGeneralMessage?: boolean;
}

const ActivityNotesModal: React.FC<ActivityNotesModalProps> = ({
  isOpen,
  onClose,
  entityId,
  entityType,
  initialEmails = '',
  title,
  isGeneralMessage = false
}) => {
  const [message, setMessage] = useState('');
  const [emailsToNotify, setEmailsToNotify] = useState(initialEmails);
  const [emailBadges, setEmailBadges] = useState<string[]>([]);
  const [currentEmailInput, setCurrentEmailInput] = useState('');
  const [emailSuggestions, setEmailSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [sending, setSending] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const { user } = useAuth();

  // Initialize email badges from initial emails
  React.useEffect(() => {
    if (initialEmails) {
      const emails = initialEmails.split(',').map(email => email.trim()).filter(email => email);
      setEmailBadges(emails);
    }
  }, [initialEmails]);

  const handleEmailInputChange = async (value: string) => {
    setCurrentEmailInput(value);

    if (value.length > 1) {
      try {
        const userEmails = await userService.getUserEmails(value);
        const filtered = userEmails.filter(email =>
          email.toLowerCase().includes(value.toLowerCase()) &&
          !emailBadges.includes(email)
        );
        setEmailSuggestions(filtered);
        setShowSuggestions(filtered.length > 0);
      } catch (error) {
        console.warn('Failed to fetch email suggestions:', error);
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  };

  const addEmailBadge = (email: string) => {
    const trimmedEmail = email.trim();
    if (trimmedEmail && !emailBadges.includes(trimmedEmail)) {
      const newBadges = [...emailBadges, trimmedEmail];
      setEmailBadges(newBadges);
      setEmailsToNotify(newBadges.join(', '));
      setCurrentEmailInput('');
      setShowSuggestions(false);
    }
  };

  const removeEmailBadge = (emailToRemove: string) => {
    const newBadges = emailBadges.filter(email => email !== emailToRemove);
    setEmailBadges(newBadges);
    setEmailsToNotify(newBadges.join(', '));
  };

  const handleEmailInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (currentEmailInput.trim()) {
        addEmailBadge(currentEmailInput);
      }
    } else if (e.key === 'Backspace' && !currentEmailInput && emailBadges.length > 0) {
      removeEmailBadge(emailBadges[emailBadges.length - 1]);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setAttachments(prev => [...prev, ...newFiles]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if (!message.trim()) {
      setErrorMessage('Please enter a message before sending.');
      return;
    }

    // For general messaging, ensure we have at least one email recipient
    if (isGeneralMessage && emailBadges.length === 0) {
      setErrorMessage('Please add at least one email recipient for general messaging.');
      return;
    }

    setSending(true);
    setSuccessMessage('');
    setErrorMessage('');

    try {
      // Step 1: Create activity note for the message
      // Use emailBadges array for more accurate email list
      const additionalEmails = emailBadges.filter(email => email.trim().length > 0);

      const activityNote = await activityNotesService.create({
        entity_type: entityType,
        entity_id: entityId,
        note: message.trim(),
        note_type: 'evaluation_comment',
        category: 'communication',
        metadata: {
          is_email_message: true,
          attachments_count: attachments.length,
          timestamp: new Date().toISOString(),
          additional_emails: additionalEmails,
        },
        priority: 'normal',
        is_internal: false,
      });

      // Step 2: Upload attachments and link to the activity note
      const uploadedDocuments = [];
      if (attachments.length > 0) {
        for (const file of attachments) {
          try {
            const uploadResult = await documentService.uploadDocument(file, {
              document_type: 'COMMUNICATION',
              entity_type: 'activity_note',
              entity_id: activityNote.id,
              is_required: false
            });
            uploadedDocuments.push(uploadResult.document);
          } catch (error) {
            console.error('Failed to upload document:', error);
          }
        }
      }

      // Step 4: Clear form and trigger refresh
      setMessage('');
      setEmailsToNotify('');
      setAttachments([]);

      const successMsg = additionalEmails.length > 0
        ? `✅ Message sent successfully! Applicant and ${additionalEmails.length} additional email(s) have been notified.`
        : '✅ Message sent successfully and applicant has been notified via email!';
      setSuccessMessage(successMsg);

      // Trigger refresh of ActivityHistory component
      setRefreshTrigger(prev => prev + 1);

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 5000);

    } catch (error) {
      console.error('Failed to send message:', error);
      setErrorMessage('❌ Failed to send message. Please try again.');
      
      // Auto-hide error message after 5 seconds
      setTimeout(() => {
        setErrorMessage('');
      }, 5000);
    } finally {
      setSending(false);
    }
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <i className="ri-mail-line mr-2 text-blue-600"></i>
              Communication Center
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              User: {user?.email}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className='overflow-y-auto'>
            {/* Send Message Section */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <i className="ri-send-plane-line mr-2 text-green-600"></i>
                {title || (isGeneralMessage ? 'Send Message' : 'Send Message to Applicant')}
              </h3>

              {/* Success Message */}
              {successMessage && (
                <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <div className="text-green-600 dark:text-green-400 text-sm font-medium">
                    {successMessage}
                  </div>
                </div>
              )}

              {/* Error Message */}
              {errorMessage && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <div className="text-red-600 dark:text-red-400 text-sm font-medium">
                    {errorMessage}
                  </div>
                </div>
              )}

              {/* Emails to Notify Input */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Additional Emails to Notify (Optional)
                </label>

                {/* Email Badges */}
                {emailBadges.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                    {emailBadges.map((email, index) => (
                      <span
                        key={index}
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          isValidEmail(email)
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
                        }`}
                      >
                        {email}
                        <button
                          type="button"
                          onClick={() => removeEmailBadge(email)}
                          className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black/10 dark:hover:bg-white/10"
                        >
                          <i className="ri-close-line text-xs"></i>
                        </button>
                      </span>
                    ))}
                  </div>
                )}

                {/* Email Input with Suggestions */}
                <div className="relative">
                  <input
                    type="text"
                    value={currentEmailInput}
                    onChange={(e) => {
                      handleEmailInputChange(e.target.value);
                      if (successMessage) setSuccessMessage('');
                      if (errorMessage) setErrorMessage('');
                    }}
                    onKeyDown={handleEmailInputKeyDown}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Type email address and press Enter or comma to add"
                  />

                  {/* Email Suggestions Dropdown */}
                  {showSuggestions && emailSuggestions.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                      {emailSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => addEmailBadge(suggestion)}
                          className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-gray-100 dark:focus:bg-gray-700 focus:outline-none text-sm"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Type email addresses and press Enter or comma to add them. {isGeneralMessage ? 'All added recipients will be notified.' : 'The applicant will be notified automatically.'}
                </p>
              </div>

              {/* Message Input */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Message *
                </label>
                <textarea
                  value={message}
                  onChange={(e) => {
                    setMessage(e.target.value);
                    if (successMessage) setSuccessMessage('');
                    if (errorMessage) setErrorMessage('');
                  }}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Enter your message to the applicant..."
                  required
                />
              </div>

              {/* File Upload */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Attach Files (Optional)
                </label>
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="message-file-upload"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  />
                  <label
                    htmlFor="message-file-upload"
                    className="cursor-pointer flex flex-col items-center justify-center"
                  >
                    <i className="ri-upload-cloud-line text-2xl text-gray-400 mb-1"></i>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Click to upload files
                    </span>
                  </label>
                </div>

                {/* Uploaded Files List */}
                {attachments.length > 0 && (
                  <div className="mt-3">
                    <div className="space-y-2">
                      {attachments.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded">
                          <div className="flex items-center">
                            <i className="ri-file-line text-gray-400 mr-2"></i>
                            <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeAttachment(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <i className="ri-close-line"></i>
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Send Button */}
              <button
                onClick={handleSendMessage}
                disabled={!message.trim() || sending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {sending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <i className="ri-send-plane-line mr-2"></i>
                    Send Message
                  </>
                )}
              </button>
            </div>

            {/* Activity History Section */}
            <div className="flex-1 overflow-hidden">
              <ActivityHistory
                entityType={entityType}
                entityId={entityId}
                title="Activity History"
                showSearch={true}
                showFilters={false}
                maxHeight="max-h-96"
                refreshTrigger={refreshTrigger}
                className="border-0 rounded-none"
              />
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ActivityNotesModal;
