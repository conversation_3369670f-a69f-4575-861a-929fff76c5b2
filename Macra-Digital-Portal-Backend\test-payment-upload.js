const http = require('http');
const fs = require('fs');
const path = require('path');

// Create a simple test file for upload
const testFilePath = path.join(__dirname, 'test-payment-proof.txt');
fs.writeFileSync(testFilePath, 'This is a test payment proof document.');

function createMultipartData(invoiceId, file) {
  const boundary = '----formdata-boundary-' + Math.random().toString(36);
  const fileContent = fs.readFileSync(file);
  
  let data = '';
  data += `--${boundary}\r\n`;
  data += `Content-Disposition: form-data; name="invoice_id"\r\n\r\n`;
  data += `${invoiceId}\r\n`;
  data += `--${boundary}\r\n`;
  data += `Content-Disposition: form-data; name="payment_method"\r\n\r\n`;
  data += `bank_transfer\r\n`;
  data += `--${boundary}\r\n`;
  data += `Content-Disposition: form-data; name="transaction_reference"\r\n\r\n`;
  data += `TEST-TXN-${Date.now()}\r\n`;
  data += `--${boundary}\r\n`;
  data += `Content-Disposition: form-data; name="file"; filename="payment-proof.txt"\r\n`;
  data += `Content-Type: text/plain\r\n\r\n`;
  
  return {
    boundary,
    data: Buffer.concat([
      Buffer.from(data, 'utf8'),
      fileContent,
      Buffer.from(`\r\n--${boundary}--\r\n`, 'utf8')
    ])
  };
}

async function testPaymentUpload() {
  try {
    console.log('🧪 Testing payment upload and task creation...');
    
    // First, get an invoice ID from the database
    console.log('📋 Getting invoice ID...');
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'macra_db'
    });
    
    const [invoices] = await connection.execute(
      'SELECT invoice_id, invoice_number FROM invoices WHERE deleted_at IS NULL LIMIT 1'
    );
    
    if (invoices.length === 0) {
      console.log('❌ No invoices found in database');
      return;
    }
    
    const invoice = invoices[0];
    console.log(`✅ Found invoice: ${invoice.invoice_number} (${invoice.invoice_id})`);
    
    await connection.end();
    
    // Create multipart form data
    const { boundary, data } = createMultipartData(invoice.invoice_id, testFilePath);
    
    // Make the upload request
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/payments/customer/upload-proof-of-payment',
      method: 'POST',
      headers: {
        'Content-Type': `multipart/form-data; boundary=${boundary}`,
        'Content-Length': data.length,
        // Note: This would normally require authentication
        // 'Authorization': 'Bearer your-jwt-token'
      }
    };
    
    console.log('📤 Uploading payment proof...');
    
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', async () => {
        console.log(`📊 Response Status: ${res.statusCode}`);
        
        try {
          const parsed = JSON.parse(responseData);
          console.log('📋 Response:', JSON.stringify(parsed, null, 2));
          
          if (res.statusCode === 200 || res.statusCode === 201) {
            console.log('✅ Payment upload successful!');
            
            // Check if task was created
            console.log('🔍 Checking if task was created...');
            await checkTaskCreation(invoice.invoice_id);
          } else {
            console.log('❌ Payment upload failed');
          }
        } catch (e) {
          console.log('📝 Raw response:', responseData);
        }
        
        // Clean up test file
        fs.unlinkSync(testFilePath);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ Request error: ${err.message}`);
      fs.unlinkSync(testFilePath);
    });
    
    req.write(data);
    req.end();
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
    }
  }
}

async function checkTaskCreation(invoiceId) {
  try {
    const mysql = require('mysql2/promise');
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'macra_db'
    });
    
    const [tasks] = await connection.execute(`
      SELECT task_id, task_type, title, status, assigned_to, created_by, created_at
      FROM tasks 
      WHERE entity_type = 'invoice' AND entity_id = ? AND task_type = 'payment_verification'
      ORDER BY created_at DESC
    `, [invoiceId]);
    
    console.log(`📊 Payment verification tasks found: ${tasks.length}`);
    
    if (tasks.length > 0) {
      console.log('✅ Task created successfully!');
      tasks.forEach((task, index) => {
        console.log(`  ${index + 1}. ${task.title}`);
        console.log(`     Status: ${task.status}, Assigned: ${task.assigned_to || 'Unassigned'}`);
        console.log(`     Created: ${task.created_at}`);
      });
    } else {
      console.log('❌ No payment verification task found');
    }
    
    await connection.end();
  } catch (error) {
    console.error('❌ Error checking task creation:', error.message);
  }
}

testPaymentUpload();
