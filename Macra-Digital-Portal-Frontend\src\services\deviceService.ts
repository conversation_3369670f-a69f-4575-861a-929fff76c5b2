import { apiClient, processApiResponse } from '@/lib';
import { DeviceData, EquipmentDetailsFormData, Manufacturer } from '@/types';



class DeviceService {
  private baseUrl = '/devices';

  /**
   * Test backend connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 Testing backend connectivity...');
      const response = await apiClient.get('/health');
      console.log('✅ Backend health check:', processApiResponse(response));
      return true;
    } catch (error: any) {
      console.error('❌ Backend connectivity test failed:', error);
      return false;
    }
  }

  /**
   * Create a new device
   */
  async createDevice(deviceData: DeviceData): Promise<any> {
    try {
      console.log('🔍 Creating device at:', `${this.baseUrl}/devices`);
      console.log('📝 Device data:', deviceData);
      const response = await apiClient.post(`${this.baseUrl}/devices`, deviceData);
      console.log('✅ Device created:', processApiResponse(response));
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error creating device:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      });
      throw error;
    }
  }

  /**
   * Get device by ID
   */
  async getDevice(deviceId: string): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/devices/${deviceId}`);
    return processApiResponse(response);
  }

  /**
   * Get device by IMEI
   */
  async getDeviceByImei(imei: string): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/devices/imei/${imei}`);
    return processApiResponse(response);
  }

  /**
   * Update device
   */
  async updateDevice(deviceId: string, deviceData: Partial<DeviceData>): Promise<any> {
    const response = await apiClient.put(`${this.baseUrl}/devices/${deviceId}`, deviceData);
    return processApiResponse(response);
  }

  /**
   * Get all devices for an application
   */
  async getDevicesByApplication(applicationId: string): Promise<any[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/devices`);
      return processApiResponse(response).filter((device: any) => device.application_id === applicationId);
    } catch (error) {
      console.error('Error fetching devices for application:', error);
      return [];
    }
  }

  /**
   * Create a new manufacturer
   */
  async createManufacturer(manufacturer: Manufacturer): Promise<Manufacturer> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/manufacturers`, manufacturer);
      return processApiResponse(response);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get manufacturer by ID
   */
  async getManufacturer(manufacturerId: string): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/manufacturers/${manufacturerId}`);
    return processApiResponse(response);
  }

  /**
   * Update manufacturer
   */
  async updateManufacturer(manufacturerId: string, manufacturer: Partial<Manufacturer>): Promise<any> {
    const response = await apiClient.put(`${this.baseUrl}/manufacturers/${manufacturerId}`, manufacturer);
    return processApiResponse(response);
  }

  /**
   * Get all manufacturers
   */
  async getAllManufacturers(): Promise<any> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/manufacturers`);
      return processApiResponse(response);
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Find manufacturer by name
   */
  async findManufacturerByName(name: string): Promise<any | null> {
    try {
      console.log('🔍 Searching for manufacturer:', name);
      const response = await this.getAllManufacturers();
      const manufacturers: Manufacturer[] = response.data;
      console.log('📋 Available manufacturers:', manufacturers.map((m: any) => m.manufacturer_name));

      const found = manufacturers.find(m =>
        m.manufacturer_name.toLowerCase().trim() === name.toLowerCase().trim()
      ) || null;

      if (found) {
        console.log('✅ Found existing manufacturer:', found);
      } else {
        console.log('❌ Manufacturer not found, will need to create new one');
      }

      return found;
    } catch (error) {
      console.error('Error finding manufacturer by name:', error);
      return null;
    }
  }

  /**
   * Save equipment details for an application
   * This combines device and manufacturer data
   */
  async saveEquipmentDetails(applicationId: string, formData: EquipmentDetailsFormData): Promise<{
    device: any;
    manufacturer: Manufacturer;
  }> {
    try {
      // Validate required fields
      if (!formData.imei || !formData.manufacturer_name || !formData.model_number || !formData.product_type_name) {
        throw new Error('Missing required fields: IMEI, manufacturer name, model number, and product type are required');
      }

      // First, check if manufacturer exists or create new one
      let manufacturer = await this.findManufacturerByName(formData.manufacturer_name);
      
      if (!manufacturer) {
        // Create new manufacturer
        manufacturer = {
          manufacturer_name: formData.manufacturer_name,
          manufacturer_country_origin: formData.manufacturer_country || 'Unknown',
          manufacturer_email: formData.manufacturer_email,
          manufacturer_phone: formData.manufacturer_phone,
          manufacturer_website: formData.manufacturer_website || 'https://example.com',
        };

        console.log('Creating new manufacturer:', manufacturer);

        try {
          manufacturer = await this.createManufacturer(manufacturer);
        } catch (error: any) {
          // If manufacturer creation fails due to conflict, try to find it again
          if (error.response?.status === 409 && error.response?.data?.message?.includes('already exists')) {
            manufacturer = await this.findManufacturerByName(formData.manufacturer_name);
            if (!manufacturer) {
              // If we still can't find it, there might be a race condition or data issue
              throw new Error(`Manufacturer "${formData.manufacturer_name}" already exists but cannot be found. Please try again.`);
            }
          } else {
            throw error; // Re-throw other errors
          }
        }
      } else {
        // Update existing manufacturer with new information if provided
        const updateData: Partial<Manufacturer> = {};
        let needsUpdate = false;

        if (formData.manufacturer_email && formData.manufacturer_email !== manufacturer.manufacturer_email) {
          updateData.manufacturer_email = formData.manufacturer_email;
          needsUpdate = true;
        }

        if (formData.manufacturer_phone && formData.manufacturer_phone !== manufacturer.manufacturer_phone) {
          updateData.manufacturer_phone = formData.manufacturer_phone;
          needsUpdate = true;
        }

        if (formData.manufacturer_website && formData.manufacturer_website !== manufacturer.manufacturer_website) {
          updateData.manufacturer_website = formData.manufacturer_website;
          needsUpdate = true;
        }

        if (formData.manufacturer_country && formData.manufacturer_country !== manufacturer.manufacturer_country_origin) {
          updateData.manufacturer_country_origin = formData.manufacturer_country;
          needsUpdate = true;
        }

        if (needsUpdate) {
          console.log('📝 Updating existing manufacturer with new data:', updateData);
          try {
            manufacturer = await this.updateManufacturer(manufacturer.manufacturer_id, updateData);
            console.log('✅ Manufacturer updated:', manufacturer);
          } catch (updateError) {
            console.warn('⚠️ Failed to update manufacturer, continuing with existing data:', updateError);
            // Continue with existing manufacturer data if update fails
          }
        }
      }

      // Check if device already exists for this application
      const existingDevices = await this.getDevicesByApplication(applicationId);
      let device;

      if (existingDevices.length > 0) {
        // Update existing device
        const deviceData: Partial<DeviceData> = {
          imei: formData.imei,
          device_type: formData.product_type_name,
          model_name: formData.model_number,
          manufacturer_id: manufacturer.manufacturer_id,
        };
        console.log('📝 Updating existing device:', existingDevices[0].device_id, deviceData);

        try {
          device = await this.updateDevice(existingDevices[0].device_id, deviceData);
          console.log('✅ Device updated:', device);
        } catch (updateError: any) {
          console.error('❌ Failed to update device:', updateError);

          // If update fails due to IMEI conflict, check if another device has this IMEI
          if (updateError.response?.status === 409) {
            try {
              const conflictingDevice = await this.getDeviceByImei(formData.imei);
              if (conflictingDevice && conflictingDevice.device_id !== existingDevices[0].device_id) {
                throw new Error(`IMEI ${formData.imei} is already registered to another device. Please use a different IMEI.`);
              }
            } catch (imeiError) {
              // If we can't find the conflicting device, re-throw original error
              throw updateError;
            }
          }
          throw updateError;
        }
      } else {
        // Create new device
        const deviceData: DeviceData = {
          application_id: applicationId,
          manufacturer_id: manufacturer.manufacturer_id,
          imei: formData.imei,
          device_type: formData.product_type_name,
          model_name: formData.model_number,
          device_serial_number: `${formData.imei}-${Date.now()}`, // Generate unique serial
        };
        console.log('📝 Creating new device:', deviceData);

        try {
          device = await this.createDevice(deviceData);
          console.log('✅ Device created:', device);
        } catch (createError: any) {
          console.error('❌ Failed to create device:', createError);

          // Handle IMEI conflicts
          if (createError.response?.status === 409) {
            if (createError.response?.data?.message?.includes('imei') ||
                createError.response?.data?.message?.includes('IMEI')) {
              throw new Error(`IMEI ${formData.imei} is already registered. Please use a different IMEI or check if this device is already in the system.`);
            }
          }
          throw createError;
        }
      }

      return { device, manufacturer };
    } catch (error) {
      console.error('Error saving equipment details:', error);
      throw error;
    }
  }

  /**
   * Load equipment details for an application
   */
  async loadEquipmentDetails(applicationId: string): Promise<EquipmentDetailsFormData | null> {
    try {
      console.log('🔍 Loading equipment details for application:', applicationId);

      const devices = await this.getDevicesByApplication(applicationId);
      console.log('📱 Found devices:', devices.length);

      if (devices.length === 0) {
        console.log('❌ No devices found for application');
        return null;
      }

      const device = devices[0]; // Use the first device for now
      console.log('📱 Using device:', device);

      if (!device.manufacturer_id) {
        console.warn('⚠️ Device has no manufacturer_id, returning partial data');
        return {
          imei: device.imei || '',
          brand_trade_name: '',
          model_number: device.model_name || '',
          product_type_name: device.device_type || '',
          manufacturer_name: '',
          manufacturer_address: '',
          manufacturer_country: '',
          manufacturer_contact_person: '',
          manufacturer_email: '',
          manufacturer_phone: '',
          manufacturer_website: '',
        };
      }

      const manufacturer = await this.getManufacturer(device.manufacturer_id);
      console.log('🏭 Loaded manufacturer:', manufacturer);

      const equipmentData = {
        imei: device.imei || '',
        brand_trade_name: manufacturer.manufacturer_name || '',
        model_number: device.model_name || '',
        product_type_name: device.device_type || '',
        manufacturer_name: manufacturer.manufacturer_name || '',
        manufacturer_address: '', // Not stored in current schema
        manufacturer_country: manufacturer.manufacturer_country_origin || '',
        manufacturer_contact_person: '', // Not stored in current schema
        manufacturer_email: manufacturer.manufacturer_email || '',
        manufacturer_phone: manufacturer.manufacturer_phone || '',
        manufacturer_website: manufacturer.manufacturer_website || '',
      };

      console.log('✅ Equipment details loaded successfully:', equipmentData);
      return equipmentData;
    } catch (error) {
      console.error('❌ Error loading equipment details:', error);
      return null;
    }
  }
}

export const deviceService = new DeviceService();
