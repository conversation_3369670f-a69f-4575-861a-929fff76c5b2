'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { AlertCircle, CheckCircle } from 'lucide-react';
import LicenseVerificationForm from '@/components/public/LicenseVerificationForm';
import LicenseVerificationResults from '@/components/public/LicenseVerificationResults';
import PublicLoader from '@/components/public/PublicLoader';
import ErrorState from '@/components/public/ErrorState';
import { VerificationResultSkeleton } from '@/components/public/SkeletonLoader';
import PageTransition, { StaggeredAnimation } from '@/components/public/PageTransition';
import { publicApi } from '@/lib/customer-api';
import {
  VerificationFormData,
  PublicLicenseInfo,
  LicenseVerificationResponse,
  VerificationErrorType
} from '@/types/public';

export default function VerifyLicenseClient() {
  const searchParams = useSearchParams();

  const [loading, setLoading] = useState(false);
  const [verificationResult, setVerificationResult] = useState<PublicLicenseInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [progress, setProgress] = useState(0);

  // Get initial values from URL parameters
  const initialLicenseNumber = searchParams.get('license') || '';
  const initialVerificationCode = searchParams.get('code') || '';

  // Handle form submission with progress tracking
  const handleVerification = async (formData: VerificationFormData) => {
    setLoading(true);
    setError(null);
    setSuccessMessage(null);
    setVerificationResult(null);
    setProgress(0);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 20;
      });
    }, 200);

    try {
      setProgress(30);
      const response: LicenseVerificationResponse = await publicApi.verifyLicense(
        formData.licenseNumber,
        formData.verificationCode
      );

      setProgress(80);

      if (response.success && response.data) {
        setProgress(100);
        setVerificationResult(response.data);
        setSuccessMessage(response.message);
        setRetryCount(0);

        // Update URL with license number (without page reload)
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.set('license', formData.licenseNumber);
        if (formData.verificationCode) {
          newUrl.searchParams.set('code', formData.verificationCode);
        } else {
          newUrl.searchParams.delete('code');
        }
        window.history.replaceState({}, '', newUrl.toString());
      } else {
        setError(response.message || 'Verification failed');
        setRetryCount(prev => prev + 1);
      }
    } catch (err: unknown) {
      console.error('Verification error:', err);
      setRetryCount(prev => prev + 1);

      // Handle different types of errors
      if (err && typeof err === 'object' && 'response' in err) {
        const errorResponse = err as { response?: { data?: { error?: string; message?: string } } };
        if (errorResponse.response?.data) {
          const errorData = errorResponse.response.data;

          switch (errorData.error) {
            case VerificationErrorType.INVALID_FORMAT:
              setError('Invalid license number format. Please use the format LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001)');
              break;
            case VerificationErrorType.INVALID_VERIFICATION_CODE:
              setError('Invalid verification code format. The code should be 12 characters long.');
              break;
            case VerificationErrorType.LICENSE_NOT_FOUND:
              setError('License not found. Please check the license number and try again.');
              break;
            case VerificationErrorType.VERIFICATION_ERROR:
              setError('System error occurred during verification. Please try again later.');
              break;
            default:
              setError(errorData.message || 'An error occurred during verification');
          }
        } else {
          setError('An unexpected error occurred. Please try again later.');
        }
      } else if (err && typeof err === 'object' && 'code' in err && err.code === 'NETWORK_ERROR') {
        setError('Network error. Please check your internet connection and try again.');
      } else {
        setError('An unexpected error occurred. Please try again later.');
      }
    } finally {
      clearInterval(progressInterval);
      setLoading(false);
      setProgress(0);
    }
  };

  // Handle "Verify Another" action
  const handleVerifyAnother = () => {
    setVerificationResult(null);
    setError(null);
    setSuccessMessage(null);
    
    // Clear URL parameters
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete('license');
    newUrl.searchParams.delete('code');
    window.history.replaceState({}, '', newUrl.toString());
  };

  // Auto-verify if license number is provided in URL
  useEffect(() => {
    if (initialLicenseNumber && !verificationResult && !loading) {
      // Validate license number format before auto-verification
      const licensePattern = /^LIC-\d{4}-\d{2}-\d{3}$/;
      if (licensePattern.test(initialLicenseNumber)) {
        handleVerification({
          licenseNumber: initialLicenseNumber,
          verificationCode: initialVerificationCode || undefined
        });
      }
    }
  }, [initialLicenseNumber, initialVerificationCode, verificationResult, loading]);

  // Show loading state during verification
  if (loading) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-center min-h-96">
          <PublicLoader
            type="verification"
            message="Verifying license..."
            submessage="Please wait while we check the MACRA database"
            showProgress={true}
            progress={progress}
            size="lg"
            dynamicMessages={[
              'Connecting to MACRA database...',
              'Validating license number...',
              'Checking license status...',
              'Retrieving license details...',
              'Finalizing verification...'
            ]}
          />
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Page Header */}
        <StaggeredAnimation delay={100}>
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              License Verification
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Verify the authenticity of licenses issued by MACRA
            </p>
          </div>
        </StaggeredAnimation>

        {/* Success Message */}
        {successMessage && !error && (
          <StaggeredAnimation delay={200}>
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 animate-slideInFromTop">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 animate-bounceIn" />
                <p className="text-green-800 dark:text-green-200 font-medium">{successMessage}</p>
              </div>
            </div>
          </StaggeredAnimation>
        )}

        {/* Error Message */}
        {error && (
          <StaggeredAnimation delay={200}>
            <ErrorState
              type="validation"
              title="Verification Failed"
              message={error}
              onRetry={() => handleVerification({
                licenseNumber: initialLicenseNumber,
                verificationCode: initialVerificationCode
              })}
              retryCount={retryCount}
              maxRetries={3}
              showHomeButton={false}
              className="animate-shake"
            />
          </StaggeredAnimation>
        )}

        {/* Main Content */}
        {verificationResult ? (
          /* Show Results */
          <StaggeredAnimation delay={300}>
            <div className="animate-fadeIn">
              <LicenseVerificationResults
                licenseInfo={verificationResult}
                onVerifyAnother={handleVerifyAnother}
              />
            </div>
          </StaggeredAnimation>
        ) : (
          /* Show Form */
          <StaggeredAnimation delay={300}>
            <div className="space-y-6">
              <LicenseVerificationForm
                onSubmit={handleVerification}
                loading={loading}
                initialLicenseNumber={initialLicenseNumber}
                initialVerificationCode={initialVerificationCode}
              />

              {/* Additional Information */}
              <div className="bg-primary/5 dark:bg-primary/10 rounded-xl p-6 border border-primary/20 hover:shadow-md transition-shadow duration-200">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                  About License Verification
                </h3>
                <div className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
                  <p>
                    • This service allows you to verify the authenticity of licenses issued by MACRA
                  </p>
                  <p>
                    • Enter the license number in the format LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001)
                  </p>
                  <p>
                    • Verification codes are optional and provide enhanced security when available
                  </p>
                  <p>
                    • All verification requests are logged for security and audit purposes
                  </p>
                </div>
              </div>
            </div>
          </StaggeredAnimation>
        )}

        {/* Help Section */}
        <StaggeredAnimation delay={400}>
          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
              Need Help?
            </h3>
            <div className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <p>
                If you&apos;re having trouble verifying a license or need assistance:
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Check that the license number is entered correctly</li>
                <li>Ensure the license number follows the format LIC-YYYY-MM-NNN</li>
                <li>Contact MACRA directly at +265 1 770 100 for support</li>
                <li>Visit our <a href="/public/help" className="text-primary hover:text-red-700 transition-colors">help page</a> for more information</li>
              </ul>
            </div>
          </div>
        </StaggeredAnimation>
      </div>
    </PageTransition>
  );
}
