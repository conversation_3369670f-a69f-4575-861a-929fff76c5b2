import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { PaginatedResponse, PaginateQuery } from '@/types';

// Types following backend entity structure
export interface ResourceApplication {
  application_id: string;
  application_number: string;
  applicant_id: string;
  title: string;
  description: string;
  resource_type: ResourceType;
  status: ResourceStatus;
  priority: ResourcePriority;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;

  // Related data
  applicant?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
    company_name?: string;
  };

  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

// Enums
export enum ResourceType {
  EQUIPMENT = 'Equipment',
  FACILITY = 'Facility',
  SPECTRUM = 'Spectrum',
  TECHNICAL_SUPPORT = 'Technical Support',
  CONSULTATION = 'Consultation',
  OTHER = 'Other',
}

export enum ResourceStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum ResourcePriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface CreateResourceApplicationData {
  title: string;
  description: string;
  resource_type: ResourceType;
  priority?: ResourcePriority;
  attachments?: File[];
}

export interface UpdateResourceApplicationData {
  title?: string;
  description?: string;
  resource_type?: ResourceType;
  status?: ResourceStatus;
  priority?: ResourcePriority;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
}

// Use centralized pagination types from @/types

export type ResourceApplicationsResponse = PaginatedResponse<ResourceApplication>;

export const resourceService = {

  // Create new resource application
  async createApplication(data: CreateResourceApplicationData): Promise<ResourceApplication> {
    try {
      console.log('🔄 Creating resource application:', {
        title: data.title,
        resource_type: data.resource_type,
        priority: data.priority
      });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('resource_type', data.resource_type);
      
      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Handle file attachments
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append(`attachments`, file);
        });
      }

      const response = await apiClient.post('/resource-applications', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Resource application created successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error creating resource application:', error);
      throw error;
    }
  },

  // Get all resource applications with pagination
  async getApplications(query: PaginateQuery = {}): Promise<ResourceApplicationsResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) params.set('sortBy', query.sortBy);
    if (query.sortOrder) params.set('sortOrder', query.sortOrder);

    // Handle filters
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/resource-applications?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get resource application by ID
  async getApplication(id: string): Promise<ResourceApplication> {
    const response = await apiClient.get(`/resource-applications/${id}`);
    return processApiResponse(response);
  },

  // Get resource application by ID (alias for consistency)
  async getApplicationById(id: string): Promise<ResourceApplication> {
    return this.getApplication(id);
  },

  // Update resource application
  async updateApplication(id: string, data: UpdateResourceApplicationData): Promise<ResourceApplication> {
    const response = await apiClient.put(`/resource-applications/${id}`, data);
    return processApiResponse(response);
  },

  // Update resource application status
  async updateStatus(id: string, status: ResourceStatus): Promise<ResourceApplication> {
    const response = await apiClient.patch(`/resource-applications/${id}/status`, { status });
    return processApiResponse(response);
  },

  // Assign resource application to officer
  async assignApplication(id: string, assignedTo: string): Promise<ResourceApplication> {
    const response = await apiClient.patch(`/resource-applications/${id}/assign`, { 
      assigned_to: assignedTo 
    });
    return processApiResponse(response);
  },

  // Delete resource application
  async deleteApplication(id: string): Promise<void> {
    const response = await apiClient.delete(`/resource-applications/${id}`);
    return processApiResponse(response);
  },

  // Get resource application statistics
  async getStats(): Promise<Record<string, number>> {
    const response = await apiClient.get('/resource-applications/stats');
    return processApiResponse(response);
  },

  // Get resource type options
  getResourceTypeOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'Equipment', label: 'Equipment' },
      { value: 'Facility', label: 'Facility' },
      { value: 'Spectrum', label: 'Spectrum' },
      { value: 'Technical Support', label: 'Technical Support' },
      { value: 'Consultation', label: 'Consultation' },
      { value: 'Other', label: 'Other' }
    ];
  },

  // Get status options
  getStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'approved', label: 'Approved' },
      { value: 'rejected', label: 'Rejected' },
      { value: 'in_progress', label: 'In Progress' },
      { value: 'completed', label: 'Completed' },
      { value: 'cancelled', label: 'Cancelled' }
    ];
  },

  // Get priority options
  getPriorityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ];
  },
};
