import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '../lib/authUtils';

// Types following backend entity structure
export interface ProfessionalServicesData {
  professional_services_id?: string;
  application_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface CreateProfessionalServicesData {
  application_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
}

export interface UpdateProfessionalServicesData {
  professional_services_id: string;
  consultants: string;
  service_providers: string;
  technical_support: string;
  maintenance_arrangements: string;
  professional_partnerships?: string;
  outsourced_services?: string;
  quality_assurance?: string;
  training_programs?: string;
}

class ProfessionalServicesService {
  private baseUrl = '/professional-services';

  // Create professional services record
  async createProfessionalServices(data: CreateProfessionalServicesData): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Creating professional services record:', data);
      const response = await apiClient.post(this.baseUrl, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error creating professional services:', error);
      throw error;
    }
  }

  // Update professional services record
  async updateProfessionalServices(data: UpdateProfessionalServicesData): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Updating professional services record:', data.professional_services_id);
      const response = await apiClient.put(`${this.baseUrl}/${data.professional_services_id}`, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error updating professional services:', error);
      throw error;
    }
  }

  // Get professional services by application ID
  async getProfessionalServicesByApplication(applicationId: string): Promise<ProfessionalServicesData | null> {
    try {
      console.log('🔧 Getting professional services for application:', applicationId);

      // Validate applicationId format
      if (!applicationId || typeof applicationId !== 'string') {
        throw new Error(`Invalid application ID: ${applicationId}`);
      }

      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      // Enhanced error logging with more details
      console.error('❌ Professional services API error:', {
        applicationId,
        url: `${this.baseUrl}/application/${applicationId}`,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        code: error.code,
        name: error.name,
        stack: error.stack,
        config: {
          baseURL: error.config?.baseURL,
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      });

      if (error.response?.status === 404) {
        console.log('📝 No professional services found for application:', applicationId);
        return null;
      }

      // Don't throw the error for now, return null to prevent app crashes
      // This allows the application to continue working even if this service fails
      console.warn('⚠️ Returning null due to professional services error to prevent app crash');
      return null;
    }
  }

  // Get professional services by ID
  async getProfessionalServices(professionalServicesId: string): Promise<ProfessionalServicesData> {
    try {
      console.log('🔧 Getting professional services:', professionalServicesId);
      const response = await apiClient.get(`${this.baseUrl}/${professionalServicesId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting professional services:', error);
      throw error;
    }
  }

  // Delete professional services record
  async deleteProfessionalServices(professionalServicesId: string): Promise<void> {
    try {
      console.log('🔧 Deleting professional services:', professionalServicesId);
      const response = await apiClient.delete(`${this.baseUrl}/${professionalServicesId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error deleting professional services:', error);
      throw error;
    }
  }

  // Get all professional services (admin only)
  async getAllProfessionalServices(): Promise<ProfessionalServicesData[]> {
    try {
      console.log('🔧 Getting all professional services');
      const response = await apiClient.get(this.baseUrl);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting all professional services:', error);
      throw error;
    }
  }

  // Create or update professional services for an application
  async createOrUpdateProfessionalServices(applicationId: string, data: Omit<CreateProfessionalServicesData, 'application_id'>): Promise<ProfessionalServicesData> {
    try {
      // Use the backend's combined create/update endpoint
      const response = await apiClient.post(`${this.baseUrl}/application/${applicationId}`, data);
      return processApiResponse(response);
    } catch (error) {
      console.error('ProfessionalServicesService.createOrUpdateProfessionalServices error:', error);
      throw error;
    }
  }
}

export const professionalServicesService = new ProfessionalServicesService();
