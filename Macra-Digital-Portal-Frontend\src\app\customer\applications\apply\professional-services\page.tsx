'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextArea } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { professionalServicesService } from '@/services/professionalServicesService';

const ProfessionalServicesPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious
  } = useDynamicNavigation({
    currentStepRoute: 'professional-services',
    licenseCategoryId,
    applicationId
  });



  // Form data state
  const [formData, setFormData] = useState({
    consultants: '',
    service_providers: '',
    technical_support: '',
    maintenance_arrangements: '',
    professional_partnerships: '',
    outsourced_services: '',
    quality_assurance: '',
    training_programs: ''
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load existing professional services data
        try {
          const existingData = await professionalServicesService.getProfessionalServicesByApplication(applicationId);
          if (existingData) {
            setFormData({
              consultants: existingData.consultants || '',
              service_providers: existingData.service_providers || '',
              technical_support: existingData.technical_support || '',
              maintenance_arrangements: existingData.maintenance_arrangements || '',
              professional_partnerships: existingData.professional_partnerships || '',
              outsourced_services: existingData.outsourced_services || '',
              quality_assurance: existingData.quality_assurance || '',
              training_programs: existingData.training_programs || ''
            });
            console.log('Professional services data auto-populated');
          } else {
            console.log('No existing professional services data found');
          }
        } catch (professionalServicesError: any) {
          console.error('Error loading professional services data:', professionalServicesError);
          // Silently handle error - form will start empty
        }

      } catch (error: any) {
        console.error('Error loading professional services form:', error);
        setError('Failed to load professional services form. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save function - following other apply pages pattern
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (!formData.consultants.trim()) errors.consultants = 'Consultants information is required';
      if (!formData.service_providers.trim()) errors.service_providers = 'Service providers information is required';
      if (!formData.technical_support.trim()) errors.technical_support = 'Technical support information is required';
      if (!formData.maintenance_arrangements.trim()) errors.maintenance_arrangements = 'Maintenance arrangements information is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      // Create or update professional services record using the proper API
      const professionalServicesData = {
        consultants: formData.consultants,
        service_providers: formData.service_providers,
        technical_support: formData.technical_support,
        maintenance_arrangements: formData.maintenance_arrangements,
        professional_partnerships: formData.professional_partnerships,
        outsourced_services: formData.outsourced_services,
        quality_assurance: formData.quality_assurance,
        training_programs: formData.training_programs
      };

      // Save professional services data
      try {
        await professionalServicesService.createOrUpdateProfessionalServices(applicationId, professionalServicesData);
      } catch (saveError: any) {
        console.error('Error saving professional services data:', saveError);
        throw new Error('Failed to save professional services information');
      }

      setValidationErrors({});
      setSuccessMessage('Professional services information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('Professional services information saved successfully');
      return true;

    } catch (error: any) {
      console.error('Error saving professional services information:', error);
      
      // Extract specific error message from API response
      let errorMessage = 'Failed to save professional services information. Please try again.';
      
      if (error.response?.data?.message) {
        // Use the specific error message from the backend
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // Fallback to error message
        errorMessage = error.message;
      }
      
      setValidationErrors({ save: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading professional services form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-refresh-line mr-2"></i>
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText="Continue to Next Step"
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Changes"
        nextButtonDisabled={false}
        previousButtonDisabled={false}
        saveButtonDisabled={isSaving}
      >
        <div className="max-w-4xl mx-auto">
          <FormMessages
            successMessage={successMessage}
            errorMessage={validationErrors.save}
          />

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <div className="bg-primary/10 p-3 rounded-lg mr-4">
              <i className="ri-service-line text-2xl text-primary"></i>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Professional Services Information
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Step 6 of your application process
              </p>
            </div>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <p className="text-blue-800 dark:text-blue-200 text-sm">
              <i className="ri-lightbulb-line mr-2"></i>
              Provide comprehensive details about your professional services, consultants, and technical support arrangements.
              Fields marked with <span className="text-red-500">*</span> are required for your application.
            </p>
          </div>
        </div>

          {/* Progress Indicator */}
          <div className="mb-6">
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
              <span>Form Completion</span>
              <span>4 required fields</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${Object.values(formData).filter((value, index) =>
                    index < 4 && value.trim() !== ''
                  ).length / 4 * 100}%`
                }}
              ></div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            {/* Required Fields Section */}
            <div className="p-8 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
                <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-lg mr-3">
                  <i className="ri-star-line text-red-500"></i>
                </div>
                Required Information
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Consultants */}
                <div className="space-y-1">
                  <TextArea
                    label="Consultants"
                    name="consultants"
                    value={formData.consultants}
                    onChange={(e) => handleFormChange('consultants', e.target.value)}
                    placeholder="e.g., Legal advisors, business consultants, industry experts..."
                    rows={5}
                    error={validationErrors.consultants}
                    required
                    helperText="List all consultants and advisory services your organization utilizes"
                  />
                </div>

                {/* Service Providers */}
                <div className="space-y-1">
                  <TextArea
                    label="Service Providers"
                    name="service_providers"
                    value={formData.service_providers}
                    onChange={(e) => handleFormChange('service_providers', e.target.value)}
                    placeholder="e.g., IT services, cleaning services, security providers..."
                    rows={5}
                    error={validationErrors.service_providers}
                    required
                    helperText="Detail your key service providers and their specific roles"
                  />
                </div>

                {/* Technical Support */}
                <div className="space-y-1">
                  <TextArea
                    label="Technical Support"
                    name="technical_support"
                    value={formData.technical_support}
                    onChange={(e) => handleFormChange('technical_support', e.target.value)}
                    placeholder="e.g., 24/7 helpdesk, on-site support, remote assistance..."
                    rows={5}
                    error={validationErrors.technical_support}
                    required
                    helperText="Describe your technical support arrangements and capabilities"
                  />
                </div>

                {/* Maintenance Arrangements */}
                <div className="space-y-1">
                  <TextArea
                    label="Maintenance Arrangements"
                    name="maintenance_arrangements"
                    value={formData.maintenance_arrangements}
                    onChange={(e) => handleFormChange('maintenance_arrangements', e.target.value)}
                    placeholder="e.g., Scheduled maintenance, emergency repairs, service contracts..."
                    rows={5}
                    error={validationErrors.maintenance_arrangements}
                    required
                    helperText="Detail your maintenance and support arrangements"
                  />
                </div>
              </div>
            </div>

            {/* Optional Fields Section */}
            <div className="p-8 bg-gray-50 dark:bg-gray-900/50">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
                <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg mr-3">
                  <i className="ri-information-line text-blue-500"></i>
                </div>
                Additional Information
                <span className="ml-3 text-sm font-normal text-gray-500 dark:text-gray-400">(Optional)</span>
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                {/* Professional Partnerships */}
                <div className="space-y-1">
                  <TextArea
                    label="Professional Partnerships"
                    name="professional_partnerships"
                    value={formData.professional_partnerships}
                    onChange={(e) => handleFormChange('professional_partnerships', e.target.value)}
                    placeholder="e.g., Strategic alliances, joint ventures, collaboration agreements..."
                    rows={5}
                    error={validationErrors.professional_partnerships}
                    helperText="Detail any strategic partnerships or professional collaborations"
                  />
                </div>

                {/* Outsourced Services */}
                <div className="space-y-1">
                  <TextArea
                    label="Outsourced Services"
                    name="outsourced_services"
                    value={formData.outsourced_services}
                    onChange={(e) => handleFormChange('outsourced_services', e.target.value)}
                    placeholder="e.g., Payroll processing, accounting services, specialized functions..."
                    rows={5}
                    error={validationErrors.outsourced_services}
                    helperText="Include third-party service arrangements and contracts"
                  />
                </div>

                {/* Quality Assurance */}
                <div className="space-y-1">
                  <TextArea
                    label="Quality Assurance"
                    name="quality_assurance"
                    value={formData.quality_assurance}
                    onChange={(e) => handleFormChange('quality_assurance', e.target.value)}
                    placeholder="e.g., ISO certifications, quality control procedures, audit processes..."
                    rows={5}
                    error={validationErrors.quality_assurance}
                    helperText="Detail your quality assurance processes and standards"
                  />
                </div>

                {/* Training Programs */}
                <div className="space-y-1">
                  <TextArea
                    label="Training Programs"
                    name="training_programs"
                    value={formData.training_programs}
                    onChange={(e) => handleFormChange('training_programs', e.target.value)}
                    placeholder="e.g., Professional development courses, skills training, certification programs..."
                    rows={5}
                    error={validationErrors.training_programs}
                    helperText="Include staff development and training initiatives"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ProfessionalServicesPage;
