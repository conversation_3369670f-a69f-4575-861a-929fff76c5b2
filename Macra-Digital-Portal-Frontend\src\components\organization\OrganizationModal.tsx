'use client';

import { useState, useEffect } from 'react';
import { Organization, CreateOrganizationDto, UpdateOrganizationDto, organizationService } from '../../services/organizationService';

interface OrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (organization: Organization) => void;
  organization?: Organization | null;
}

const OrganizationModal = ({ isOpen, onClose, onSave, organization }: OrganizationModalProps) => {
  const initialFormData: CreateOrganizationDto = {
    name: '',
    registration_number: '',
    tpin: '',
    website: '',
    email: '',
    phone: '',
    fax: '',
    postal_address: '',
    physical_address: '',
    date_incorporation: '',
    place_incorporation: '',
    profile_description: '',
  };

  const [formData, setFormData] = useState<CreateOrganizationDto>(initialFormData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const isEditing = !!organization;

  useEffect(() => {
    if (isOpen) {
      if (organization) {
        // Editing existing organization
        setFormData({
          name: organization.name || '',
          registration_number: organization.registration_number || '',
          tpin: organization.tpin || '',
          website: organization.website || '',
          email: organization.email || '',
          phone: organization.phone || '',
          fax: organization.fax || '',
          postal_address: organization.postal_address || '',
          physical_address: organization.physical_address || '',
          date_incorporation: organization.date_incorporation ? organization.date_incorporation.split('T')[0] : '', // Convert to YYYY-MM-DD format
          place_incorporation: organization.place_incorporation || '',
          profile_description: organization.profile_description || '',
        });
      } else {
        // Creating new organization
        setFormData(initialFormData);
      }
      setErrors({});
    }
  }, [isOpen, organization]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Ensure formData is properly initialized
    if (!formData) {
      newErrors.general = 'Form data is not properly initialized';
      setErrors(newErrors);
      return false;
    }

    // Name validation
    if (!formData.name || !formData.name.trim()) {
      newErrors.name = 'Organization name is required';
    } else if (formData.name.length > 200) {
      newErrors.name = 'Organization name must be 200 characters or less';
    }

    // Registration number validation
    if (!formData.registration_number || !formData.registration_number.trim()) {
      newErrors.registration_number = 'Registration number is required';
    } else if (!organizationService.validateRegistrationNumber(formData.registration_number)) {
      newErrors.registration_number = 'Registration number must be at least 3 characters and contain only letters, numbers, and hyphens';
    }

    // TPIN validation
    if (!formData.tpin || !formData.tpin.trim()) {
      newErrors.tpin = 'TPIN is required';
    } else if (!organizationService.validateTpin(formData.tpin)) {
      newErrors.tpin = 'TPIN must be at least 8 characters and contain only letters, numbers, and hyphens';
    }

    // Website validation
    if (!formData.website || !formData.website.trim()) {
      newErrors.website = 'Website is required';
    } else if (!organizationService.validateWebsite(organizationService.formatWebsite(formData.website))) {
      newErrors.website = 'Please enter a valid website URL';
    }

    // Email validation
    if (!formData.email || !formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!organizationService.validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (!formData.phone || !formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!organizationService.validatePhone(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number (5-20 characters)';
    }

    // Fax validation (optional)
    if (formData.fax && formData.fax.trim() && !organizationService.validatePhone(formData.fax)) {
      newErrors.fax = 'Please enter a valid fax number';
    }

    // Postal address validation
    if (!formData.postal_address || !formData.postal_address.trim()) {
      newErrors.postal_address = 'Postal address is required';
    }

    // Physical address validation
    if (!formData.physical_address || !formData.physical_address.trim()) {
      newErrors.physical_address = 'Physical address is required';
    }

    // Date of incorporation validation
    if (!formData.date_incorporation || !formData.date_incorporation.trim()) {
      newErrors.date_incorporation = 'Date of incorporation is required';
    } else if (!organizationService.validateIncorporationDate(formData.date_incorporation)) {
      newErrors.date_incorporation = 'Please enter a valid date of incorporation (not in the future)';
    }

    // Place of incorporation validation
    if (!formData.place_incorporation || !formData.place_incorporation.trim()) {
      newErrors.place_incorporation = 'Place of incorporation is required';
    }

    // Profile description validation
    if (!formData.profile_description || !formData.profile_description.trim()) {
      newErrors.profile_description = 'Profile description is required';
    } else if (formData.profile_description.length > 1000) {
      newErrors.profile_description = 'Profile description must be 1000 characters or less';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      let savedOrganization: Organization;

      if (isEditing && organization) {
        // Update existing organization
        const updateData: UpdateOrganizationDto = {
          name: formData.name,
          registration_number: organizationService.formatRegistrationNumber(formData.registration_number || ''),
          tpin: organizationService.formatTpin(formData.tpin || ''),
          website: organizationService.formatWebsite(formData.website || ''),
          email: formData.email,
          phone: formData.phone,
          fax: formData.fax || undefined,
          postal_address: formData.postal_address,
          physical_address: formData.physical_address,
          date_incorporation: formData.date_incorporation,
          place_incorporation: formData.place_incorporation,
          profile_description: formData.profile_description,
        };
        savedOrganization = await organizationService.updateOrganization(organization.organization_id, updateData);
      } else {
        // Create new organization
        const createData: CreateOrganizationDto = {
          ...formData,
          registration_number: organizationService.formatRegistrationNumber(formData.registration_number || ''),
          tpin: organizationService.formatTpin(formData.tpin || ''),
          website: organizationService.formatWebsite(formData.website || ''),
          fax: formData.fax || undefined,
        };
        savedOrganization = await organizationService.createOrganization(createData);
      }

      onSave(savedOrganization);
      onClose();
    } catch (error: any) {
      console.error('Error saving organization:', error);

      // Handle validation errors from server
      if (error.response?.data?.message) {
        if (error.response.data.message.includes('registration_number')) {
          setErrors({ registration_number: 'Registration number already exists' });
        } else if (error.response.data.message.includes('tpin')) {
          setErrors({ tpin: 'TPIN already exists' });
        } else if (error.response.data.message.includes('name')) {
          setErrors({ name: 'Organization name already exists' });
        } else if (error.response.data.message.includes('email')) {
          setErrors({ email: 'Email already exists' });
        } else {
          setErrors({ general: error.response.data.message });
        }
      } else {
        setErrors({ general: 'Failed to save organization. Please try again.' });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateOrganizationDto, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field as string]) {
      setErrors(prev => ({ ...prev, [field as string]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            {/* Header */}
            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                  {isEditing ? 'Edit Organization' : 'Add New Organization'}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>

              {/* General error */}
              {errors.general && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
                </div>
              )}

              {/* Form fields */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {/* Organization Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Organization Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.name
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="e.g., ABC Company Limited"
                    maxLength={200}
                    disabled={isLoading}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                  )}
                </div>

                {/* Registration Number */}
                <div>
                  <label htmlFor="registration_number" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Registration Number *
                  </label>
                  <input
                    type="text"
                    id="registration_number"
                    value={formData.registration_number}
                    onChange={(e) => handleInputChange('registration_number', e.target.value.toUpperCase())}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ${
                      errors.registration_number
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="e.g., BN123456"
                    disabled={isLoading}
                  />
                  {errors.registration_number && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.registration_number}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Business registration number from registrar
                  </p>
                </div>

                {/* TPIN */}
                <div>
                  <label htmlFor="tpin" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    TPIN *
                  </label>
                  <input
                    type="text"
                    id="tpin"
                    value={formData.tpin}
                    onChange={(e) => handleInputChange('tpin', e.target.value.toUpperCase())}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ${
                      errors.tpin
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="e.g., 12345678-90"
                    disabled={isLoading}
                  />
                  {errors.tpin && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.tpin}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Tax Payer Identification Number
                  </p>
                </div>

                {/* Website */}
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Website *
                  </label>
                  <input
                    type="url"
                    id="website"
                    value={formData.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.website
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="e.g., www.company.com"
                    disabled={isLoading}
                  />
                  {errors.website && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.website}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.email
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                  )}
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.phone
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="+265 1 234 567"
                    disabled={isLoading}
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>
                  )}
                </div>

                {/* Fax (Optional) */}
                <div>
                  <label htmlFor="fax" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Fax Number
                  </label>
                  <input
                    type="tel"
                    id="fax"
                    value={formData.fax}
                    onChange={(e) => handleInputChange('fax', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.fax
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="+265 1 234 568 (optional)"
                    disabled={isLoading}
                  />
                  {errors.fax && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.fax}</p>
                  )}
                </div>

                {/* Postal Address */}
                <div>
                  <label htmlFor="postal_address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Postal Address *
                  </label>
                  <textarea
                    id="postal_address"
                    value={formData.postal_address}
                    onChange={(e) => handleInputChange('postal_address', e.target.value)}
                    rows={2}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.postal_address
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="P.O. Box 123, City, Country"
                    disabled={isLoading}
                  />
                  {errors.postal_address && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.postal_address}</p>
                  )}
                </div>

                {/* Physical Address */}
                <div>
                  <label htmlFor="physical_address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Physical Address *
                  </label>
                  <textarea
                    id="physical_address"
                    value={formData.physical_address}
                    onChange={(e) => handleInputChange('physical_address', e.target.value)}
                    rows={2}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.physical_address
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="Street address, building, city"
                    disabled={isLoading}
                  />
                  {errors.physical_address && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.physical_address}</p>
                  )}
                </div>

                {/* Date of Incorporation */}
                <div>
                  <label htmlFor="date_incorporation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date of Incorporation *
                  </label>
                  <input
                    type="date"
                    id="date_incorporation"
                    value={formData.date_incorporation}
                    onChange={(e) => handleInputChange('date_incorporation', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.date_incorporation
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    disabled={isLoading}
                  />
                  {errors.date_incorporation && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.date_incorporation}</p>
                  )}
                </div>

                {/* Place of Incorporation */}
                <div>
                  <label htmlFor="place_incorporation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Place of Incorporation *
                  </label>
                  <input
                    type="text"
                    id="place_incorporation"
                    value={formData.place_incorporation}
                    onChange={(e) => handleInputChange('place_incorporation', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.place_incorporation
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="e.g., Lilongwe, Malawi"
                    disabled={isLoading}
                  />
                  {errors.place_incorporation && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.place_incorporation}</p>
                  )}
                </div>

                {/* Profile Description */}
                <div>
                  <label htmlFor="profile_description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Profile Description *
                  </label>
                  <textarea
                    id="profile_description"
                    value={formData.profile_description}
                    onChange={(e) => handleInputChange('profile_description', e.target.value)}
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
                      errors.profile_description
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'
                    } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}
                    placeholder="Brief description of the organization's business activities and profile"
                    maxLength={1000}
                    disabled={isLoading}
                  />
                  {errors.profile_description && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.profile_description}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Maximum 1000 characters
                  </p>
                </div>
              </div>
            </div>

            {/* Fixed Footer */}
            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-200 dark:border-gray-600">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-2"></i>
                    {isEditing ? 'Update Organization' : 'Create Organization'}
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OrganizationModal;
