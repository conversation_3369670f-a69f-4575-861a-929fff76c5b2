'use client';

import React, { useState, useEffect, useRef } from 'react';
import { addressService, useAddresses } from '@/hooks/useAddressing';
import CountryDropdown from '../forms/CountryDropdown';
import { TextInput } from '../forms';
import { SequentialAddressBuilderProps, SequentialAddressData, PostalCodeData } from '@/types/address_types';

// Removed step-based navigation - now using unified form

const SequentialAddressBuilder: React.FC<SequentialAddressBuilderProps> = ({
  onAddressComplete,
  onAddressChange,
  initialData = {},
  disabled = false,
  className = '',
  onFieldChange,
  validationErrors = {}
}) => {
  // State management
  const [addressData, setAddressData] = useState<Partial<SequentialAddressData>>({
    country: '',
    region: '',
    district: '',
    location: '',
    postal_code: '',
    address_line_1: '',
    address_line_2: '',
    ...initialData
  });

  // Track if we're in edit mode (have initial data)
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Available options for each step
  const [regions, setRegions] = useState<string[]>([]);
  const [districts, setDistricts] = useState<string[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [postcodes, setPostcodes] = useState<PostalCodeData[]>([]);

  // Loading states
  const [loadingRegions, setLoadingRegions] = useState(false);
  const [loadingDistricts, setLoadingDistricts] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(false);
  const [loadingPostcodes, setLoadingPostcodes] = useState(false);

  // Use the addressing hook for postal code searches
  const { } = useAddresses();

  // Track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Initialize component with initial data (for edit mode)
  useEffect(() => {
    const initializeEditMode = async () => {
      if (initialData && Object.keys(initialData).length > 0 && !hasInitialized) {
        console.log('Initializing SequentialAddressBuilder in edit mode with data:', initialData);

        setIsEditMode(true);
        setAddressData(prev => ({ ...prev, ...initialData }));

        // If we have initial data for Malawi, load the appropriate dropdowns
        if (initialData.country === 'Malawi') {
          try {
            // Load regions first
            await loadRegions();

            // If we have a region, load districts
            if (initialData.region) {
              await loadDistricts(initialData.region);

              // If we have a district, load locations
              if (initialData.district) {
                await loadLocations(initialData.region, initialData.district);

                // If we have a location, load postcodes
                if (initialData.location) {
                  await loadPostcodes(initialData.region, initialData.district, initialData.location);
                }
              }
            }
          } catch (error) {
            console.error('Error initializing edit mode:', error);
          }
        }

        setHasInitialized(true);
      }
    };

    initializeEditMode();
  }, [initialData, hasInitialized]);

  // Watch for changes to initialData (when parent updates it)
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      console.log('SequentialAddressBuilder received updated initialData:', initialData);
      setAddressData(prev => ({ ...prev, ...initialData }));
    }
  }, [initialData]);

  console.log('Addressing form data', addressData);

  // Update address data and notify parent
  const updateAddressData = (updates: Partial<SequentialAddressData>) => {
    const newData = { ...addressData, ...updates };
    setAddressData(newData);
    onAddressChange?.(newData);

    // Notify parent of individual field changes
    Object.entries(updates).forEach(([field, value]) => {
      onFieldChange?.(field, value);
    });

    // Check if address is complete
    if (newData.country && newData.postal_code && newData.address_line_1) {
      onAddressComplete(newData as SequentialAddressData);
    }
  };

  // Handle country selection
  const handleCountryChange = (country: string) => {
    // In edit mode, be more conservative about clearing fields
    if (isEditMode && country === addressData.country) {
      // If selecting the same country in edit mode, don't clear anything
      return;
    }

    // Only clear location-specific fields if changing to a different country
    const updates: Partial<SequentialAddressData> = { country };

    // If changing from Malawi to another country or vice versa, clear location fields
    if ((addressData.country === 'Malawi' && country !== 'Malawi') ||
        (addressData.country !== 'Malawi' && country === 'Malawi')) {
      updates.region = '';
      updates.district = '';
      updates.location = '';
      updates.postal_code = '';
    }

    // Only clear address lines if explicitly changing countries (not in edit mode initialization)
    if (!isEditMode || (addressData.country && addressData.country !== country)) {
      updates.address_line_1 = '';
      updates.address_line_2 = '';
    }

    updateAddressData(updates);

    if (country === 'Malawi') {
      loadRegions();
    }
  };

  // Load regions for Malawi from cached data
  const loadRegions = async () => {
    setLoadingRegions(true);
    try {
      const response = await addressService.getRegions();
      if (isMountedRef.current && response.data) {
        setRegions(response.data);
      }
    } catch (error) {
      console.error('Failed to load regions:', error);
      // Fallback to predetermined regions
      if (isMountedRef.current) {
        setRegions(['Northern', 'Central', 'Southern']);
      }
    } finally {
      if (isMountedRef.current) {
        setLoadingRegions(false);
      }
    }
  };



  // Handle region selection
  const handleRegionChange = (region: string) => {
    // In edit mode, only clear dependent fields if region actually changed
    const updates: Partial<SequentialAddressData> = { region };

    if (!isEditMode || addressData.region !== region) {
      // Clear dependent fields only if region changed
      updates.district = '';
      updates.location = '';
      updates.postal_code = '';
    }

    updateAddressData(updates);
    loadDistricts(region);
  };

  // Load districts for selected region
  const loadDistricts = async (region: string) => {
    setLoadingDistricts(true);
    try {
      const response = await addressService.getDistrictsByRegion(region);
      if (isMountedRef.current && response.data) {
        setDistricts(response.data);
      }
    } catch (error) {
      console.error('Failed to load districts:', error);
      if (isMountedRef.current) {
        setDistricts([]);
      }
    } finally {
      if (isMountedRef.current) {
        setLoadingDistricts(false);
      }
    }
  };



  // Handle district selection
  const handleDistrictChange = (district: string) => {
    // In edit mode, only clear dependent fields if district actually changed
    const updates: Partial<SequentialAddressData> = { district };

    if (!isEditMode || addressData.district !== district) {
      // Clear dependent fields only if district changed
      updates.location = '';
      updates.postal_code = '';
    }

    updateAddressData(updates);
    loadLocations(addressData.region!, district);
  };

  // Load locations for selected district
  const loadLocations = async (region: string, district: string) => {
    setLoadingLocations(true);
    try {
      const response = await addressService.getLocationsByDistrict(region, district);
      if (isMountedRef.current && response.data) {
        setLocations(response.data);
      }
    } catch (error) {
      console.error('Failed to load locations:', error);
      if (isMountedRef.current) {
        setLocations([]);
      }
    } finally {
      if (isMountedRef.current) {
        setLoadingLocations(false);
      }
    }
  };



  // Handle location selection
  const handleLocationChange = (location: string) => {
    // In edit mode, only clear postal code if location actually changed
    const updates: Partial<SequentialAddressData> = { location };

    if (!isEditMode || addressData.location !== location) {
      // Clear postal code only if location changed
      updates.postal_code = '';
    }

    updateAddressData(updates);
    loadPostcodes(addressData.region!, addressData.district!, location);
  };

  // Load postcodes for selected location
  const loadPostcodes = async (region: string, district: string, location: string) => {
    setLoadingPostcodes(true);
    try {
      const response = await addressService.getPostcodesByLocation(region, district, location);
      if (isMountedRef.current && response.data) {
        setPostcodes(response.data);
      }
    } catch (error) {
      console.error('Failed to load postcodes:', error);
      if (isMountedRef.current) {
        setPostcodes([]);
      }
    } finally {
      if (isMountedRef.current) {
        setLoadingPostcodes(false);
      }
    }
  };



  // Handle postcode selection
  const handlePostcodeChange = (postal_code: string) => {
    updateAddressData({ postal_code });
  };

  // Handle address line changes
  const handleAddressLineChange = (field: keyof SequentialAddressData, value: string) => {
    updateAddressData({ [field]: value });
  };

  // Check if address lines should be enabled
  const areAddressLinesEnabled = () => {
    // In edit mode, if we already have address lines, keep them enabled
    if (isEditMode && (addressData.address_line_1 || addressData.address_line_2)) {
      return true;
    }

    // For Malawi, require postal code selection first
    if (addressData.country === 'Malawi') {
      return !!addressData.postal_code;
    }
    // For other countries, enable immediately when country is selected
    return !!addressData.country;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Country Selection - Always shown first */}
      <div>
        <CountryDropdown
          label="Country"
          value={addressData.country || ''}
          onChange={handleCountryChange}
          placeholder="Select your country"
          required
          disabled={disabled}
          error={validationErrors.country}
          id="sequential-country"
          name="country"
        />
      </div>

      {/* Malawi-specific fields - shown when Malawi is selected */}
      {addressData.country === 'Malawi' && (
        <div className="space-y-4">
          {/* Region Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Region *
            </label>
            <select
              value={addressData.region || ''}
              onChange={(e) => handleRegionChange(e.target.value)}
              disabled={disabled || loadingRegions}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
                validationErrors.region ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              <option value="">
                {loadingRegions ? 'Loading regions...' : 'Select region'}
              </option>
              {regions.map((region) => (
                <option key={region} value={region}>
                  {region}
                </option>
              ))}
            </select>
            {loadingRegions && (
              <div className="mt-2 flex items-center text-sm text-gray-500">
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                Loading available regions...
              </div>
            )}
            {validationErrors.region && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.region}</p>
            )}
          </div>

          {/* District Selection - shown when region is selected */}
          {addressData.region && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                District *
              </label>
              <select
                value={addressData.district || ''}
                onChange={(e) => handleDistrictChange(e.target.value)}
                disabled={disabled || loadingDistricts}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
                  validationErrors.district ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                }`}
              >
                <option value="">
                  {loadingDistricts ? 'Loading districts...' : 'Select district'}
                </option>
                {districts.map((district) => (
                  <option key={district} value={district}>
                    {district}
                  </option>
                ))}
              </select>
              {loadingDistricts && (
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Loading available districts...
                </div>
              )}
              {validationErrors.district && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.district}</p>
              )}
            </div>
          )}

          {/* Location/Area Selection - shown when district is selected */}
          {addressData.district && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Location/Area *
              </label>
              <select
                value={addressData.location || ''}
                onChange={(e) => handleLocationChange(e.target.value)}
                disabled={disabled || loadingLocations}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
                  validationErrors.location ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                }`}
              >
                <option value="">
                  {loadingLocations ? 'Loading locations...' : 'Select location/area'}
                </option>
                {locations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
              {loadingLocations && (
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Loading available locations...
                </div>
              )}
              {validationErrors.location && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.location}</p>
              )}
            </div>
          )}

          {/* Postcode Selection - shown when location is selected */}
          {addressData.location && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Postal Code *
              </label>
              <select
                value={addressData.postal_code || ''}
                onChange={(e) => handlePostcodeChange(e.target.value)}
                disabled={disabled || loadingPostcodes}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed ${
                  validationErrors.postal_code ? 'border-red-300' : 'border-gray-300 dark:border-gray-600'
                }`}
              >
                <option value="">
                  {loadingPostcodes ? 'Loading postal codes...' : 'Select postal code'}
                </option>
                {postcodes.map((pc) => (
                  <option key={pc.postal_code_id} value={pc.postal_code}>
                    {pc.postal_code} - {pc.location}
                  </option>
                ))}
              </select>
              {loadingPostcodes && (
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Loading available postal codes...
                </div>
              )}
              {postcodes.length === 0 && !loadingPostcodes && addressData.location && (
                <div className="mt-2 text-sm text-amber-600 dark:text-amber-400">
                  <i className="ri-alert-line mr-2"></i>
                  No postal codes found for this location. Please contact support.
                </div>
              )}
              {validationErrors.postal_code && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.postal_code}</p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Address Lines - shown when country is selected */}
      {addressData.country && (
        <div className="space-y-4">

          {/* For non-Malawi countries, show postal code input */}
          {addressData.country && addressData.country !== 'Malawi' && (
            <div>
              <TextInput
                label="Postal Code"
                value={addressData.postal_code || ''}
                onChange={(e) => handleAddressLineChange('postal_code', e.target.value)}
                placeholder="Enter postal code"
                required
                disabled={disabled}
                error={validationErrors.postal_code}
              />
            </div>
          )}

          {/* City field for non-Malawi countries */}
          {addressData.country && addressData.country !== 'Malawi' && (
            <div>
              <TextInput
                label="City"
                value={addressData.location || ''}
                onChange={(e) => handleAddressLineChange('location', e.target.value)}
                placeholder="Enter city"
                required
                disabled={disabled}
                error={validationErrors.city}
              />
            </div>
          )}

          {/* Address Line 1 */}
          <div>
            <TextInput
              label="Address Line 1"
              value={addressData.address_line_1 || ''}
              onChange={(e) => handleAddressLineChange('address_line_1', e.target.value)}
              placeholder={
                !areAddressLinesEnabled()
                  ? "Please select postal code first"
                  : "Enter street address, building name, etc."
              }
              required
              disabled={disabled || !areAddressLinesEnabled()}
              error={validationErrors.address_line_1}
            />
            {!areAddressLinesEnabled() && addressData.country === 'Malawi' && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <i className="ri-information-line mr-1"></i>
                Complete the location selection above to enable address input
              </p>
            )}
          </div>

          {/* Address Line 2 */}
          <div>
            <TextInput
              label="Address Line 2"
              value={addressData.address_line_2 || ''}
              onChange={(e) => handleAddressLineChange('address_line_2', e.target.value)}
              placeholder={
                !areAddressLinesEnabled()
                  ? "Please select postal code first"
                  : "Enter additional address information (optional)"
              }
              disabled={disabled || !areAddressLinesEnabled()}
              error={validationErrors.address_line_2}
            />
          </div>

          {/* Address Lines Enabled Notification for Malawi */}
          {addressData.country === 'Malawi' && addressData.postal_code && !addressData.address_line_1 && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md border border-green-200 dark:border-green-800">
              <div className="flex items-center text-sm text-green-800 dark:text-green-300">
                <i className="ri-map-pin-line mr-2"></i>
                Great! Now you can enter your specific address details above..
              </div>
            </div>
          )}

          {/* Show selected address summary */}
          {addressData.country && addressData.postal_code && addressData.address_line_1 && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md border border-green-200 dark:border-green-800">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                <i className="ri-map-pin-line mr-2"></i>
                Confirm Address Details
              </h4>
              <div className="text-sm text-green-700 dark:text-green-400 space-y-1">
                { addressData.country == 'Malawi' && (
                  <>
                    <p><strong>Region:</strong> {addressData.region}</p>
                    <p><strong>District:</strong> {addressData.district}</p>
                    <p><strong>Location:</strong> {addressData.location}</p>
                  </>
                )}
                <p><strong>Postal Code:</strong> {addressData.postal_code}</p>
                <p><strong>Address Line 1:</strong> {addressData.address_line_1}</p>
                {addressData.address_line_2 && (
                  <p><strong>Address Line 2:</strong> {addressData.address_line_2}</p>
                )}
              </div>
            </div>
          )}

          {/* Completion Status */}
          {addressData.address_line_1 && addressData.postal_code && (
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-200 dark:border-blue-800">
              <div className="flex items-center text-sm text-blue-800 dark:text-blue-300">
                <i className="ri-check-line mr-2"></i>
                {isEditMode
                  ? "Address details complete. You can now update."
                  : "Address completed! You can now proceed with your application."
                }
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SequentialAddressBuilder;
