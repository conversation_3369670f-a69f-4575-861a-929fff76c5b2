'use client';

import React, { useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEvaluationNavigation } from '@/hooks/useEvaluationNavigation';
import { applicationService } from '@/services/applicationService';

interface EvaluationPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluationPage: React.FC<EvaluationPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // Evaluation navigation
  const { getSteps } = useEvaluationNavigation(licenseType);

  // Redirect to first step immediately
  useEffect(() => {
    const redirectToFirstStep = async () => {
      if (!applicationId) {
        router.push('/applications');
        return;
      }

      try {
        // Load application to get license_category_id
        const application = await applicationService.getApplication(applicationId);

        const steps = getSteps();
        const firstStep = steps[0] || 'applicant-info';
        const params = new URLSearchParams();
        params.set('application_id', applicationId);

        // Get license category ID from application
        const licenseCategoryId = application?.license_category_id;

        if (licenseCategoryId) {
          params.set('license_category_id', licenseCategoryId);
          console.log('✅ Added license_category_id to URL:', licenseCategoryId);
        } else {
          console.warn('⚠️ No license_category_id found in application data');
        }

        router.push(`/applications/${licenseType}/evaluate/${firstStep}?${params.toString()}`);
      } catch (err) {
        console.error('Error loading application:', err);
        router.push('/applications');
      }
    };

    redirectToFirstStep();
  }, [applicationId, licenseType, router, getSteps]);

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Starting evaluation...</p>
      </div>
    </div>
  );
};

export default EvaluationPage;
