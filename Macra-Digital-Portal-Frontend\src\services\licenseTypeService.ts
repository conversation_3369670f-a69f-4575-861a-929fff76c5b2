import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';
import { PaginateQuery } from '@/types';
import { PaginatedResponse } from "@/types/license";

// Types
export interface LicenseType {
  license_type_id: string;
  name: string;
  code?: string;
  description: string;
  validity: number;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface NavigationItem {
  license_type_id: string;
  name: string;
  code: string;
  href: string;
  label: string;
  roles: string[];
}

export interface CreateLicenseTypeDto {
  name: string;
  description: string;
  validity: number;
}

export interface UpdateLicenseTypeDto {
  name?: string;
  description?: string;
  validity?: number;
}

export type LicenseTypesResponse = PaginatedResponse<LicenseType>;

export const licenseTypeService = {
  // Get all license types with pagination
  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/license-types?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get license type by ID
  async getLicenseType(id: string): Promise<LicenseType> {
    const response = await apiClient.get(`/license-types/${id}`);
    return processApiResponse(response);
  },

  
  // Get license type by ID
  async getLicenseTypeByCode(code: string): Promise<LicenseType> {
    const response = await apiClient.get(`/license-types/by-code/${code}`);
    return processApiResponse(response);
  },


  // Create new license type
  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {
    const response = await apiClient.post('/license-types', licenseTypeData);
    return processApiResponse(response);
  },

  // Update license type
  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {
    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);
    return processApiResponse(response);
  },

  // Delete license type
  async deleteLicenseType(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/license-types/${id}`);
    return processApiResponse(response);
  },

  // Get all license types (simple list for dropdowns) with caching
  async getAllLicenseTypes(): Promise<any> {
    return cacheService.getOrSet(
      CACHE_KEYS.LICENSE_TYPES,
      async () => {
        console.log('Fetching license types from API...');
        // Reduce limit to avoid rate limiting
        const response = await this.getLicenseTypes({ limit: 100 });
        return processApiResponse(response);
      },
      CACHE_TTL.LONG // Cache for 15 minutes
    );
  },

  // Get license types for sidebar navigation
  async getNavigationItems(): Promise<any> {
    try {
      const response = await apiClient.get('/license-types/navigation/sidebar');
      return processApiResponse(response);
    } catch (error) {
      console.error('LicenseTypeService.getNavigationItems error:', error);
      throw error;
    }
  },


};
