

export interface ProfileUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  organizationName?: string;
  profileImage?: string;
}

export interface DeactivateAccountData {
  reason: string;
  feedback?: string;
  user_id?: string;
}


export interface LicenseApplicationData {
  type: string;
  organizationName: string;
  description?: string;
  contactEmail?: string;
  contactPhone?: string;
  businessAddress?: string;
  businessType?: string;
  requestedStartDate?: string;
  additionalDocuments?: string[];
  notes?: string;
}

export interface PaymentCreateData {
  amount: number;
  currency: string;
  dueDate: string;
  issueDate: string;
  description: string;
  paymentType: string;
  clientName: string;
  clientEmail: string;
  paymentMethod?: string;
  notes?: string;
  relatedLicense?: string;
  relatedApplication?: string;
}

export interface TenderPaymentData {
  amount: number;
  currency: string;
  paymentMethod: string;
  description?: string;
}

export interface ComplaintData {
  title: string;
  description: string;
  category: string;
  attachments?: File[];
}
