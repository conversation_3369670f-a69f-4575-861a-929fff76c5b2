import React, { useState, useEffect } from 'react';
import DataDisplayCard from './DataDisplayCard';
import { Application } from '@/types/license';
import { contactPersonService } from '@/services/contactPersonService';

interface ContactInfoCardProps {
  application: Application | null;
  className?: string;
  showEmptyFields?: boolean;
  defaultCollapsed?: boolean;
}

const ContactInfoCard: React.FC<ContactInfoCardProps> = ({
  application,
  className = '',
  showEmptyFields = true,
  defaultCollapsed = false
}) => {
  const [contactPersons, setContactPersons] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchContactPersons = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        const contactResponse = await contactPersonService.getContactPersonsByApplication(application.application_id);
        const contactData = contactResponse?.data || contactResponse || [];
        setContactPersons(contactData);
      } catch (err) {
        console.warn('Could not load contact persons:', err);
        setContactPersons([]);
      } finally {
        setLoading(false);
      }
    };

    fetchContactPersons();
  }, [application?.application_id]);

  // Return empty fragment if no application or no contact persons
  if (!application || loading || !contactPersons || contactPersons.length === 0) {
    return <></>;
  }

  return (
    <>
      {contactPersons.map((contact, index) => (
        <DataDisplayCard
          key={index}
          title={`${contact.is_primary ? 'Primary' : 'Secondary'} Contact Person ${contactPersons.length > 1 ? `#${index + 1}` : ''}`}
          icon="ri-user-line"
          className={className}
          showEmptyFields={showEmptyFields}
          defaultCollapsed={defaultCollapsed}
          fields={[
            {
              label: 'First Name',
              value: contact.first_name,
              icon: 'ri-user-3-line'
            },
            {
              label: 'Last Name',
              value: contact.last_name,
              icon: 'ri-user-3-line'
            },
            {
              label: 'Middle Name',
              value: contact.middle_name,
              icon: 'ri-user-3-line'
            },
            {
              label: 'Designation',
              value: contact.designation,
              icon: 'ri-briefcase-line'
            },
            {
              label: 'Email Address',
              value: contact.email,
              type: 'email' as const,
              icon: 'ri-mail-line'
            },
            {
              label: 'Phone Number',
              value: contact.phone,
              type: 'phone' as const,
              icon: 'ri-phone-line'
            },
            {
              label: 'Primary Contact',
              value: contact.is_primary,
              type: 'boolean' as const,
              icon: 'ri-star-line'
            },
            ...(contact.notes ? [{
              label: 'Additional Notes',
              value: contact.notes,
              icon: 'ri-sticky-note-line',
              fullWidth: true
            }] : [])
          ]}
        />
      ))}
    </>
  );
};

export default ContactInfoCard;
