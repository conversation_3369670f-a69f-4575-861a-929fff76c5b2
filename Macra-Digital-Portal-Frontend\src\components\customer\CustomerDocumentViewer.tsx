'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import DataTable from '@/components/common/DataTable';
import DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';
import { customerApi } from '@/lib/customer-api';
import { PaginatedResponse, PaginateQuery } from '@/types';

// Document interface based on the backend entity
export interface Document extends Record<string, unknown> {
  document_id: string;
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface TableColumn {
  key: keyof Document | string;
  label: string;
  sortable?: boolean;
  searchable?: boolean;
  render?: (value: unknown, item: Document) => React.ReactNode;
  className?: string;
}

interface CustomerDocumentViewerProps {
  title?: string;
  subtitle?: string;
  searchPlaceholder?: string;
  showEntityInfo?: boolean;
  showRequiredColumn?: boolean;
  showCreatorInfo?: boolean;
  showActions?: boolean;
  customActions?: (document: Document) => React.ReactElement;
  filterParams?: Record<string, string | number | string[] | undefined>;
  className?: string;
}

const CustomerDocumentViewer: React.FC<CustomerDocumentViewerProps> = ({
  title = "Documents",
  subtitle = "View and manage documents",
  searchPlaceholder = "Search documents by name, type, or entity...",
  showEntityInfo = true,
  showRequiredColumn = true,
  showCreatorInfo = false,
  showActions = true,
  customActions,
  filterParams = {},
  className = "",
}) => {
  const [documentsData, setDocumentsData] = useState<PaginatedResponse<Document> | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);

  // Memoize filter params to prevent unnecessary re-renders
  const memoizedFilterParams = useMemo(() => filterParams, [JSON.stringify(filterParams)]);

  // Load documents function with debouncing
  const loadDocuments = useCallback(async (query: PaginateQuery) => {
    setLoading(true);
    setError(null);

    try {
      console.log('Loading customer documents with query:', query);

      // Use customer API to get documents (automatically filtered by user)
      const response = await customerApi.getDocuments({
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy?.join(','),
        ...memoizedFilterParams,
      });

      console.log('Customer documents loaded successfully:', response);
      setDocumentsData(response);
    } catch (err: unknown) {
      console.error('Error loading customer documents:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to load documents');
      } else {
        setError('Failed to load documents');
      }
    } finally {
      setLoading(false);
    }
  }, [memoizedFilterParams]);

  // Load documents on component mount - only once
  useEffect(() => {
    let isMounted = true;

    const initialLoad = async () => {
      if (isMounted) {
        await loadDocuments({
          page: 1,
          limit: 10,
          search: '',
          sortBy: ['created_at:DESC'],
        });
      }
    };

    initialLoad();

    return () => {
      isMounted = false;
    };
  }, [loadDocuments]);

  // Format file size helper
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Check if document type is previewable
  const isPreviewable = (mimeType: string = ""): boolean => {
    if (!mimeType) return false;
    const previewableMimeTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'text/html',
      'text/css',
      'text/javascript',
      'application/json',
    ];

    return previewableMimeTypes.includes(mimeType.toLowerCase());
  };

  // View document details handler
  const handleViewDocument = (doc: Document) => {
    setSelectedDocument(doc);
    setIsPreviewModalOpen(true);
  };

  // Close preview modal
  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setSelectedDocument(null);
  };

  // Download document handler
  const handleDownload = (doc: Document) => {
    const downloadAsync = async () => {
      try {
        const blob = await customerApi.downloadDocument(doc.document_id);

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = doc.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (err) {
        console.error('Error downloading document:', err);
      }
    };

    downloadAsync();
  };

  // Define table columns
  const columns: TableColumn[] = [
    {
      key: 'file_name',
      label: 'File Name',
      sortable: true,
      render: (_value: unknown, item: Document) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8">
            <div className="h-8 w-8 rounded-lg bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
              <i className="ri-file-text-line text-blue-600 dark:text-blue-400"></i>
            </div>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {item.file_name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {formatFileSize(item.file_size)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'document_type',
      label: 'Type',
      sortable: true,
      render: (value: unknown) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
          {String(value)}
        </span>
      ),
    },
  ];

  // Conditionally add entity info column
  if (showEntityInfo) {
    columns.push({
      key: 'entity_info',
      label: 'Related To',
      render: (_value: unknown, item: Document) => (
        <div className="text-sm">
          <div className="text-gray-900 dark:text-gray-100 font-medium">
            {item.entity_type}
          </div>
          <div className="text-gray-500 dark:text-gray-400 text-xs">
            {item.entity_id}
          </div>
        </div>
      ),
    });
  }

  // Conditionally add required column
  if (showRequiredColumn) {
    columns.push({
      key: 'is_required',
      label: 'Required',
      render: (_value: unknown, item: Document) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          item.is_required
            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
            : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
        }`}>
          {item.is_required ? 'Required' : 'Optional'}
        </span>
      ),
    });
  }

  // Conditionally add creator info column
  if (showCreatorInfo) {
    columns.push({
      key: 'creator',
      label: 'Uploaded By',
      render: (_value: unknown, item: Document) => (
        <div className="text-sm">
          <div className="text-gray-900 dark:text-gray-100">
            {item.creator ? `${item.creator.first_name} ${item.creator.last_name}` : 'Unknown'}
          </div>
          <div className="text-gray-500 dark:text-gray-400">
            {item.creator?.email}
          </div>
        </div>
      ),
    });
  }

  columns.push({
    key: 'created_at',
    label: 'Upload Date',
    sortable: true,
    render: (value: unknown) => (
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {new Date(String(value)).toLocaleDateString()}
      </span>
    ),
  });

  // Add actions column
  if (showActions) {
    columns.push({
      key: 'actions',
      label: 'Actions',
      render: (_value: unknown, item: Document) => (
        <div className="flex items-center space-x-2">
          {/* Preview Button */}
          {isPreviewable(item.mime_type) && (
            <button
              onClick={() => handleViewDocument(item)}
              className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30 rounded-md transition-colors"
              title="Preview document"
            >
              <i className="ri-eye-line mr-1"></i>
              Preview
            </button>
          )}
          
          {/* Download Button */}
          <button
            onClick={() => handleDownload(item)}
            className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 rounded-md transition-colors"
            title="Download document"
          >
            <i className="ri-download-line mr-1"></i>
            Download
          </button>

          {/* Custom Actions */}
          {customActions && customActions(item)}
        </div>
      ),
    });
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <i className="ri-folder-line mr-3 text-blue-600"></i>
                {title}
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {subtitle}
              </p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="ri-error-warning-line text-red-400"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Error loading documents
                </h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Data Table */}
        <div className="p-6">
          <DataTable<Document>
            data={documentsData}
            columns={columns}
            loading={loading}
            searchPlaceholder={searchPlaceholder}
            onQueryChange={loadDocuments}
            emptyStateIcon="ri-file-search-line"
            emptyStateMessage="No documents have been uploaded yet."
          />
        </div>
      </div>

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        document={selectedDocument ? {
          document_id: selectedDocument.document_id,
          document_type: selectedDocument.document_type,
          file_name: selectedDocument.file_name,
          entity_type: selectedDocument.entity_type,
          entity_id: selectedDocument.entity_id,
          file_path: selectedDocument.file_path,
          file_size: selectedDocument.file_size,
          mime_type: selectedDocument.mime_type,
          is_required: selectedDocument.is_required,
          created_at: selectedDocument.created_at,
          updated_at: selectedDocument.updated_at,
          created_by: selectedDocument.created_by,
          updated_by: selectedDocument.updated_by,
        } : null}
        isOpen={isPreviewModalOpen}
        onClose={handleClosePreview}
        onDownload={(doc) => handleDownload(doc as Document)}
      />
    </div>
  );
};

export default CustomerDocumentViewer;
