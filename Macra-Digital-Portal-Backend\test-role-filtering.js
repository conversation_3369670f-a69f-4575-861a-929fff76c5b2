const mysql = require('mysql2/promise');

async function testRoleBasedFiltering() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'macra_db'
    });
    
    console.log('🔍 Checking user roles...');
    const [users] = await connection.execute(`
      SELECT u.user_id, u.email, u.first_name, u.last_name, 
             GROUP_CONCAT(r.name) as roles
      FROM users u
      LEFT JOIN user_roles ur ON u.user_id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.role_id
      WHERE u.deleted_at IS NULL AND u.status = 'active'
      GROUP BY u.user_id, u.email, u.first_name, u.last_name
      ORDER BY u.created_at DESC
      LIMIT 10
    `);
    
    console.log('📊 Active users and their roles:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} - Roles: ${user.roles || 'No roles'}`);
    });
    
    console.log('\n💳 Checking payments count...');
    const [paymentCount] = await connection.execute('SELECT COUNT(*) as count FROM payments WHERE deleted_at IS NULL');
    console.log(`📊 Total payments: ${paymentCount[0].count}`);
    
    console.log('\n📋 Checking invoices count...');
    const [invoiceCount] = await connection.execute('SELECT COUNT(*) as count FROM invoices WHERE deleted_at IS NULL');
    console.log(`📊 Total invoices: ${invoiceCount[0].count}`);
    
    // Check specific user data
    console.log('\n🔍 Checking finance user data...');
    const [financeUser] = await connection.execute(`
      SELECT u.user_id, u.email, GROUP_CONCAT(r.name) as roles
      FROM users u
      LEFT JOIN user_roles ur ON u.user_id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.role_id
      WHERE u.email = '<EMAIL>' AND u.deleted_at IS NULL
      GROUP BY u.user_id, u.email
    `);
    
    if (financeUser.length > 0) {
      console.log(`📊 Finance user: ${financeUser[0].email} (${financeUser[0].user_id}) - Roles: ${financeUser[0].roles}`);
      
      // Check payments for this user
      const [userPayments] = await connection.execute(
        'SELECT COUNT(*) as count FROM payments WHERE user_id = ? AND deleted_at IS NULL',
        [financeUser[0].user_id]
      );
      console.log(`💳 Payments for finance user: ${userPayments[0].count}`);
      
      // Check invoices for this user
      const [userInvoices] = await connection.execute(
        'SELECT COUNT(*) as count FROM invoices WHERE client_id = ? AND deleted_at IS NULL',
        [financeUser[0].user_id]
      );
      console.log(`📋 Invoices for finance user: ${userInvoices[0].count}`);
    } else {
      console.log('❌ Finance user not found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testRoleBasedFiltering();
