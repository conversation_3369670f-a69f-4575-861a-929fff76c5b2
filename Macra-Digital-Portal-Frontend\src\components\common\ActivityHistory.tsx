'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { activityNotesService, ActivityNote } from '@/services/activityNotesService';

interface ActivityHistoryProps {
  entityType: string;
  entityId: string;
  title?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  maxHeight?: string;
  className?: string;
  onNotesChange?: (notes: ActivityNote[]) => void;
  refreshTrigger?: number; // For external refresh
}

const ActivityHistory: React.FC<ActivityHistoryProps> = ({
  entityType,
  entityId,
  title = 'Activity History',
  showSearch = true,
  showFilters = false,
  maxHeight = 'max-h-96',
  className = '',
  onNotesChange,
  refreshTrigger = 0
}) => {
  const [notes, setNotes] = useState<ActivityNote[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNoteType, setSelectedNoteType] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Load notes when component mounts or when entity changes
  useEffect(() => {
    if (entityType && entityId) {
      loadNotes();
    }
  }, [entityType, entityId, refreshTrigger]);

  // Notify parent when notes change
  useEffect(() => {
    if (onNotesChange) {
      onNotesChange(notes);
    }
  }, [notes, onNotesChange]);

  const loadNotes = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await activityNotesService.findByEntity(entityType, entityId);
      const activityNotes = response.data || response;
      
      // Ensure we always have an array
      if (Array.isArray(activityNotes)) {
        setNotes(activityNotes);
      } else {
        console.warn('Unexpected response format for notes:', activityNotes);
        setNotes([]);
      }
    } catch (error) {
      console.error('Failed to load activity notes:', error);
      setError('Failed to load activity history. Please try again.');
      setNotes([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter and search notes
  const filteredNotes = useMemo(() => {
    return notes.filter(note => {
      // Search filter
      const matchesSearch = searchTerm === '' || 
        note.note.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.creator?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.creator?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.note_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.category?.toLowerCase().includes(searchTerm.toLowerCase());

      // Note type filter
      const matchesNoteType = selectedNoteType === 'all' || note.note_type === selectedNoteType;

      // Category filter
      const matchesCategory = selectedCategory === 'all' || note.category === selectedCategory;

      // Priority filter
      const matchesPriority = selectedPriority === 'all' || note.priority === selectedPriority;

      return matchesSearch && matchesNoteType && matchesCategory && matchesPriority;
    });
  }, [notes, searchTerm, selectedNoteType, selectedCategory, selectedPriority]);

  // Get unique values for filter dropdowns
  const noteTypes = useMemo(() => {
    const types = [...new Set(notes.map(note => note.note_type))];
    return types.sort();
  }, [notes]);

  const categories = useMemo(() => {
    const cats = [...new Set(notes.map(note => note.category).filter(Boolean))];
    return cats.sort();
  }, [notes]);

  const priorities = useMemo(() => {
    const prios = [...new Set(notes.map(note => note.priority))];
    return prios.sort();
  }, [notes]);

  const formatNoteType = (noteType: string) => {
    return noteType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getNoteIcon = (noteType: string) => {
    switch (noteType) {
      case 'evaluation_comment': return 'ri-chat-3-line';
      case 'status_update': return 'ri-flag-line';
      case 'system_log': return 'ri-settings-line';
      case 'review_note': return 'ri-eye-line';
      case 'approval_note': return 'ri-check-line';
      case 'rejection_note': return 'ri-close-line';
      default: return 'ri-sticky-note-line';
    }
  };

  const getNoteTypeColor = (noteType: string) => {
    switch (noteType) {
      case 'evaluation_comment': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'status_update': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'system_log': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'review_note': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'approval_note': return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200';
      case 'rejection_note': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedNoteType('all');
    setSelectedCategory('all');
    setSelectedPriority('all');
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
            <i className="ri-history-line mr-2 text-purple-600"></i>
            {title} ({filteredNotes.length})
          </h3>
          <button
            onClick={loadNotes}
            disabled={loading}
            className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50"
            title="Refresh"
          >
            <i className={`ri-refresh-line ${loading ? 'animate-spin' : ''}`}></i>
          </button>
        </div>

        {/* Search and Filters */}
        {(showSearch || showFilters) && (
          <div className="mt-4 space-y-3">
            {/* Search */}
            {showSearch && (
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i className="ri-search-line text-gray-400"></i>
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  placeholder="Search activity notes..."
                />
              </div>
            )}

            {/* Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                <select
                  value={selectedNoteType}
                  onChange={(e) => setSelectedNoteType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Types</option>
                  {noteTypes.map(type => (
                    <option key={type} value={type}>{formatNoteType(type)}</option>
                  ))}
                </select>

                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>

                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Priorities</option>
                  {priorities.map(priority => (
                    <option key={priority} value={priority}>{priority}</option>
                  ))}
                </select>

                <button
                  onClick={clearFilters}
                  className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Content */}
      <div className={`${maxHeight} overflow-y-auto`}>
        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
            <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
              <i className="ri-error-warning-line mr-2"></i>
              {error}
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-gray-600 dark:text-gray-400">Loading activity history...</span>
          </div>
        ) : filteredNotes.length === 0 ? (
          <div className="text-center py-8">
            <i className="ri-chat-3-line text-4xl text-gray-400 mb-4"></i>
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {notes.length === 0 ? 'No Activity Yet' : 'No Matching Results'}
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              {notes.length === 0 
                ? 'No notes or communications have been recorded for this entity.'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {filteredNotes.map((note) => (
              <div key={note.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                {/* Note Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          {note?.creator?.first_name?.charAt(0)}{note?.creator?.last_name?.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {note.creator?.first_name} {note.creator?.last_name}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getNoteTypeColor(note.note_type)}`}>
                          <i className={`${getNoteIcon(note.note_type)} mr-1`}></i>
                          {formatNoteType(note.note_type)}
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <i className="ri-time-line mr-1"></i>
                        <span>{new Date(note.created_at).toLocaleString()}</span>
                        {note.step && (
                          <>
                            <span className="mx-2">•</span>
                            <i className="ri-footprint-line mr-1"></i>
                            <span className="capitalize">{note.step.replace(/-/g, ' ')}</span>
                          </>
                        )}
                        {note.category && (
                          <>
                            <span className="mx-2">•</span>
                            <i className="ri-bookmark-line mr-1"></i>
                            <span className="capitalize">{note.category}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {note.priority !== 'normal' && (
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        note.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                        note.priority === 'critical' ? 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                      }`}>
                        {note.priority}
                      </span>
                    )}
                    {note.metadata?.is_email_message && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <i className="ri-mail-line mr-1"></i>
                        Email Sent
                      </span>
                    )}
                  </div>
                </div>

                {/* Note Content */}
                <div className="mb-3">
                  <p className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {note.note}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivityHistory;
