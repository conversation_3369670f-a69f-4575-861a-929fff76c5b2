import { formatLicenseCategoryFee } from '../formatters';

describe('formatLicenseCategoryFee', () => {
  describe('Short Code Allocation category', () => {
    test('should return "Free" when fee is 0', () => {
      expect(formatLicenseCategoryFee(0, 'Short Code Allocation')).toBe('Free');
      expect(formatLicenseCategoryFee('0', 'Short Code Allocation')).toBe('Free');
    });

    test('should return "Free" when fee is empty', () => {
      expect(formatLicenseCategoryFee('', 'Short Code Allocation')).toBe('Free');
    });

    test('should format currency when fee is greater than 0', () => {
      expect(formatLicenseCategoryFee(1000, 'Short Code Allocation')).toContain('1,000');
      expect(formatLicenseCategoryFee('1500', 'Short Code Allocation')).toContain('1,500');
    });
  });

  describe('Other license categories', () => {
    test('should return "Contact MACRA" when fee is 0', () => {
      expect(formatLicenseCategoryFee(0, 'Other License')).toBe('Contact MACRA');
      expect(formatLicenseCategoryFee('0', 'Other License')).toBe('Contact MACRA');
    });

    test('should return "Contact MACRA" when fee is empty', () => {
      expect(formatLicenseCategoryFee('', 'Other License')).toBe('Contact MACRA');
    });

    test('should format currency when fee is greater than 0', () => {
      expect(formatLicenseCategoryFee(1000, 'Other License')).toContain('1,000');
      expect(formatLicenseCategoryFee('1500', 'Other License')).toContain('1,500');
    });
  });

  describe('Edge cases', () => {
    test('should handle case-sensitive category names', () => {
      expect(formatLicenseCategoryFee(0, 'short code allocation')).toBe('Contact MACRA');
      expect(formatLicenseCategoryFee(0, 'SHORT CODE ALLOCATION')).toBe('Contact MACRA');
    });

    test('should handle partial matches', () => {
      expect(formatLicenseCategoryFee(0, 'Short Code')).toBe('Contact MACRA');
      expect(formatLicenseCategoryFee(0, 'Code Allocation')).toBe('Contact MACRA');
    });

    test('should handle null and undefined values', () => {
      expect(formatLicenseCategoryFee(null as any, 'Short Code Allocation')).toBe('Free');
      expect(formatLicenseCategoryFee(undefined as any, 'Short Code Allocation')).toBe('Free');
      expect(formatLicenseCategoryFee(null as any, 'Other License')).toBe('Contact MACRA');
      expect(formatLicenseCategoryFee(undefined as any, 'Other License')).toBe('Contact MACRA');
    });
  });

  describe('Currency formatting', () => {
    test('should use default MWK currency', () => {
      const result = formatLicenseCategoryFee(1000, 'Test License');
      expect(result).toContain('MWK');
    });

    test('should use custom currency when provided', () => {
      const result = formatLicenseCategoryFee(1000, 'Test License', 'USD');
      expect(result).toContain('USD');
    });
  });
});
