'use client';

import { useState, useCallback, useRef } from 'react';

interface RateLimitState {
  isRateLimited: boolean;
  retryAfter: number | null;
  lastRateLimitTime: number | null;
}

interface UseRateLimitReturn {
  isRateLimited: boolean;
  retryAfter: number | null;
  handleRateLimit: (retryAfter?: number) => void;
  clearRateLimit: () => void;
  canMakeRequest: () => boolean;
}

export const useRateLimit = (): UseRateLimitReturn => {
  const [state, setState] = useState<RateLimitState>({
    isRateLimited: false,
    retryAfter: null,
    lastRateLimitTime: null
  });
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleRateLimit = useCallback((retryAfter: number = 60) => {
    const now = Date.now();
    
    setState({
      isRateLimited: true,
      retryAfter,
      lastRateLimitTime: now
    });

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set timeout to clear rate limit
    timeoutRef.current = setTimeout(() => {
      setState(prev => ({
        ...prev,
        isRateLimited: false,
        retryAfter: null
      }));
    }, retryAfter * 1000);
  }, []);

  const clearRateLimit = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    setState({
      isRateLimited: false,
      retryAfter: null,
      lastRateLimitTime: null
    });
  }, []);

  const canMakeRequest = useCallback(() => {
    if (!state.isRateLimited) return true;
    
    // Check if enough time has passed since last rate limit
    if (state.lastRateLimitTime && state.retryAfter) {
      const timePassed = (Date.now() - state.lastRateLimitTime) / 1000;
      return timePassed >= state.retryAfter;
    }
    
    return false;
  }, [state.isRateLimited, state.lastRateLimitTime, state.retryAfter]);

  return {
    isRateLimited: state.isRateLimited,
    retryAfter: state.retryAfter,
    handleRateLimit,
    clearRateLimit,
    canMakeRequest
  };
};
