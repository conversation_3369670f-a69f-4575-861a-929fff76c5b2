'use client';

import React, { useState } from 'react';
import SequentialAddressBuilder from './SequentialAddressBuilder';

interface SequentialAddressData {
  country: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  address_line_1: string;
  address_line_2: string;
}

/**
 * Example component demonstrating the SequentialAddressBuilder
 * Shows how to integrate the step-by-step address building process
 */
const SequentialAddressExample: React.FC = () => {
  const [completedAddress, setCompletedAddress] = useState<SequentialAddressData | null>(null);
  const [currentAddress, setCurrentAddress] = useState<Partial<SequentialAddressData>>({});
  const [isDisabled, setIsDisabled] = useState(false);

  const handleAddressComplete = (address: SequentialAddressData) => {
    setCompletedAddress(address);
    console.log('Address completed:', address);
  };

  const handleAddressChange = (address: Partial<SequentialAddressData>) => {
    setCurrentAddress(address);
    console.log('Address changed:', address);
  };

  const handleReset = () => {
    setCompletedAddress(null);
    setCurrentAddress({});
  };

  const handleSaveAddress = async () => {
    if (!completedAddress) return;

    setIsDisabled(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Here you would typically call your address creation API
      console.log('Saving address:', completedAddress);
      
      alert('Address saved successfully!');
    } catch (error) {
      console.error('Failed to save address:', error);
      alert('Failed to save address. Please try again.');
    } finally {
      setIsDisabled(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Sequential Address Builder
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          This component guides users through a step-by-step address building process, 
          with special handling for Malawi addresses using the postal codes database.
        </p>
      </div>

      {/* Address Builder */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Build Your Address
        </h2>
        
        <SequentialAddressBuilder
          onAddressComplete={handleAddressComplete}
          onAddressChange={handleAddressChange}
          disabled={isDisabled}
          className="mb-4"
        />
      </div>

      {/* Current Progress */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-6">
        <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
          Current Progress
        </h3>
        <div className="space-y-2 text-sm">
          {Object.entries(currentAddress).map(([key, value]) => (
            value && (
              <div key={key} className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 capitalize">
                  {key.replace(/_/g, ' ')}:
                </span>
                <span className="text-gray-900 dark:text-gray-100 font-medium">
                  {value}
                </span>
              </div>
            )
          ))}
          {Object.keys(currentAddress).length === 0 && (
            <p className="text-gray-500 dark:text-gray-400 italic">
              No address data entered yet
            </p>
          )}
        </div>
      </div>

      {/* Completed Address */}
      {completedAddress && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
          <h3 className="text-md font-medium text-green-800 dark:text-green-300 mb-3">
            <i className="ri-check-circle-line mr-2"></i>
            Completed Address
          </h3>
          <div className="space-y-2 text-sm text-green-700 dark:text-green-400">
            <div><strong>Country:</strong> {completedAddress.country}</div>
            {completedAddress.country === 'Malawi' && (
              <>
                <div><strong>Region:</strong> {completedAddress.region}</div>
                <div><strong>District:</strong> {completedAddress.district}</div>
                <div><strong>Location:</strong> {completedAddress.location}</div>
                <div><strong>Postal Code:</strong> {completedAddress.postal_code}</div>
              </>
            )}
            <div><strong>Address Line 1:</strong> {completedAddress.address_line_1}</div>
            {completedAddress.address_line_2 && (
              <div><strong>Address Line 2:</strong> {completedAddress.address_line_2}</div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-4">
        <button
          onClick={handleReset}
          disabled={isDisabled}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <i className="ri-refresh-line mr-2"></i>
          Reset
        </button>

        {completedAddress && (
          <button
            onClick={handleSaveAddress}
            disabled={isDisabled}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDisabled ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                Saving...
              </>
            ) : (
              <>
                <i className="ri-save-line mr-2"></i>
                Save Address
              </>
            )}
          </button>
        )}
      </div>

      {/* Features List */}
      <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h3 className="text-md font-medium text-blue-800 dark:text-blue-300 mb-3">
          <i className="ri-information-line mr-2"></i>
          Features
        </h3>
        <ul className="text-sm text-blue-700 dark:text-blue-400 space-y-1">
          <li>• Sequential step-by-step address building</li>
          <li>• Special handling for Malawi with region → district → location → postcode</li>
          <li>• Debounced postal code searches for performance</li>
          <li>• Caching of postal code data (15-minute TTL)</li>
          <li>• Real-time validation and progress tracking</li>
          <li>• Responsive design with loading states</li>
          <li>• Accessibility support with proper ARIA labels</li>
          <li>• Dark mode compatible</li>
        </ul>
      </div>

      {/* Integration Notes */}
      <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
        <h3 className="text-md font-medium text-yellow-800 dark:text-yellow-300 mb-3">
          <i className="ri-code-line mr-2"></i>
          Integration Notes
        </h3>
        <div className="text-sm text-yellow-700 dark:text-yellow-400 space-y-2">
          <p>
            <strong>onAddressComplete:</strong> Called when all required fields are filled
          </p>
          <p>
            <strong>onAddressChange:</strong> Called on every field change for real-time updates
          </p>
          <p>
            <strong>initialData:</strong> Pre-populate the component with existing address data
          </p>
          <p>
            <strong>disabled:</strong> Disable all inputs during save operations
          </p>
        </div>
      </div>
    </div>
  );
};

export default SequentialAddressExample;
