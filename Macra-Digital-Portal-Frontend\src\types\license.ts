import { LicenseCategory } from ".";
import { User } from "./user";

export interface LicenseType {
  license_type_id: string;
  name: string;
  validity?: number;
  code?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface StatusAction {
  status: ApplicationStatus;
  label: string;
  icon: string;
  color: string;
  hoverColor: string;
  roles?: string[],
  description: string;
  confirmMessage?: string;
}


export interface Applicant {
  applicant_id: string;
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  address_id?: string;
  contact_id?: string;
  date_incorporation: string;
  place_incorporation: string;
  created_at: string;
  updated_at: string;
}

export interface Application extends Record<string, unknown> {
  application_id: string;
  application_number: string;
  applicant_id: string;
  license_category_id: string;
  status: string;
  current_step: number;
  progress_percentage: number;
  submitted_at?: string;
  created_at: string;
  updated_at: string;
  assigned_to?: string;
  assigned_at?: string;
  application_data?: any;
  applicant?: Applicant;
  license_category?:LicenseCategory;
  assignee?:User;
}

export enum ApplicationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  EVALUATION = 'evaluation',
  PENDING_PAYMENT = 'pending_payment',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  PASS_EVALUATION = 'pass_evaluation',
  WAITING_FOR_APPROVAL = 'waiting_for_approval',
}

export interface ApplicationFilters {
  licenseTypeId?: string;
  licenseCategoryId?: string;
  status?: ApplicationStatus | '';
  dateRange?: string;
  search?: string;
}

export interface License extends Record<string, unknown> {
  license_id: string;
  license_number: string;
  description?: string;
  application_id: string;
  applicant_id: string;
  license_type_id: string;
  status: LicenseStatus;
  issue_date: string;
  expiry_date: string;
  issued_by: string;
  code?: string;
  conditions?: string;
  created_at: string;
  updated_at: string;
  application: Application;
  issuer?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export enum LicenseStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  REVOKED = 'revoked',
  UNDER_REVIEW = 'under_review',
}


