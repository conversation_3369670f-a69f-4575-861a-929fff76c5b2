import { AxiosError } from 'axios';
import { BaseEntity, UserReference } from './index';

// Audit enums
export enum AuditAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  SUBMIT = 'SUBMIT',
  ASSIGN = 'ASSIGN',
  COMPLETE = 'COMPLETE'
}

export enum AuditModule {
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  APPLICATION_MANAGEMENT = 'APPLICATION_MANAGEMENT',
  PAYMENT_MANAGEMENT = 'PAYMENT_MANAGEMENT',
  INVOICE_MANAGEMENT = 'INVOICE_MANAGEMENT',
  TASK_MANAGEMENT = 'TASK_MANAGEMENT',
  ORGANIZATION_MANAGEMENT = 'ORGANIZATION_MANAGEMENT',
  DEPARTMENT_MANAGEMENT = 'DEPARTMENT_MANAGEMENT',
  LICENSE_MANAGEMENT = 'LICENSE_MANAGEMENT',
  NOTIFICATION_MANAGEMENT = 'NOTIFICATION_MANAGEMENT',
  SYSTEM_SETTINGS = 'SYSTEM_SETTINGS',
  AUTHENTICATION = 'AUTHENTICATION'
}

// Audit interfaces
export interface AuditLog extends BaseEntity {
  audit_id: string;
  action: AuditAction;
  module: AuditModule;
  resource_type?: string;
  resource_id?: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  metadata?: Record<string, any>;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  
  // Relations
  user?: UserReference;
}

export interface CreateAuditLogDto {
  action: AuditAction;
  module: AuditModule;
  resource_type?: string;
  resource_id?: string;
  description?: string;
  metadata?: Record<string, any>;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
}

export interface AuditLogFilters {
  search?: string;
  action?: AuditAction;
  module?: AuditModule;
  resource_type?: string;
  resource_id?: string;
  user_id?: string;
  date_from?: string;
  date_to?: string;
  ip_address?: string;
}

// Audit responses
export type AuditLogsResponse = {
  data: AuditLog[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
};

export interface AuditStatistics {
  total_logs: number;
  actions_summary: Record<AuditAction, number>;
  modules_summary: Record<AuditModule, number>;
  top_users: {
    user_id: string;
    user_name: string;
    action_count: number;
  }[];
  recent_activities: AuditLog[];
}


// Error handling utility
export class AuditTrailError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'AuditTrailError';
  }
}

// Handle API errors consistently
const handleApiError = (error: any): never => {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const message = error.response?.data?.message || error.message;
    const code = error.code;

    if (status === 401) {
      throw new AuditTrailError('Authentication required', 'UNAUTHORIZED', status);
    } else if (status === 403) {
      throw new AuditTrailError('Access denied', 'FORBIDDEN', status);
    } else if (status === 404) {
      throw new AuditTrailError('Audit trail not found', 'NOT_FOUND', status);
    } else if (status === 429) {
      throw new AuditTrailError('Too many requests', 'RATE_LIMITED', status);
    } else if (status && status >= 500) {
      throw new AuditTrailError('Server error occurred', 'SERVER_ERROR', status);
    } else if (code === 'ERR_NETWORK' || error.message === 'Network Error') {
      throw new AuditTrailError('Network error - please check your connection', 'NETWORK_ERROR');
    } else {
      throw new AuditTrailError(message || 'An unexpected error occurred', code, status, error.response?.data);
    }
  }

  throw new AuditTrailError(error.message || 'An unexpected error occurred');
};

export interface AuditTrail {
  audit_id: string;
  action: string;
  module: string;
  status: string;
  resource_type: string;
  resource_id?: string;
  description?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  error_message?: string;
  user_id?: string;
  user?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  created_at: string;
}

export interface AuditTrailFilters {
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  action?: string;
  module?: string;
  status?: string;
  ipAddress?: string;
  resourceType?: string;
  resourceId?: string;
}

// Standard API response format (matches backend)
export interface StandardResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  meta?: any;
  timestamp: string;
  path: string;
  statusCode: number;
}