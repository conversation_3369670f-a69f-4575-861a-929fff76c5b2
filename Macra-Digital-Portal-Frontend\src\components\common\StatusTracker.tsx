'use client';

import React from 'react';
import { ApplicationStatus } from '@/types/license';

interface StatusTrackerProps {
  currentStatus: ApplicationStatus | string;
  className?: string;
}

interface Step {
  id: number;
  name: string;
  icon: string;
  description: string;
}

const StatusTracker: React.FC<StatusTrackerProps> = ({ currentStatus, className = '' }) => {
  const steps: Step[] = [
    {
      id: 1,
      name: 'Draft',
      icon: 'ri-draft-line',
      description: 'Application being prepared'
    },
    {
      id: 2,
      name: 'Submitted',
      icon: 'ri-send-plane-line',
      description: 'Application submitted for review'
    },
    {
      id: 3,
      name: 'Under Review',
      icon: 'ri-search-eye-line',
      description: 'Initial review in progress'
    },
    {
      id: 4,
      name: 'Evaluation',
      icon: 'ri-file-search-line',
      description: 'Detailed evaluation in progress'
    },
    {
      id: 5,
      name: 'Approved',
      icon: 'ri-checkbox-circle-line',
      description: 'Application approved'
    },
    {
      id: 6,
      name: 'Rejected',
      icon: 'ri-close-circle-line',
      description: 'Application rejected'
    }
  ];

  const getStepStatus = (stepId: number) => {
    const status = currentStatus;

    // Map database status to step progression
    switch (status) {
      case 'draft':
        return stepId === 1 ? 'current' : 'upcoming';
      case 'submitted':
        return stepId <= 2 ? (stepId === 2 ? 'current' : 'complete') : 'upcoming';
      case 'under_review':
        return stepId <= 3 ? (stepId === 3 ? 'current' : 'complete') : 'upcoming';
      case 'evaluation':
        return stepId <= 4 ? (stepId === 4 ? 'current' : 'complete') : 'upcoming';
      case 'approved':
        return stepId <= 5 ? 'complete' : 'upcoming';
      case 'rejected':
        return stepId === 6 ? 'error' : stepId < 6 ? 'complete' : 'upcoming';
      default:
        return stepId === 1 ? 'current' : 'upcoming';
    }
  };

  // Don't show status tracker for approved applications
  if (currentStatus === ApplicationStatus.APPROVED) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Progress line */}
      <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600">
        <div
          className={`h-full bg-primary transition-all duration-500 ${
            (currentStatus as string) === ApplicationStatus.APPROVED ? 'w-full' :
            currentStatus === ApplicationStatus.EVALUATION ? 'w-4/5' :
            currentStatus === ApplicationStatus.UNDER_REVIEW ? 'w-3/5' :
            currentStatus === ApplicationStatus.SUBMITTED ? 'w-2/5' :
            currentStatus === ApplicationStatus.DRAFT ? 'w-1/5' :
            'w-0'
          }`}
        />
      </div>

      {/* Steps */}
      <div className="relative flex justify-between">
        {steps.map((step) => {
          const stepStatus = getStepStatus(step.id);
          return (
            <div key={step.id} className="flex flex-col items-center">
              <div className={`
                w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300
                ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :
                  stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :
                  stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :
                  'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}
              `}>
                <i className={step.icon}></i>
              </div>
              <div className="mt-3 text-center">
                <div className={`text-sm font-medium ${
                  stepStatus === 'complete' || stepStatus === 'current' ? 'text-primary' :
                  stepStatus === 'error' ? 'text-red-500' :
                  'text-gray-400'
                }`}>
                  {step.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20 leading-tight">
                  {step.description}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StatusTracker;
