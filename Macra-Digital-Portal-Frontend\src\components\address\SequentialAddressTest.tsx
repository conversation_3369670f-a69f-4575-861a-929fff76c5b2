'use client';

import React, { useState, useEffect } from 'react';
import { addressService } from '@/hooks/useAddressing';

/**
 * Test component to verify that regions are loading correctly
 */
const SequentialAddressTest: React.FC = () => {
  const [regions, setRegions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testLoadRegions = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Testing region loading...');
      const response = await addressService.getRegions();
      console.log('Regions response:', response);
      
      if (response && response.data) {
        setRegions(response.data);
        console.log('Regions loaded:', response.data);
      } else {
        setError('No regions data received');
        console.error('No regions data in response:', response);
      }
    } catch (err: any) {
      console.error('Failed to load regions:', err);
      setError(err.message || 'Failed to load regions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testLoadRegions();
  }, []);

  return (
    <div className="p-6 max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Region Loading Test
      </h2>

      <button
        onClick={testLoadRegions}
        disabled={loading}
        className="mb-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Loading...' : 'Test Load Regions'}
      </button>

      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <p className="text-sm text-red-600 dark:text-red-400">
            <strong>Error:</strong> {error}
          </p>
        </div>
      )}

      <div className="space-y-2">
        <h3 className="font-medium text-gray-900 dark:text-gray-100">
          Regions ({regions.length}):
        </h3>
        {loading ? (
          <div className="flex items-center text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            Loading regions...
          </div>
        ) : regions.length > 0 ? (
          <ul className="space-y-1">
            {regions.map((region, index) => (
              <li
                key={index}
                className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-sm text-gray-700 dark:text-gray-300"
              >
                {region}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            No regions loaded yet
          </p>
        )}
      </div>

      {/* Debug Information */}
      <div className="mt-6 p-3 bg-gray-50 dark:bg-gray-900 rounded">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          Debug Info:
        </h4>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
          <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
          <p><strong>Error:</strong> {error || 'None'}</p>
          <p><strong>Regions Count:</strong> {regions.length}</p>
          <p><strong>Service Available:</strong> {addressService ? 'Yes' : 'No'}</p>
        </div>
      </div>
    </div>
  );
};

export default SequentialAddressTest;
