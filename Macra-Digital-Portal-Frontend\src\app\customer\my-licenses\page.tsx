'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { licenseService } from '@/services/licenseService';
import { License, PaginatedResponse, PaginateQuery } from '@/types';
import DataTable from '@/components/common/DataTable';
import Select from '@/components/common/Select';
import toast from 'react-hot-toast';

interface LicenseFilters {
  status?: string;
}

const MyLicensesPage: React.FC = () => {
  const [licensesData, setLicensesData] = useState<PaginatedResponse<License> | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LicenseFilters>({});

  // Load licenses function following the standard pattern
  const loadLicenses = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);

      // Backend automatically filters licenses for customer role
      // No need for special filtering - just pass the standard parameters
      const params = {
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: Array.isArray(query.sortBy) ? query.sortBy[0] : query.sortBy,
        sortOrder: Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] as 'ASC' | 'DESC' : undefined,
        status: filters.status,
      };

      const response = await licenseService.getLicenses(params);
      setLicensesData(response);
    } catch (err: unknown) {
      console.error('Error loading licenses:', err);

      let errorMessage = 'Failed to load licenses';
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      }
      setError(errorMessage);

      // Set empty data structure to prevent undefined errors
      setLicensesData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
          filter: {},
        },
        links: {
          first: '',
          current: '',
          last: '',
        },
      });

    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load licenses when component mounts or filters change
  useEffect(() => {
    loadLicenses({ page: 1, limit: 10 });
  }, [loadLicenses]);

  // Handle filter changes
  const handleFilterChange = (key: keyof LicenseFilters, value: string) => {
    const newFilters = { ...filters };

    if (value && value.trim() !== '') {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }

    setFilters(newFilters);
  };

  // Handler for DataTable query changes (pagination, search, sorting)
  const handleQueryChange = useCallback((query: PaginateQuery) => {
    loadLicenses(query);
  }, [loadLicenses]);

  // Download license PDF function
  const handleDownloadPDF = async (licenseId: string, licenseNumber: string) => {
    try {
      const blob = await licenseService.downloadLicensePDF(licenseId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `license-${licenseNumber}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('License PDF downloaded successfully');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('Failed to download license PDF');
    }
  };

  // Get status badge for license status
  const getStatusBadge = (status: string) => {
    const statusClasses: Record<string, string> = {
      'active': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'expired': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      'suspended': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
      'revoked': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      'under_review': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || statusClasses['active']}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  // Define license table columns following the standard pattern
  const licenseColumns = [
    {
      key: 'license_number',
      label: 'License Number',
      sortable: true,
      render: (_: unknown, item: License) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
              <i className="ri-award-line text-blue-600 dark:text-blue-400"></i>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {item.license_number}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {item.code || 'N/A'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'license_type',
      label: 'License Type',
      render: (_: unknown, item: License) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {item?.description || 'N/A'}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {item?.code || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (_: unknown, item: License) => getStatusBadge(item.status),
    },
    {
      key: 'issue_date',
      label: 'Issue Date',
      sortable: true,
      render: (_: unknown, item: License) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(item.issue_date).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'expiry_date',
      label: 'Expiry Date',
      sortable: true,
      render: (_: unknown, item: License) => {
        const expiryDate = new Date(item.expiry_date);
        const isExpired = expiryDate < new Date();
        const isExpiringSoon = expiryDate < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

        return (
          <span className={`text-sm ${isExpired ? 'text-red-600 dark:text-red-400' : isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>
            {expiryDate.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: unknown, item: License) => (
        <div className="flex items-center justify-end space-x-2">
          <button
            type="button"
            onClick={() => handleDownloadPDF(item.license_id, item.license_number)}
            className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
          >
            <i className="ri-download-line mr-1"></i>
            Download
          </button>
        </div>
      ),
    },
  ];

  // Status filter options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'expired', label: 'Expired' },
    { value: 'suspended', label: 'Suspended' },
    { value: 'revoked', label: 'Revoked' },
    { value: 'under_review', label: 'Under Review' },
  ];

  return (
    <CustomerLayout>
      <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Page header */}
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">My Licenses</h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  View and manage your issued licenses.
                </p>
              </div>
              <div className="flex space-x-3">
                <Link
                  href="/customer/applications/apply"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                >
                  <div className="w-4 h-4 flex items-center justify-center mr-2">
                    <i className="ri-add-line"></i>
                  </div>
                  New Application
                </Link>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select
                  value={filters.status || ''}
                  onChange={(value) => handleFilterChange('status', value)}
                  options={statusOptions}
                  placeholder="Filter by status"
                />
              </div>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-6">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <i className="ri-error-warning-line text-red-400"></i>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Error loading licenses
                    </h3>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <p>{error}</p>
                    </div>
                  </div>
                  <div className="ml-auto pl-3">
                    <button
                      type="button"
                      onClick={() => setError(null)}
                      className="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                    >
                      <span className="sr-only">Dismiss</span>
                      <i className="ri-close-line text-sm"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Licenses Table */}
          <DataTable<License>
            columns={licenseColumns}
            data={licensesData}
            loading={loading}
            onQueryChange={handleQueryChange}
            searchPlaceholder="Search licenses by number or type..."
            emptyStateIcon="ri-award-line"
            emptyStateMessage="No licenses found. Apply for a license to get started."
          />
        </div>
      </div>
    </CustomerLayout>
  );
};

export default MyLicensesPage;
