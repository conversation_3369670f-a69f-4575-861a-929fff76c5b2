'use client';

import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { InvoiceFilters, CreateInvoiceDto, UpdateInvoiceDto, InvoiceItem, Invoice } from '@/types/invoice';



class InvoiceService {
  private baseUrl = '/invoices';

  async getInvoices(filters?: InvoiceFilters): Promise<Invoice[]> {
    try {
      const response = await apiClient.get(this.baseUrl, { params: filters });
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to fetch invoices:', error);
      throw error;
    }
  }

  async getInvoiceById(id: string): Promise<Invoice> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${id}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to fetch invoice:', error);
      throw error;
    }
  }

  async getInvoicesByEntity(entityType: string, entityId: string): Promise<any> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to fetch invoices for entity:', error);
      throw error;
    }
  }

  async createInvoice(data: CreateInvoiceDto): Promise<Invoice> {
    try {
      const response = await apiClient.post(this.baseUrl, data);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to create invoice:', error);
      throw error;
    }
  }

  async updateInvoice(id: string, data: UpdateInvoiceDto): Promise<Invoice> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/${id}`, data);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to update invoice:', error);
      throw error;
    }
  }

  async deleteInvoice(id: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error('Failed to delete invoice:', error);
      throw error;
    }
  }

  async sendInvoice(id: string): Promise<Invoice> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${id}/send`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to send invoice:', error);
      throw error;
    }
  }

  async markAsPaid(id: string): Promise<Invoice> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${id}/mark-paid`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to mark invoice as paid:', error);
      throw error;
    }
  }

  // Helper method to generate invoice for application
  async generateApplicationInvoice(applicationId: string, data: {
    amount: number;
    description: string;
    items?: InvoiceItem[];
  }): Promise<Invoice> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/generate/application/${applicationId}`, data);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to generate application invoice:', error);
      throw error;
    }
  }

  // Helper method to get application invoice status
  async getApplicationInvoiceStatus(applicationId: string): Promise<{
    hasInvoice: boolean;
    invoice?: Invoice;
    status?: 'paid' | 'pending' | 'overdue' | 'none';
  }> {
    try {
      const response = await this.getInvoicesByEntity('application', applicationId);
      const invoices: Invoice[]  = response.data || response
      
      if (invoices.length === 0) {
        return { hasInvoice: false, status: 'none' };
      }

      // Get the most recent invoice
      const sortedInvoices = [...invoices].sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      const latestInvoice = sortedInvoices.length > 0 ? sortedInvoices[0] : null;

      if (!latestInvoice) {
        return { hasInvoice: false, status: 'none' };
      }

      let status: 'paid' | 'pending' | 'overdue' = 'pending';
      
      if (latestInvoice.status === 'paid') {
        status = 'paid';
      } else if (latestInvoice.status === 'overdue') {
        status = 'overdue';
      } else if (latestInvoice.status === 'sent' || latestInvoice.status === 'draft') {
        // Check if overdue
        const dueDate = new Date(latestInvoice.due_date);
        const now = new Date();
        if (now > dueDate) {
          status = 'overdue';
        } else {
          status = 'pending';
        }
      }

      return {
        hasInvoice: true,
        invoice: latestInvoice,
        status
      };
    } catch (error) {
      console.error('Failed to get application invoice status:', error);
      return { hasInvoice: false, status: 'none' };
    }
  }

  // Helper method to get application details for invoice generation
  async getApplicationDetailsForInvoice(applicationId: string): Promise<{
    application: any;
    defaultInvoiceData: {
      amount: number;
      description: string;
      items: InvoiceItem[];
    };
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}/details`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Failed to get application details for invoice:', error);
      throw error;
    }
  }
}

export const invoiceService = new InvoiceService();
