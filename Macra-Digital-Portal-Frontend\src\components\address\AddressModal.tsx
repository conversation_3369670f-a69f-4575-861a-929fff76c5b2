'use client';

import { useState, useEffect } from 'react';
import { Address, AddressModalProps, ADDRESS_TYPES, CreateAddressDto, EditAddressData, EntityType } from '@/types/address_types';
import { useAddresses } from '@/hooks/useAddressing';
import SequentialAddressBuilder from './SequentialAddressBuilder';
import { SequentialAddressData } from '@/types/address_types';

const AddressModal = ({ isOpen, onClose, onSave, address, entityType, entityId }: AddressModalProps) => {
  const [sequentialData, setSequentialData] = useState<Partial<SequentialAddressData>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const { createAddress, editAddress, getPostcodeDetails, populateAddressFromPostcode } = useAddresses();

  const isEditing = !!address;

  // Initialize sequential data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (address) {
        console.log(`Existing address`, address);
        // Editing existing address - convert to sequential format
        if (address.country === 'Malawi') {
          getPostcodeDetails(address.postal_code).then(postcodeDetails => {
            if (postcodeDetails) {
              console.log(`Postcode details`, postcodeDetails);
              setSequentialData({
                country: address.country || 'Malawi',
                region: postcodeDetails.region || '',
                district: postcodeDetails.district || '',
                location: address.city || '',
                postal_code: address.postal_code || '',
                address_line_1: address.address_line_1 || '',
                address_line_2: address.address_line_2 || '',
              });
            } else {
              // Handle error or default to empty data
              setSequentialData({
                country: address.country || 'Malawi',
                region: '',
                district: '',
                location: address.city || '',
                postal_code: address.postal_code || '',
                address_line_1: address.address_line_1 || '',
                address_line_2: address.address_line_2 || '',
              });
            }
          });
        } else {
          // For international addresses, we don't have region, district, and location data
          setSequentialData({
            country: address.country || '',
            region: '', // These fields might not be available in Address type
            district: '',
            location: address.city || '',
            postal_code: address.postal_code || '',
            address_line_1: address.address_line_1 || '',
            address_line_2: address.address_line_2 || '',
          });
        }
      } else {
        // Creating new address - start with empty data
        setSequentialData({
          country: '',
          region: '',
          district: '',
          location: '',
          postal_code: '',
          address_line_1: '',
          address_line_2: '',
        });
      }
      setErrors({});
    }
  }, [isOpen, address]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Address line 1 validation
    if (!sequentialData.address_line_1?.trim()) {
      newErrors.address_line_1 = 'Address line 1 is required';
    } else if (sequentialData.address_line_1.length > 255) {
      newErrors.address_line_1 = 'Address line 1 must be 255 characters or less';
    }

    // Postal code validation
    if (!sequentialData.postal_code?.trim()) {
      newErrors.postal_code = 'Postal code is required';
    } else if (sequentialData.postal_code.length > 9) {
      newErrors.postal_code = 'Postal code must be 9 characters or less';
    }

    // Country validation
    if (!sequentialData.country?.trim()) {
      newErrors.country = 'Country is required';
    } else if (sequentialData.country.length > 50) {
      newErrors.country = 'Country must be 50 characters or less';
    }

    // Location/City validation (for Malawi, this could be location; for others, city)
    if (sequentialData.country === 'Malawi') {
      if (!sequentialData.location?.trim()) {
        newErrors.location = 'Location is required';
      }
    } else {
      if (!sequentialData.location?.trim()) {
        newErrors.location = 'City is required';
      }
    }

    // Optional field length validations
    if (sequentialData.address_line_2 && sequentialData.address_line_2.length > 255) {
      newErrors.address_line_2 = 'Address line 2 must be 255 characters or less';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handlers for SequentialAddressBuilder
  const handleSequentialAddressComplete = (address: SequentialAddressData) => {
    setSequentialData(address);
    setErrors({}); // Clear any validation errors
  };

  const handleSequentialAddressChange = (address: Partial<SequentialAddressData>) => {
    setSequentialData(prev => ({ ...prev, ...address }));
  };

  const handleFieldChange = (field: string, value: string) => {
    setSequentialData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // Convert sequentialData to the format expected by the API
      const formData: CreateAddressDto = {
        address_type: ADDRESS_TYPES.BUSINESS,
        entity_type: entityType || EntityType.USER,
        entity_id: entityId || '',
        address_line_1: sequentialData.address_line_1 || '',
        address_line_2: sequentialData.address_line_2 || '',
        address_line_3: '', // Not used in sequential builder
        postal_code: sequentialData.postal_code || '',
        country: sequentialData.country || '',
        city: sequentialData.location || '', // Map location to city
      };

      let savedAddress: Address;

      if (isEditing && address) {
        // Update existing address
        const updateData: EditAddressData = {
          address_id: address.address_id,
          ...formData,
        };
        const response = await editAddress(updateData);
        savedAddress = response.data;
      } else {
        // Create new address
        const response = await createAddress(formData);
        savedAddress = response.data;
      }

      onSave(savedAddress);
      onClose();
    } catch (error: any) {
      console.error('Error saving address:', error);

      // Extract error message from API response
      let errorMessage = 'Failed to save address. Please try again.';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setErrors({ general: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            {/* Header */}
            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                  {isEditing ? 'Edit Address' : 'Add New Address'}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>

              {/* General error */}
              {errors.general && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
                </div>
              )}

              {/* Sequential Address Builder */}
              <div className="space-y-4">
                <SequentialAddressBuilder
                  onAddressComplete={handleSequentialAddressComplete}
                  onAddressChange={handleSequentialAddressChange}
                  onFieldChange={handleFieldChange}
                  validationErrors={errors}
                  initialData={sequentialData}
                  disabled={isLoading}
                  className="space-y-4"
                />
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-2"></i>
                    {isEditing ? 'Update Address' : 'Create Address'}
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AddressModal;
