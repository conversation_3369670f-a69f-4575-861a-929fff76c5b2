/**
 * Test file for ShortCodeUsage component
 * Basic validation and functionality tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ShortCodeUsage from './ShortCodeUsage';
import { DEFAULT_SHORTCODE_FORM_DATA, ShortcodeFormData } from '@/types/shortcode';

// Mock the shortcode service
jest.mock('@/services/shortcodeService', () => ({
  shortcodeService: {
    validateFormData: jest.fn((data: ShortcodeFormData) => {
      const errors: string[] = [];
      if (!data.audience) errors.push('Audience is required');
      if (!data.category) errors.push('Category is required');
      if (!data.description?.trim()) errors.push('Description is required');
      return errors;
    })
  }
}));

describe('ShortCodeUsage Component', () => {
  const mockOnDataChange = jest.fn();
  const mockOnValidationChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <ShortCodeUsage
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    expect(screen.getByText('Short Code Usage Information')).toBeInTheDocument();
  });

  it('displays all required form fields', () => {
    render(
      <ShortCodeUsage
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    expect(screen.getByLabelText(/Intended Audience/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Service Category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Service Description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Additional Notes/i)).toBeInTheDocument();
  });

  it('calls onDataChange when form fields are updated', async () => {
    render(
      <ShortCodeUsage
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    const descriptionField = screen.getByLabelText(/Service Description/i);
    fireEvent.change(descriptionField, { target: { value: 'Test description' } });
    
    await waitFor(() => {
      expect(mockOnDataChange).toHaveBeenCalled();
    });
  });

  it('shows validation errors for required fields', async () => {
    render(
      <ShortCodeUsage
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
        showValidation={true}
      />
    );
    
    // Trigger validation by blurring a required field
    const descriptionField = screen.getByLabelText(/Service Description/i);
    fireEvent.blur(descriptionField);
    
    await waitFor(() => {
      expect(screen.getByText('Description is required')).toBeInTheDocument();
    });
  });

  it('initializes with default data', () => {
    render(
      <ShortCodeUsage
        initialData={DEFAULT_SHORTCODE_FORM_DATA}
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    const audienceField = screen.getByDisplayValue('community');
    expect(audienceField).toBeInTheDocument();
  });

  it('can be disabled', () => {
    render(
      <ShortCodeUsage
        disabled={true}
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    const descriptionField = screen.getByLabelText(/Service Description/i);
    expect(descriptionField).toBeDisabled();
  });

  it('displays important information notice', () => {
    render(
      <ShortCodeUsage
        onDataChange={mockOnDataChange}
        onValidationChange={mockOnValidationChange}
      />
    );
    
    expect(screen.getByText('Important Information')).toBeInTheDocument();
    expect(screen.getByText(/Short codes will be automatically assigned/i)).toBeInTheDocument();
  });
});
