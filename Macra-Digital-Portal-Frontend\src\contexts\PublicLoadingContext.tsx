'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import PublicLoader from '@/components/public/PublicLoader';

interface PublicLoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  showLoader: (message?: string, type?: 'verification' | 'statistics' | 'general' | 'page-load') => void;
  hideLoader: () => void;
  updateProgress: (progress: number) => void;
  setLoadingMessage: (message: string) => void;
}

const PublicLoadingContext = createContext<PublicLoadingContextType | undefined>(undefined);

export const usePublicLoading = () => {
  const context = useContext(PublicLoadingContext);
  if (!context) {
    throw new Error('usePublicLoading must be used within a PublicLoadingProvider');
  }
  return context;
};

interface PublicLoadingProviderProps {
  children: React.ReactNode;
}

export const PublicLoadingProvider: React.FC<PublicLoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessageState] = useState('Loading...');
  const [loadingType, setLoadingType] = useState<'verification' | 'statistics' | 'general' | 'page-load'>('general');
  const [progress, setProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);
  const pathname = usePathname();

  // Handle route changes with smooth transitions
  useEffect(() => {
    const handleStart = () => {
      setIsLoading(true);
      setProgress(0);
      setShowProgress(false);
      
      // Set appropriate loading type and message based on route
      if (pathname.includes('/verify')) {
        setLoadingType('verification');
        setLoadingMessageState('Loading verification portal...');
      } else if (pathname.includes('/statistics')) {
        setLoadingType('statistics');
        setLoadingMessageState('Loading statistics dashboard...');
      } else if (pathname.includes('/help')) {
        setLoadingType('general');
        setLoadingMessageState('Loading help center...');
      } else {
        setLoadingType('page-load');
        setLoadingMessageState('Loading page...');
      }
    };

    const handleComplete = () => {
      // Simulate progress completion
      setProgress(100);
      
      // Add a small delay to ensure smooth transition
      setTimeout(() => {
        setIsLoading(false);
        setProgress(0);
        setShowProgress(false);
      }, 300);
    };

    // Start loading immediately
    handleStart();
    
    // Complete loading after a short delay for smooth UX
    const timer = setTimeout(handleComplete, 500);

    return () => clearTimeout(timer);
  }, [pathname]);

  const setLoading = (loading: boolean) => {
    setIsLoading(loading);
    if (!loading) {
      setProgress(0);
      setShowProgress(false);
    }
  };

  const showLoader = (
    message: string = 'Loading...', 
    type: 'verification' | 'statistics' | 'general' | 'page-load' = 'general'
  ) => {
    setLoadingMessageState(message);
    setLoadingType(type);
    setIsLoading(true);
    setProgress(0);
    setShowProgress(false);
  };

  const hideLoader = () => {
    setIsLoading(false);
    setProgress(0);
    setShowProgress(false);
  };

  const updateProgress = (newProgress: number) => {
    setProgress(Math.min(100, Math.max(0, newProgress)));
    setShowProgress(true);
  };

  const setLoadingMessage = (message: string) => {
    setLoadingMessageState(message);
  };

  // Get dynamic messages based on loading type
  const getDynamicMessages = () => {
    switch (loadingType) {
      case 'verification':
        return [
          'Connecting to MACRA database...',
          'Validating license information...',
          'Checking license status...',
          'Retrieving license details...',
          'Finalizing verification...'
        ];
      case 'statistics':
        return [
          'Loading license statistics...',
          'Fetching latest data...',
          'Calculating metrics...',
          'Preparing charts...',
          'Almost ready...'
        ];
      case 'page-load':
        return [
          'Loading page content...',
          'Preparing interface...',
          'Setting up components...',
          'Almost ready...'
        ];
      default:
        return [
          'Loading...',
          'Please wait...',
          'Processing...',
          'Almost done...'
        ];
    }
  };

  return (
    <PublicLoadingContext.Provider 
      value={{ 
        isLoading, 
        setLoading, 
        showLoader, 
        hideLoader, 
        updateProgress, 
        setLoadingMessage 
      }}
    >
      {children}
      {isLoading && (
        <div className="fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-90 dark:bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center">
          <PublicLoader
            type={loadingType}
            message={loadingMessage}
            submessage="Please wait while we prepare everything for you"
            showProgress={showProgress}
            progress={progress}
            size="lg"
            dynamicMessages={getDynamicMessages()}
            messageInterval={2000}
            className="animate-fadeIn"
          />
        </div>
      )}
    </PublicLoadingContext.Provider>
  );
};

// Hook for easy access to loading functions with predefined types
export const usePublicLoadingHelpers = () => {
  const { showLoader, hideLoader, updateProgress, setLoadingMessage } = usePublicLoading();

  return {
    showVerificationLoader: (message?: string) => showLoader(message, 'verification'),
    showStatisticsLoader: (message?: string) => showLoader(message, 'statistics'),
    showGeneralLoader: (message?: string) => showLoader(message, 'general'),
    showPageLoader: (message?: string) => showLoader(message, 'page-load'),
    hideLoader,
    updateProgress,
    setLoadingMessage
  };
};

// Higher-order component for automatic loading states
export const withPublicLoading = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    loadingType?: 'verification' | 'statistics' | 'general' | 'page-load';
    loadingMessage?: string;
    autoHide?: boolean;
    hideDelay?: number;
  }
) => {
  const WrappedComponent: React.FC<P> = (props) => {
    const { showLoader, hideLoader } = usePublicLoading();

    useEffect(() => {
      if (options?.loadingMessage) {
        showLoader(options.loadingMessage, options.loadingType);
      }

      if (options?.autoHide) {
        const timer = setTimeout(() => {
          hideLoader();
        }, options.hideDelay || 1000);

        return () => clearTimeout(timer);
      }
    }, [showLoader, hideLoader]);

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withPublicLoading(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};
