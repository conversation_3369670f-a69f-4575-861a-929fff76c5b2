'use client';

import { useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { notificationService, getStatusChangeMessage } from '@/services/notificationService';
import { ApplicationStatus } from '@/types/license';

interface ApplicationStatusChange {
  application_id: string;
  application_number: string;
  license_category_name: string;
  old_status: ApplicationStatus;
  new_status: ApplicationStatus;
  step?: number;
  progress_percentage?: number;
}

export const useApplicationNotifications = () => {
  const { user } = useAuth();
  const { showSuccess, showInfo, showWarning, showError } = useToast();

  // Show toast notification for status changes
  const showStatusChangeNotification = useCallback(
    (statusChange: ApplicationStatusChange) => {
      const { title, message, type } = getStatusChangeMessage(
        statusChange.application_number,
        statusChange.license_category_name,
        statusChange.old_status,
        statusChange.new_status,
        statusChange.step,
        statusChange.progress_percentage
      );

      const duration = type === 'success' ? 8000 : 6000; // Show success messages longer

      switch (type) {
        case 'success':
          showSuccess(message, duration);
          break;
        case 'warning':
          showWarning(message, duration);
          break;
        case 'error':
          showError(message, duration);
          break;
        default:
          showInfo(message, duration);
      }
    },
    [showSuccess, showInfo, showWarning, showError]
  );

  // Poll for new notifications and show toasts
  const checkForNewNotifications = useCallback(async () => {
    if (!user) return;

    try {
      const data = await notificationService.getUserNotifications({ 
        limit: 5, 
        status: 'unread' 
      });

      // Validate the response data
      if (!data || !Array.isArray(data.notifications)) {
        console.warn('Invalid notification data received:', data);
        return;
      }

      // Show toast for recent unread notifications (within last 5 minutes)
      const recentNotifications = data.notifications.filter(notification => {
        try {
          const createdAt = new Date(notification.created_at);
          const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
          return createdAt > fiveMinutesAgo && notification.type === 'status_change';
        } catch (error) {
          console.warn('Error filtering notification:', notification, error);
          return false;
        }
      });

      recentNotifications.forEach(notification => {
        try {
          if (notification.metadata?.old_status && notification.metadata?.new_status) {
            showStatusChangeNotification({
              application_id: notification.application_id,
              application_number: notification.application_number,
              license_category_name: notification.license_category_name,
              old_status: notification.metadata.old_status,
              new_status: notification.metadata.new_status,
              step: notification.metadata.step,
              progress_percentage: notification.metadata.progress_percentage
            });
          }
        } catch (error) {
          console.warn('Error showing notification:', notification, error);
        }
      });
    } catch (error: any) {
      console.error('Error checking for new notifications:', {
        error: error.message || 'Unknown error',
        response: error.response?.data || null,
        status: error.response?.status || null,
        userId: user?.id || 'unknown'
      });
      
      // Don't show error toast for background notification checks
      // as it would be annoying for users
    }
  }, [user, showStatusChangeNotification]);

  // Check for notifications when component mounts and every 30 seconds
  useEffect(() => {
    if (user) {
      // Initial check with a small delay to allow auth to settle
      const initialTimeout = setTimeout(checkForNewNotifications, 1000);
      
      // Regular polling every 30 seconds
      const interval = setInterval(checkForNewNotifications, 30000);
      
      return () => {
        clearTimeout(initialTimeout);
        clearInterval(interval);
      };
    }
  }, [user, checkForNewNotifications]);

  return {
    showStatusChangeNotification,
    checkForNewNotifications
  };
};

// Hook for tracking application status changes in components
export const useApplicationStatusTracker = (applicationId?: string) => {
  const { showStatusChangeNotification } = useApplicationNotifications();

  const trackStatusChange = useCallback(
    (
      applicationNumber: string,
      licenseCategoryName: string,
      oldStatus: ApplicationStatus,
      newStatus: ApplicationStatus,
      step?: number,
      progressPercentage?: number
    ) => {
      if (!applicationId) return;

      // Show immediate toast notification
      showStatusChangeNotification({
        application_id: applicationId,
        application_number: applicationNumber,
        license_category_name: licenseCategoryName,
        old_status: oldStatus,
        new_status: newStatus,
        step,
        progress_percentage: progressPercentage
      });

      // Create notification record (this would typically be done by the backend)
      notificationService.createStatusChangeNotification(
        applicationId,
        oldStatus,
        newStatus,
        step,
        progressPercentage
      ).catch(error => {
        console.error('Error creating notification record:', error);
      });
    },
    [applicationId, showStatusChangeNotification]
  );

  return { trackStatusChange };
};