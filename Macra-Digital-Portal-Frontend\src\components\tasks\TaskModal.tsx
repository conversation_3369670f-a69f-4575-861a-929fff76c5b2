'use client';

import { useState, useEffect } from 'react';
import Select from '../common/Select';
import { taskService } from '@/services/task-assignment';
import { Task, CreateTaskDto, TaskType, TaskPriority, TaskStatus, User, UpdateTaskDto } from '@/types';

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (task: Task) => void;
  task?: Task | null;
}

const TaskModal = ({ isOpen, onClose, onSave, task }: TaskModalProps) => {
  const [formData, setFormData] = useState<CreateTaskDto>({
    task_type: TaskType.APPLICATION,
    title: '',
    description: '',
    priority: TaskPriority.MEDIUM,
    status: TaskStatus.PENDING,
    entity_type: '',
    entity_id: '',
    due_date: '',
    assigned_to: '',
  });
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadUsers();
      if (task) {
        // Edit mode - populate form with task data
        setFormData({
          task_type: task.task_type,
          title: task.title,
          description: task.description,
          priority: task.priority,
          status: task.status,
          entity_type: task.entity_type || '',
          entity_id: task.entity_id || '',
          due_date: task.due_date ? task.due_date.split('T')[0] : '', // Format for date input
          assigned_to: task.assigned_to || '',
        });
      } else {
        // Create mode - reset form
        setFormData({
          task_type: TaskType.APPLICATION,
          title: '',
          description: '',
          priority: TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          entity_type: '',
          entity_id: '',
          due_date: '',
          assigned_to: '',
        });
      }
      setErrors({});
    }
  }, [isOpen, task]);

  const loadUsers = async () => {
    try {
      const response = await taskService.getOfficers();
      setUsers(response.data);
    } catch (error) {
      console.error('Error loading users:', error);
      setUsers([]);
    }
  };

  const handleInputChange = (field: keyof CreateTaskDto, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (formData.description && !formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.task_type) {
      newErrors.task_type = 'Task type is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      let savedTask: Task;
      
      if (task) {
        // Update existing task
        const updateData: UpdateTaskDto = {
          title: formData.title,
          description: formData.description,
          priority: formData.priority,
          status: formData.status,
          entity_type: formData.entity_type || undefined,
          entity_id: formData.entity_id || undefined,
          due_date: formData.due_date || undefined,
        };
        savedTask = await taskService.updateTask(task.task_id, updateData);
      } else {
        // Create new task
        const createData: CreateTaskDto = {
          ...formData,
          entity_type: formData.entity_type || undefined,
          entity_id: formData.entity_id || undefined,
          due_date: formData.due_date || undefined,
          assigned_to: formData.assigned_to || undefined,
        };
        savedTask = await taskService.createTask(createData);
      }

      onSave(savedTask);
      onClose();
    } catch (error: any) {
      console.error('Error saving task:', error);
      if (error.response?.data?.message) {
        setErrors({ submit: error.response.data.message });
      } else {
        setErrors({ submit: 'Failed to save task. Please try again.' });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 transform transition-all max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {task ? 'Edit Task' : 'Create New Task'}
          </h3>
          <button
            type="button"
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1"
            aria-label="Close modal"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
        {errors.submit && (
          <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
            {errors.submit}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Title */}
          <div className="md:col-span-2">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.title ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Enter task title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title}</p>
            )}
          </div>

          {/* Task Type */}
          <div>
            <Select
              label="Task Type *"
              value={formData.task_type}
              onChange={(value) => handleInputChange('task_type', value)}
              options={[
                { value: TaskType.APPLICATION, label: 'Application' },
                { value: TaskType.COMPLAINT, label: 'Complaint' },
                { value: TaskType.DATA_BREACH, label: 'Data Breach' },
                { value: TaskType.EVALUATION, label: 'Evaluation' },
                { value: TaskType.INSPECTION, label: 'Inspection' },
                { value: TaskType.DOCUMENT_REVIEW, label: 'Document Review' },
                { value: TaskType.COMPLIANCE_CHECK, label: 'Compliance Check' },
                { value: TaskType.FOLLOW_UP, label: 'Follow Up' },
              ]}
              error={errors.task_type}
            />
          </div>

          {/* Priority */}
          <div>
            <Select
              label="Priority"
              value={formData.priority}
              onChange={(value) => handleInputChange('priority', value)}
              options={[
                { value: TaskPriority.LOW, label: 'Low' },
                { value: TaskPriority.MEDIUM, label: 'Medium' },
                { value: TaskPriority.HIGH, label: 'High' },
                { value: TaskPriority.URGENT, label: 'Urgent' },
              ]}
            />
          </div>

          {/* Status */}
          <div>
            <Select
              label="Status"
              value={formData.status}
              onChange={(value) => handleInputChange('status', value)}
              options={[
                { value: TaskStatus.PENDING, label: 'Pending' },
                { value: TaskStatus.IN_PROGRESS, label: 'In Progress' },
                { value: TaskStatus.COMPLETED, label: 'Completed' },
                { value: TaskStatus.CANCELLED, label: 'Cancelled' },
                { value: TaskStatus.ON_HOLD, label: 'On Hold' },
              ]}
            />
          </div>

          {/* Assigned To */}
          {users.length > 0 && (
            <div>
              <Select
                label="Assigned To"
                value={formData.assigned_to}
                onChange={(value) => handleInputChange('assigned_to', value)}
                options={[
                  { value: '', label: 'Unassigned' },
                  ...users.map((user) => ({
                    value: user.user_id,
                    label: `${user.first_name} ${user.last_name}`
                  }))
                ]}
              />
            </div>
          )}

          {/* Due Date */}
          <div>
            <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Due Date
            </label>
            <input
              type="date"
              id="due_date"
              value={formData.due_date}
              onChange={(e) => handleInputChange('due_date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
            />
          </div>


        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            rows={4}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="Enter task description"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description}</p>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleClose}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed dark:focus:ring-offset-gray-900"
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {task ? 'Updating...' : 'Creating...'}
              </div>
            ) : (
              task ? 'Update Task' : 'Create Task'
            )}
          </button>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskModal;
