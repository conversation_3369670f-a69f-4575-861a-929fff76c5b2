'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';

interface PremisesRequirement {
  id: string;
  title: string;
  description: string;
  category: string;
  requirements: string[];
  icon: string;
  color: string;
  bgColor: string;
}

const PremisesPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const premisesRequirements: PremisesRequirement[] = [
    {
      id: 'telecom-facilities',
      title: 'Telecommunications Facilities',
      description: 'Requirements for telecommunications infrastructure and equipment housing',
      category: 'telecommunications',
      icon: 'ri-building-line',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      requirements: [
        'Minimum floor area of 100 square meters for main equipment',
        'Adequate ventilation and air conditioning systems',
        'Backup power supply (UPS and generator)',
        'Fire suppression and security systems',
        'Proper grounding and lightning protection',
        'Controlled access and 24/7 monitoring capabilities',
        'Compliance with building codes and safety standards'
      ]
    },
    {
      id: 'data-centers',
      title: 'Data Center Requirements',
      description: 'Standards for data center facilities and server hosting environments',
      category: 'data-center',
      icon: 'ri-server-line',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      requirements: [
        'Tier 2 or higher data center certification',
        'Redundant power and cooling systems',
        'Environmental monitoring (temperature, humidity)',
        'Physical security with biometric access',
        'Network connectivity with multiple ISPs',
        'Disaster recovery and backup facilities',
        'Compliance with international data center standards'
      ]
    },
    {
      id: 'broadcasting',
      title: 'Broadcasting Stations',
      description: 'Requirements for radio and television broadcasting facilities',
      category: 'broadcasting',
      icon: 'ri-radio-line',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      requirements: [
        'Studio facilities with professional equipment',
        'Transmission equipment and antenna systems',
        'Emergency broadcasting capabilities',
        'Content storage and archival systems',
        'Compliance with broadcasting technical standards',
        'Backup transmission facilities',
        'Staff facilities and operational areas'
      ]
    },
    {
      id: 'postal-facilities',
      title: 'Postal Service Facilities',
      description: 'Requirements for postal and courier service premises',
      category: 'postal',
      icon: 'ri-mail-line',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      requirements: [
        'Customer service areas with adequate space',
        'Secure mail sorting and processing areas',
        'Storage facilities for packages and mail',
        'Vehicle access and loading/unloading areas',
        'Security systems for valuable items',
        'Accessibility compliance for disabled customers',
        'Proper signage and identification'
      ]
    },
    {
      id: 'mobile-towers',
      title: 'Mobile Tower Sites',
      description: 'Requirements for cellular tower installations and base stations',
      category: 'mobile',
      icon: 'ri-signal-tower-line',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      requirements: [
        'Structural engineering certification',
        'Environmental impact assessment',
        'Aviation clearance for tower height',
        'RF emission compliance testing',
        'Site security and fencing',
        'Access roads and maintenance facilities',
        'Community consultation and approval'
      ]
    },
    {
      id: 'internet-cafes',
      title: 'Internet Cafes & Public Access',
      description: 'Requirements for public internet access facilities',
      category: 'public-access',
      icon: 'ri-computer-line',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      requirements: [
        'Minimum 20 computer workstations',
        'High-speed internet connectivity',
        'Content filtering and monitoring systems',
        'Customer registration and logging systems',
        'Adequate lighting and ventilation',
        'Fire safety and emergency exits',
        'Compliance with cybersecurity regulations'
      ]
    }
  ];

  const categories = [
    { id: 'all', name: 'All Categories', count: premisesRequirements.length },
    { id: 'telecommunications', name: 'Telecommunications', count: premisesRequirements.filter(r => r.category === 'telecommunications').length },
    { id: 'data-center', name: 'Data Centers', count: premisesRequirements.filter(r => r.category === 'data-center').length },
    { id: 'broadcasting', name: 'Broadcasting', count: premisesRequirements.filter(r => r.category === 'broadcasting').length },
    { id: 'postal', name: 'Postal Services', count: premisesRequirements.filter(r => r.category === 'postal').length },
    { id: 'mobile', name: 'Mobile Infrastructure', count: premisesRequirements.filter(r => r.category === 'mobile').length },
    { id: 'public-access', name: 'Public Access', count: premisesRequirements.filter(r => r.category === 'public-access').length }
  ];

  const filteredRequirements = premisesRequirements.filter(requirement => {
    const matchesCategory = selectedCategory === 'all' || requirement.category === selectedCategory;
    const matchesSearch = requirement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         requirement.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         requirement.requirements.some(req => req.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-blue-600">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <Link href="/customer/resources" className="hover:text-blue-600">Resources</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Premises</span>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Premises Requirements
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Facility standards and requirements for telecommunications and related services.
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex space-x-3">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                <i className="ri-download-line mr-2"></i>
                Download Guidelines
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                <i className="ri-add-line mr-2"></i>
                Submit Application
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400"></i>
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search requirements..."
              />
            </div>
          </div>
          <div className="sm:w-64">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name} ({category.count})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Requirements Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {filteredRequirements.map((requirement) => (
            <div
              key={requirement.id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200"
            >
              {/* Header */}
              <div className="flex items-start space-x-4 mb-4">
                <div className={`flex-shrink-0 w-12 h-12 rounded-lg ${requirement.bgColor} dark:bg-gray-700 flex items-center justify-center`}>
                  <i className={`${requirement.icon} text-2xl ${requirement.color} dark:text-gray-300`}></i>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                    {requirement.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {requirement.description}
                  </p>
                </div>
              </div>

              {/* Requirements List */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                  Key Requirements:
                </h4>
                <ul className="space-y-2">
                  {requirement.requirements.map((req, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400">
                      <i className="ri-check-line text-green-500 mt-0.5 flex-shrink-0"></i>
                      <span>{req}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action Button */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                  <i className="ri-file-text-line mr-2"></i>
                  View Detailed Guidelines
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredRequirements.length === 0 && (
          <div className="text-center py-12">
            <i className="ri-building-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No requirements found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search terms or category filter.
            </p>
          </div>
        )}

        {/* Additional Resources */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-200 dark:border-gray-600">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <i className="ri-question-line text-2xl text-blue-600 dark:text-blue-400"></i>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Need Assistance?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Our technical team can help you understand premises requirements and compliance standards.
                </p>
                <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
                  <i className="ri-phone-line mr-2"></i>
                  Contact Technical Support
                </button>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-green-200 dark:border-gray-600">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <i className="ri-calendar-line text-2xl text-green-600 dark:text-green-400"></i>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Site Inspection
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Schedule a premises inspection to ensure compliance with MACRA standards.
                </p>
                <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors duration-200">
                  <i className="ri-calendar-check-line mr-2"></i>
                  Schedule Inspection
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default PremisesPage;
