import { User } from "./user";

export interface CreateLicenseTypeDto {
  name: string;
  description: string;
}

export interface UpdateLicenseTypeDto {
  name?: string;
  description?: string;
}

export interface LicenseTypeFilters {
  search?: string;
  status?: string;
  created_by?: string;
}

// Types
export interface LicenseType {
  license_type_id: string;
  name: string;
  code?: string;
  description: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  creator?: User;
  updater?: User;
}

export interface NavigationItem {
  license_type_id: string;
  name: string;
  code: string;
  href: string;
  label: string;
  roles: string[];
}

export interface CreateLicenseTypeDto {
  name: string;
  code: string;
  description: string;
}

export interface UpdateLicenseTypeDto {
  name?: string;
  code?: string;
  description?: string;
}