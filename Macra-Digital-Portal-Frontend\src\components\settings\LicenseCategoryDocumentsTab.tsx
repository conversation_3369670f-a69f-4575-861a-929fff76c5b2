'use client';

import { useState, useEffect } from 'react';
import { licenseCategoryDocumentService, LicenseCategoryDocumentsResponse } from '../../services/licenseCategoryDocumentService';
import { LicenseCategoryDocument, PaginateQuery } from '@/types';
import DataTable from '../common/DataTable';
import { formatDate } from '../../utils/formatters';

interface LicenseCategoryDocumentsTabProps {
  onEditLicenseCategoryDocument: (document: LicenseCategoryDocument) => void;
  onCreateLicenseCategoryDocument: () => void;
  refreshTrigger?: number;
}

const LicenseCategoryDocumentsTab = ({ onEditLicenseCategoryDocument, onCreateLicenseCategoryDocument, refreshTrigger }: LicenseCategoryDocumentsTabProps) => {
  const [documentsData, setDocumentsData] = useState<LicenseCategoryDocumentsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadDocuments({ page: 1, limit: 10 });
  }, [refreshTrigger]);

  const loadDocuments = async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError('');
      const response = await licenseCategoryDocumentService.getLicenseCategoryDocuments(query);
      setDocumentsData(response);
    } catch (err: any) {
      console.error('Error loading license category documents:', err);
      setError(err.response?.data?.message || 'Failed to load license category documents');
      // Set empty data structure to prevent undefined errors
      setDocumentsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (document: LicenseCategoryDocument) => {
    const confirmMessage = `Are you sure you want to delete the document requirement "${document.name}"?\n\nThis action cannot be undone and may affect license applications for the "${document.license_category?.name}" category.`;
    
    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      await licenseCategoryDocumentService.deleteLicenseCategoryDocument(document.license_category_document_id);
      await loadDocuments({ page: 1, limit: 10 }); // Reload the list
    } catch (err: any) {
      console.error('Error deleting license category document:', err);
      const errorMessage = err.response?.data?.message || 'Failed to delete license category document';

      // Check if it's a constraint error (related records exist)
      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {
        setError('Cannot delete this document requirement because it is being used by one or more license applications. Please remove or update the related applications first.');
      } else {
        setError(errorMessage);
      }
    }
  };

  // Define columns for DataTable
  const documentColumns = [
    {
      key: 'name',
      label: 'Document Name',
      sortable: true,
      searchable: true,
      render: (value: unknown, item: LicenseCategoryDocument) => (
        <div className="text-sm font-medium text-gray-900 dark:text-white">
          {item.name}
        </div>
      ),
    },
    {
      key: 'is_required',
      label: 'Required',
      sortable: true,
      render: (value: unknown, item: LicenseCategoryDocument) => (
        <div className="text-sm">
          {item.is_required ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              Required
            </span>
          ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400">
              Optional
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'license_category',
      label: 'License Category',
      sortable: false,
      render: (value: unknown, item: LicenseCategoryDocument) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {item.license_category?.name || 'N/A'}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: unknown, item: LicenseCategoryDocument) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatDate(item.created_at)}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      render: (value: unknown, item: LicenseCategoryDocument) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditLicenseCategoryDocument(item)}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            title="Edit document requirement"
          >
            <i className="ri-edit-line text-lg"></i>
          </button>
          <button
            onClick={() => handleDelete(item)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Delete document requirement"
          >
            <i className="ri-delete-bin-line text-lg"></i>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">License Category Documents</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage document requirements for license categories
          </p>
        </div>
        <button
          onClick={onCreateLicenseCategoryDocument}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Document Requirement
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Documents Table */}
      <DataTable
        columns={documentColumns as any}
        data={documentsData as any}
        loading={loading}
        onQueryChange={(query) => {
          loadDocuments({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search document requirements by name..."
        emptyStateMessage="No document requirements found"
        emptyStateIcon="ri-file-list-3-line"
      />
    </div>
  );
};

export default LicenseCategoryDocumentsTab;
