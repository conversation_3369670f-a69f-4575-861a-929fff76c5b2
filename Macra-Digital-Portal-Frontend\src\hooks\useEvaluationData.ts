import { useState, useEffect } from 'react';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { contactPersonService } from '@/services/contactPersonService';
import { stakeholderService } from '@/services/stakeholderService';
import { scopeOfServiceService } from '@/services/scopeOfServiceService';
import { professionalServicesService } from '@/services/professionalServicesService';
import { legalHistoryService } from '@/services/legalHistoryService';
import { DocumentData, documentService } from '@/services/documentService';
import { addressService } from './useAddressing';

interface EvaluationData {
  application: any;
  applicant: any;
  addresses: any[];
  contactPersons: any[];
  stakeholders: any[];
  scopeOfServices: any[];
  professionalServices: any[];
  legalHistory: any[];
  documents: any[];
}

interface UseEvaluationDataReturn {
  data: EvaluationData;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useEvaluationData = (applicationId: string | null): UseEvaluationDataReturn => {
  const [data, setData] = useState<EvaluationData>({
    application: null,
    applicant: null,
    addresses: [],
    contactPersons: [],
    stakeholders: [],
    scopeOfServices: [],
    professionalServices: [],
    legalHistory: [],
    documents: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!applicationId) {
      setError('Application ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading evaluation data for application:', applicationId);

      // Load application details first
      const application = await applicationService.getApplication(applicationId);
      
      const newData: EvaluationData = {
        application,
        applicant: null,
        addresses: [],
        contactPersons: [],
        stakeholders: [],
        scopeOfServices: [],
        professionalServices: [],
        legalHistory: [],
        documents: []
      };

      // Load applicant data if available
      if (application.applicant_id) {
        try {
          const applicant = await applicantService.getApplicant(application.applicant_id);
          newData.applicant = applicant;

          // Load addresses for the applicant
          try {
            const addressResponse = await addressService.getAddressesByEntity('applicant', application.applicant_id);
            // Handle different response structures - extract the actual array
            newData.addresses = addressResponse?.data || addressResponse || [];
          } catch (err) {
            console.warn('Could not load addresses:', err);
            newData.addresses = [];
          }

          // Load contact persons
          try {
            const contactResponse = await contactPersonService.getContactPersonsByApplication(applicationId);
            // Handle different response structures - extract the actual array
            newData.contactPersons = contactResponse?.data || contactResponse || [];
          } catch (err) {
            console.warn('Could not load contact persons:', err);
            newData.contactPersons = [];
          }

          // Load stakeholders
          try {
            const stakeholderResponse = await stakeholderService.getStakeholdersByApplication(applicationId);
            // Handle different response structures - extract the actual array
            newData.stakeholders = stakeholderResponse?.data || stakeholderResponse || [];
          } catch (err) {
            console.warn('Could not load stakeholders:', err);
            newData.stakeholders = [];
          }

          // Load scope of services
          try {
            const scopeOfServices = await scopeOfServiceService.getScopeOfServiceByApplication(applicationId);
            newData.scopeOfServices = scopeOfServices ? [scopeOfServices] : [];
          } catch (err: any) {
            console.warn('Could not load scope of services:', err);
            // Log more details for 500 errors to help with debugging
            if (err.response?.status === 500) {
              console.error('Scope of service 500 error details:', {
                applicationId,
                error: err.response?.data || err.message,
                url: err.config?.url
              });
            }
          }

          // Load professional services
          try {
            const professionalServices = await professionalServicesService.getProfessionalServicesByApplication(applicationId);
            newData.professionalServices = professionalServices ? [professionalServices] : [];
          } catch (err: any) {
            console.warn('Could not load professional services:', err);
            // Enhanced error logging for debugging
            console.error('Professional services error details:', {
              applicationId,
              status: err.response?.status,
              statusText: err.response?.statusText,
              data: err.response?.data,
              message: err.message,
              code: err.code,
              name: err.name,
              url: err.config?.url,
              baseURL: err.config?.baseURL
            });
            // Set empty array to prevent undefined errors
            newData.professionalServices = [];
          }

          // Load legal history
          try {
            const legalHistory = await legalHistoryService.getLegalHistoryByApplication(applicationId);
            newData.legalHistory = legalHistory ? [legalHistory] : [];
          } catch (err) {
            console.warn('Could not load legal history:', err);
          }
          
          let documents: DocumentData[] = [];
          // Load documents
          try {
            // Try primary method first
            try {
              if (!applicationId) {
                throw new Error('Application ID is required for loading documents');
              }

              const response = await documentService.getDocumentsByApplication(applicationId);
              documents = response.data;
              console.log(`✅ Primary method: Loaded ${documents?.length || 0} documents`);
            } catch (primaryErr: any) {
              console.warn('Primary document loading method failed:', primaryErr?.message);

              // Try alternative method
              try {
                if (!applicationId) {
                  throw new Error('Application ID is required for loading documents');
                }

                const altResponse = await documentService.getDocumentsByEntity('application', applicationId);
                documents = altResponse.data;
                console.log(`✅ Alternative method: Loaded ${documents?.length || 0} documents`);
              } catch (altErr: any) {
                console.error('Alternative document loading method also failed:', altErr?.message);

                // Log more details for 500 errors
                if (altErr?.response?.status === 500) {
                  console.error('Document loading 500 error details:', {
                    applicationId,
                    error: altErr.response?.data || altErr.message,
                    url: altErr.config?.url
                  });
                }
              }
            }
            newData.documents = documents || [];
          } catch (err: any) {
            console.error('Document loading outer error:', err?.message);
            newData.documents = [];
          }

        } catch (applicantError) {
        }
      }

      setData(newData);
    } catch (err: any) {
      console.error('❌ Error loading evaluation data:', err);
      setError(err.message || 'Failed to load evaluation data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [applicationId]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
};
