# Multiple Address Types Implementation

## Overview

Modified the address-info/page.tsx to support different address types (business, postal, physical, billing, shipping) that can be submitted under one applicant, allowing comprehensive address management for various purposes.

## Key Changes

### 1. **Enhanced State Management**

**Before (Single Address):**
```typescript
const [existingAddress, setExistingAddress] = useState<Address | null>(null);
const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
```

**After (Multiple Address Types):**
```typescript
const [selectedAddressType, setSelectedAddressType] = useState<AddressType>(ADDRESS_TYPES.BUSINESS);
// Define supported address types for this application
type SupportedAddressType = typeof ADDRESS_TYPES.BUSINESS | typeof ADDRESS_TYPES.POSTAL;

const [addressesByType, setAddressesByType] = useState<Record<SupportedAddressType, Address[]>>({
  [ADDRESS_TYPES.BUSINESS]: [],
  [ADDRESS_TYPES.POSTAL]: [],
});
const [selectedAddressIds, setSelectedAddressIds] = useState<Record<SupportedAddressType, string | null>>({
  [ADDRESS_TYPES.BUSINESS]: null,
  [ADDRESS_TYPES.POSTAL]: null,
});
```

### 2. **Address Type Selector UI**

Added a prominent address type selector at the top of the form:

```typescript
<div className="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
    <i className="ri-building-line mr-2"></i>
    Address Management
  </h3>
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Address Type
      </label>
      <select
        value={selectedAddressType}
        onChange={(e) => setSelectedAddressType(e.target.value as AddressType)}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
      >
        <option value={ADDRESS_TYPES.BUSINESS}>Business Address</option>
        <option value={ADDRESS_TYPES.POSTAL}>Postal Address</option>
      </select>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Status
      </label>
      <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {getCurrentAddresses().length > 0 
            ? `${getCurrentAddresses().length} address${getCurrentAddresses().length > 1 ? 'es' : ''} available`
            : 'No addresses yet'
          }
        </span>
      </div>
    </div>
  </div>
</div>
```

### 3. **Helper Functions for Current Address Type**

Added utility functions to work with the currently selected address type:

```typescript
// Address management functions for current address type
const getCurrentAddresses = () => addressesByType[selectedAddressType] || [];
const getCurrentSelectedAddressId = () => selectedAddressIds[selectedAddressType];
const getCurrentSelectedAddress = () => {
  const addressId = getCurrentSelectedAddressId();
  return addressId ? getCurrentAddresses().find(addr => addr.address_id === addressId) : null;
};
```

### 4. **Enhanced Data Loading**

Updated the data loading logic to organize addresses by type:

```typescript
// Organize addresses by supported types only
const organizedAddresses: Record<SupportedAddressType, Address[]> = {
  [ADDRESS_TYPES.BUSINESS]: [],
  [ADDRESS_TYPES.POSTAL]: [],
};

const selectedIds: Record<SupportedAddressType, string | null> = {
  [ADDRESS_TYPES.BUSINESS]: null,
  [ADDRESS_TYPES.POSTAL]: null,
};

// Categorize addresses by supported types only
addressesResponse.data.forEach((address: Address) => {
  const addressType = address.address_type as SupportedAddressType;
  // Only include supported address types
  if (addressType && organizedAddresses[addressType] !== undefined) {
    organizedAddresses[addressType].push(address);
    // Select the first address of each type by default
    if (!selectedIds[addressType]) {
      selectedIds[addressType] = address.address_id;
    }
  }
});

setAddressesByType(organizedAddresses);
setSelectedAddressIds(selectedIds);
```

### 5. **Dynamic UI Updates**

All UI sections now adapt to the selected address type:

**Form Title:**
```typescript
<h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
  <i className="ri-route-line mr-2"></i>
  {selectedAddressType.charAt(0).toUpperCase() + selectedAddressType.slice(1)} Address Information
</h3>
```

**Address Selection:**
```typescript
<h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-2">
  <i className="ri-building-line mr-2"></i>
  Existing {selectedAddressType.charAt(0).toUpperCase() + selectedAddressType.slice(1)} Addresses
</h3>
```

**Selected Address Display:**
```typescript
<h3 className="text-lg font-medium text-green-800 dark:text-green-300">
  <i className="ri-check-circle-line mr-2"></i>
  Selected {selectedAddressType.charAt(0).toUpperCase() + selectedAddressType.slice(1)} Address
</h3>
```

### 6. **Enhanced Save Logic**

Updated the save function to work with the current address type:

```typescript
const addressData = {
  address_type: selectedAddressType, // Use current selected address type
  entity_type: 'applicant',
  entity_id: application.applicant_id,
  // ... other fields
};

const currentSelectedAddress = getCurrentSelectedAddress();

if (currentSelectedAddress) {
  // Update existing address
  const updateData = {
    address_id: currentSelectedAddress.address_id,
    ...addressData
  };
  const response = await editAddress(updateData);
  address = response.data || response;
} else {
  // Create new address
  const response = await createAddress(addressData);
  address = response.data || response;
  
  // Update state to track the newly created address
  setAddressesByType(prev => ({
    ...prev,
    [selectedAddressType]: [...prev[selectedAddressType], address]
  }));
  setSelectedAddressIds(prev => ({
    ...prev,
    [selectedAddressType]: address.address_id
  }));
}
```

## Address Types Supported

### **Available Types:**
- **Business Address**: Primary business location for license applications
- **Postal Address**: Mailing and correspondence address

### **Type-Specific Features:**
- Each type maintains its own list of addresses
- Independent selection for each type
- Type-specific validation and requirements
- Contextual UI labels and messaging
- **Empty Form Initialization**: Forms always start empty to prevent accidental data carryover

## User Experience Flow

### **1. Address Type Selection**
1. User selects desired address type from dropdown
2. UI updates to show addresses for that type
3. Status indicator shows count of addresses for selected type

### **2. Address Management per Type**
1. **No Addresses**: Shows unified form to create new address
2. **Existing Addresses**: Shows selection dropdown with existing addresses
3. **Selected Address**: Shows address details with edit/deselect options

### **3. Cross-Type Navigation**
1. User can switch between address types seamlessly
2. Each type maintains its own state and selection
3. Form data updates based on selected type and address

### **4. Saving Addresses**
1. Addresses are saved with the correct type
2. State is updated to reflect new/updated addresses
3. Selection is maintained for the current type

### **5. Form Initialization**
1. **Always Empty**: Forms initialize with empty values to prevent data confusion
2. **No Auto-Population**: Existing addresses don't automatically populate the form
3. **Explicit Selection**: Users must explicitly choose to edit existing addresses
4. **Clean State**: Each new address creation starts with a clean slate

## Benefits

### ✅ **Comprehensive Address Management**
- **Multiple Types**: Support for all common address types
- **Independent Management**: Each type managed separately
- **Flexible Usage**: Addresses can be reused across applications

### ✅ **Enhanced User Experience**
- **Clear Organization**: Type-based organization is intuitive
- **Visual Feedback**: Status indicators and contextual messaging
- **Seamless Navigation**: Easy switching between address types

### ✅ **Data Integrity**
- **Type Safety**: Proper TypeScript typing for all address types
- **Validation**: Type-specific validation and requirements
- **Consistency**: Standardized data structure across types

### ✅ **Scalability**
- **Extensible**: Easy to add new address types
- **Maintainable**: Clean separation of concerns
- **Reusable**: Address data can be shared across applications

## Technical Implementation

### **State Structure:**
```typescript
interface AddressState {
  selectedAddressType: AddressType;
  addressesByType: Record<AddressType, Address[]>;
  selectedAddressIds: Record<AddressType, string | null>;
}
```

### **Helper Functions:**
```typescript
const getCurrentAddresses = () => addressesByType[selectedAddressType] || [];
const getCurrentSelectedAddressId = () => selectedAddressIds[selectedAddressType];
const getCurrentSelectedAddress = () => { /* ... */ };
```

### **API Integration:**
- Addresses saved with `address_type` field
- Polymorphic relationship: `entity_type: 'applicant'`, `entity_id: applicant_id`
- Type-based filtering and organization on load

The implementation provides a comprehensive solution for managing multiple address types under a single applicant, enhancing the application's flexibility and user experience while maintaining data integrity and type safety.
