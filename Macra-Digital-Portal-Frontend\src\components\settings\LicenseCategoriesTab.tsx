'use client';

import { useState, useEffect } from 'react';
import {  licenseCategoryService, LicenseCategoriesResponse } from '../../services/licenseCategoryService';
import { formatCurrency, formatDate, formatLicenseCategoryFee } from '../../utils/formatters';
import DataTable from '../common/DataTable';
import { LicenseCategory, PaginateQuery } from '@/types';

interface LicenseCategoriesTabProps {
  onEditLicenseCategory: (licenseCategory: LicenseCategory) => void;
  onCreateLicenseCategory: () => void;
  refreshTrigger?: number;
}

const LicenseCategoriesTab = ({ onEditLicenseCategory, onCreateLicenseCategory, refreshTrigger }: LicenseCategoriesTabProps) => {
  const [licenseCategoriesData, setLicenseCategoriesData] = useState<LicenseCategoriesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadLicenseCategories({ page: 1, limit: 10 });
  }, [refreshTrigger]);

  const loadLicenseCategories = async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError('');
      const response = await licenseCategoryService.getLicenseCategories(query);
      setLicenseCategoriesData(response);
    } catch (err: any) {
      console.error('Error loading license categories:', err);
      setError(err.response?.data?.message || 'Failed to load license categories');
      // Set empty data structure to prevent undefined errors
      setLicenseCategoriesData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (licenseCategory: LicenseCategory) => {
    const confirmMessage = `Are you sure you want to delete the license category "${licenseCategory.name}"?\n\nThis action cannot be undone and may affect users who have licenses of this category.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      await licenseCategoryService.deleteLicenseCategory(licenseCategory.license_category_id);
      await loadLicenseCategories({ page: 1, limit: 10 }); // Reload the list
    } catch (err: any) {
      console.error('Error deleting license category:', err);
      const errorMessage = err.response?.data?.message || 'Failed to delete license category';

      // Check if it's a constraint error (related records exist)
      if (err.response?.status === 409 || errorMessage.includes('constraint') || errorMessage.includes('foreign key')) {
        setError('Cannot delete this license category because it is being used by one or more user licenses. Please remove or reassign the related licenses first.');
      } else {
        setError(errorMessage);
      }
    }
  };

  // Define columns for DataTable
  const licenseCategoryColumns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      searchable: true,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="text-sm font-medium text-gray-900 dark:text-white">
          {item.name}
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      searchable: true,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
          {item.description}
        </div>
      ),
    },
    {
      key: 'authorizes',
      label: 'Authorizes',
      sortable: false,
      searchable: true,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
          {item.authorizes || 'N/A'}
        </div>
      ),
    },
    {
      key: 'application_fee',
      label: 'Application Fee',
      sortable: true,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="text-sm text-gray-900 dark:text-white">
          {formatLicenseCategoryFee(item?.fee?? 0 ,item.name,)}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatDate(item.created_at)}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      render: (value: unknown, item: LicenseCategory) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditLicenseCategory(item)}
            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            title="Edit license category"
          >
            <i className="ri-edit-line text-lg"></i>
          </button>
          <button
            onClick={() => handleDelete(item)}
            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
            title="Delete license category"
          >
            <i className="ri-delete-bin-line text-lg"></i>
          </button>
        </div>
      ),
    },
  ];



  return (
    <div className="space-y-6">
      {/* Header with Search and Add Button */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">License Categories</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage license categories, fees, and authorizations
          </p>
        </div>
        <button
          onClick={onCreateLicenseCategory}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
        >
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add License Category
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* License Categories Table */}
      <DataTable
        columns={licenseCategoryColumns as any}
        data={licenseCategoriesData as any}
        loading={loading}
        onQueryChange={(query) => {
          loadLicenseCategories({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search license categories by name, description, or authorization..."
        emptyStateMessage="No license categories found"
        emptyStateIcon="ri-price-tag-3-line"
      />
    </div>
  );
};

export default LicenseCategoriesTab;
