'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';
import PaymentTabs from '@/components/customer/payments/PaymentTabs';
import InvoicesTab from '@/components/customer/payments/InvoicesTab';
import PaymentsTab from '@/components/customer/payments/PaymentsTab';
import InvoiceDetailsModal from '@/components/customer/payments/InvoiceDetailsModal';
import UploadPaymentModal from '@/components/customer/payments/UploadPaymentModal';
import { paymentService } from '@/services/paymentService';
import { PaymentStatistics } from '@/types/invoice';
import DashboardLayout from '../dashboard/layout';

const PaymentsPage = () => {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('invoices');
  const [isLoading, setIsLoading] = useState(true);
  const [statistics, setStatistics] = useState<PaymentStatistics | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  // Helper function to check if user is a customer
  const isCustomerUser = () => {
    if (!user || !user.roles) return true;
    const roles = user.roles || [];
    return roles.includes('customer') ||
           (!roles.includes('admin') &&
            !roles.includes('administrator') &&
            !roles.includes('staff') &&
            !roles.includes('officer') &&
            !roles.includes('finance'));
  };

  // Modal states
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [showInvoiceDetails, setShowInvoiceDetails] = useState(false);
  const [showPaymentTransactions, setShowPaymentTransactions] = useState(false);
  const [showUploadPayment, setShowUploadPayment] = useState(false);
  const [showViewPayments, setShowViewPayments] = useState(false);

  const loadStatistics = async () => {
    try {
      setStatsLoading(true);
      // Add timeout to prevent infinite loading
      const statsPromise = paymentService.getPaymentStatistics();
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Statistics loading timeout')), 20000)
      );

      const stats = await Promise.race([statsPromise, timeoutPromise]);
      setStatistics(stats);
    } catch (error) {
      setStatistics({
        total: 0,
        pending: 0,
        paid: 0,
        overdue: 0,
        cancelled: 0,
        totalAmount: 0,
        pendingAmount: 0,
        paidAmount: 0,
        overdueAmount: 0,
      });
    } finally {
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    const initializePage = async () => {
      try {
        // Load statistics with timeout
        const initPromise = loadStatistics();
        const timeoutPromise = new Promise<void>((resolve) =>
          setTimeout(() => {
            console.warn('⚠️ Page initialization timeout, proceeding anyway');
            resolve();
          }, 25000)
        );

        await Promise.race([initPromise, timeoutPromise]);
      } catch (error) {
        console.error('❌ Failed to initialize payments page:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializePage();
  }, [isAuthenticated, router]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowInvoiceDetails(true);
  };

  const handlePayInvoice = (invoice: any) => {
    // TODO: Implement payment flow - redirect to payment gateway or show payment options
  };

  const handleViewPaymentsDone = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowViewPayments(true);
  };

  const handleUploadPayment = (invoice: any) => {
    setSelectedInvoice(invoice);
    setShowUploadPayment(true);
  };

  const handleCloseModals = () => {
    setSelectedInvoice(null);
    setShowInvoiceDetails(false);
    setShowPaymentTransactions(false);
    setShowUploadPayment(false);
    setShowViewPayments(false);
  };

  const handleUploadSuccess = () => {
    // Refresh statistics after successful upload
    loadStatistics();
  };

  const handleViewPayment = (payment: any) => {
    console.log('View payment:', payment);
    // TODO: Implement payment view modal or navigation
  };

  const handleUploadProof = (payment: any) => {
    console.log('Upload proof for payment:', payment);
    // TODO: Implement proof of payment upload modal
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Define tabs for the tab system
  const tabs = [
    {
      id: 'invoices',
      label: 'Invoices',
      icon: 'ri-file-list-3-line',
      content: (
        <InvoicesTab
          onViewInvoice={handleViewInvoice}
          onPayInvoice={handlePayInvoice}
          onViewPaymentsDone={handleViewPaymentsDone}
          onUploadPayment={handleUploadPayment}
        />
      )
    },
    {
      id: 'payments',
      label: 'Payment History',
      icon: 'ri-money-dollar-circle-line',
      content: (
        <PaymentsTab
          onViewPayment={handleViewPayment}
          onUploadProof={handleUploadProof}
        />
      )
    }
  ];

  const isCustomer = isCustomerUser();
  const pageTitle = isCustomer ? 'My Payments' : 'Payment Management';

  if (isLoading) {
    return (
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading payments..." />
        </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{pageTitle}</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isCustomer
                  ? 'Manage your invoices and payment history'
                  : 'Manage all invoices and payments across the system'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Admin/Staff Notice */}
        {!isCustomer && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <i className="ri-shield-user-line text-blue-600 dark:text-blue-400 text-xl"></i>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Administrative View
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  You are viewing all payments and invoices across the system. This includes data from all customers and applications.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Payment Statistics */}
        {!statsLoading && statistics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <i className="ri-file-list-3-line text-white text-lg"></i>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Total Invoices
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {statistics.total}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <i className="ri-time-line text-white text-lg"></i>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Pending
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {statistics.pending}
                      </dd>
                      <dd className="text-xs text-gray-500 dark:text-gray-400">
                        {formatCurrency(statistics.pendingAmount)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <i className="ri-check-line text-white text-lg"></i>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Paid
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {statistics.paid}
                      </dd>
                      <dd className="text-xs text-gray-500 dark:text-gray-400">
                        {formatCurrency(statistics.paidAmount)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                      <i className="ri-error-warning-line text-white text-lg"></i>
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                        Overdue
                      </dt>
                      <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {statistics.overdue}
                      </dd>
                      <dd className="text-xs text-gray-500 dark:text-gray-400">
                        {formatCurrency(statistics.overdueAmount)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {statsLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="animate-pulse">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-md"></div>
                      <div className="ml-5 flex-1">
                        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-2"></div>
                        <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Payment Tabs */}
        <PaymentTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* Modals */}
      <InvoiceDetailsModal
        isOpen={showInvoiceDetails}
        onClose={handleCloseModals}
        invoice={selectedInvoice}
      />

      <UploadPaymentModal
        isOpen={showUploadPayment}
        onClose={handleCloseModals}
        invoice={selectedInvoice}
        onSuccess={handleUploadSuccess}
      />
    </div>
  );
};

export default PaymentsPage;
