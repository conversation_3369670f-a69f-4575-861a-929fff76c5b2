'use client';

import React, { useState } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
import NotificationModal from '@/components/notifications/NotificationModal';
import { useAuth } from '@/contexts/AuthContext';

export default function TestNotificationsPage() {
  const { isAuthenticated, user } = useAuth();
  const {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
  } = useNotifications();
  
  const [modalOpen, setModalOpen] = useState(false);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Please log in to test notifications
          </h1>
          <a
            href="/auth/login"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Notification System Test
          </h1>

          {/* User Info */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Current User
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Email: {user?.email}
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              User ID: {user?.user_id}
            </p>
          </div>

          {/* Notification Stats */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Total Notifications
              </h3>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                {notifications.length}
              </p>
            </div>
            <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Unread Count
              </h3>
              <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                {unreadCount}
              </p>
            </div>
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Status
              </h3>
              <p className="text-sm font-medium text-green-900 dark:text-green-100">
                {loading ? 'Loading...' : error ? 'Error' : 'Ready'}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="mb-6 flex flex-wrap gap-4">
            <button
              onClick={refreshNotifications}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Refresh Notifications'}
            </button>
            <button
              onClick={() => setModalOpen(true)}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Open Notification Modal
            </button>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
              >
                Mark All as Read
              </button>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Error
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          )}

          {/* Notifications List */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Recent Notifications
            </h2>
            {notifications.length === 0 ? (
              <p className="text-gray-600 dark:text-gray-400">
                No notifications found.
              </p>
            ) : (
              <div className="space-y-2">
                {notifications.slice(0, 5).map((notification) => (
                  <div
                    key={notification.notification_id}
                    className={`p-3 border rounded-lg ${
                      notification.is_read
                        ? 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'
                        : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {notification.subject}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                          {new Date(notification.created_at).toLocaleString()}
                        </p>
                      </div>
                      {!notification.is_read && (
                        <button
                          onClick={() => markAsRead(notification.notification_id)}
                          className="ml-4 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        >
                          Mark as read
                        </button>
                      )}
                    </div>
                  </div>
                ))}
                {notifications.length > 5 && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    ... and {notifications.length - 5} more notifications
                  </p>
                )}
              </div>
            )}
          </div>

          {/* API Test Info */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              API Information
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              API URL: {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Endpoint: /notifications/my-notifications
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Auth Token: {localStorage.getItem('auth_token') ? 'Present' : 'Missing'}
            </p>
          </div>
        </div>
      </div>

      {/* Notification Modal */}
      <NotificationModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
      />
    </div>
  );
}
