import React, { useState, useEffect } from 'react';
import { Application } from '@/types/license';
import { DocumentData, documentService } from '@/services/documentService';
import DocumentPreviewModal from '@/components/documents/DocumentPreviewModal';

interface DocumentCardProps {
  application: Application | null;
  className?: string;
  defaultCollapsed?: boolean;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  application,
  className = '',
  defaultCollapsed = false
}) => {
  const [documents, setDocuments] = useState<DocumentData[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [selectedDocument, setSelectedDocument] = useState<DocumentData | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  useEffect(() => {
    const fetchDocuments = async () => {
      if (!application?.application_id) return;

      try {
        setLoading(true);
        let documentData: DocumentData[] = [];

        // Try primary method first
        try {
          const response = await documentService.getDocumentsByApplication(application.application_id);
          documentData = response.data;
        } catch (primaryErr) {
          // Try alternative method
          try {
            const altResponse = await documentService.getDocumentsByEntity('application', application.application_id);
            documentData = altResponse.data;
          } catch (altErr) {
            console.warn('Could not load documents:', altErr);
          }
        }

        setDocuments(documentData || []);
      } catch (err) {
        console.warn('Could not load documents:', err);
        setDocuments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [application?.application_id]);

  // Return empty fragment if no application or no documents
  if (!application || loading || !documents || documents.length === 0) {
    return <></>;
  }

  const handleDocumentDownload = async (documentId: string, fileName: string) => {
    try {
      const blob = await documentService.downloadDocument(documentId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading document:', err);
    }
  };

  const handleDocumentPreview = (document: DocumentData) => {
    // Only preview if document has required fields
    if (document.document_id) {
      setSelectedDocument(document);
      setIsPreviewModalOpen(true);
    }
  };

  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    setSelectedDocument(null);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'ri-file-pdf-line text-red-500';
      case 'doc':
      case 'docx':
        return 'ri-file-word-line text-blue-500';
      case 'xls':
      case 'xlsx':
        return 'ri-file-excel-line text-green-500';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'ri-image-line text-purple-500';
      default:
        return 'ri-file-line text-gray-500';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <i className="ri-folder-line text-xl text-blue-600 dark:text-blue-400 mr-3"></i>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Application Documents
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {documents && documents.length > 0
                    ? `${documents.length} document${documents.length !== 1 ? 's' : ''} uploaded`
                    : 'No documents uploaded'
                  }
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              title={isCollapsed ? 'Expand' : 'Collapse'}
            >
              <i className={`${isCollapsed ? 'ri-add-line' : 'ri-subtract-line'} text-lg text-gray-500 dark:text-gray-400`}></i>
            </button>
          </div>
        </div>

        <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isCollapsed ? 'max-h-0' : 'max-h-none'}`}>
          <div className="p-6">
          {documents && documents.length > 0 ? (
            <div className="grid gap-4">
              {documents.map((document) => (
                <div
                  key={document.document_id}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="flex-shrink-0">
                      <i className={`${getFileIcon(document.file_name)} text-2xl`}></i>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {document.file_name}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          (document as any).is_verified ?
                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {(document as any).is_verified ? 'Verified' : 'Pending'}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <i className="ri-file-line mr-1"></i>
                          {document.file_size ? formatFileSize(document.file_size) : 'Unknown size'}
                        </span>
                        <span className="flex items-center">
                          <i className="ri-calendar-line mr-1"></i>
                          {document.created_at ? new Date(document.created_at).toLocaleDateString() : 'Unknown date'}
                        </span>
                        {document.document_type && (
                          <span className="flex items-center">
                            <i className="ri-price-tag-3-line mr-1"></i>
                            {documentService.formatDocumentType(document.document_type)}
                          </span>
                        )}
                      </div>

                      {document.description && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 line-clamp-2">
                          {document.description}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {/* Preview Button */}
                    {documentService.isPreviewable(document.mime_type) && (
                      <button
                        onClick={() => handleDocumentPreview(document)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30 rounded-md transition-colors"
                        title="Preview document"
                      >
                        <i className="ri-eye-line mr-1"></i>
                        Preview
                      </button>
                    )}

                    {/* Download Button */}
                    <button
                      onClick={() => handleDocumentDownload(document.document_id!, document.file_name)}
                      className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors"
                      title="Download document"
                    >
                      <i className="ri-download-line mr-1"></i>
                      Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <i className="ri-file-search-line text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No Documents Found
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                No documents have been uploaded for this application yet.
              </p>
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Document Preview Modal */}
      {selectedDocument && selectedDocument.document_id && (
        <DocumentPreviewModal
          isOpen={isPreviewModalOpen}
          onClose={handleClosePreview}
          document={selectedDocument as any} // Type assertion since we've checked document_id exists
        />
      )}
    </>
  );
};

export default DocumentCard;
