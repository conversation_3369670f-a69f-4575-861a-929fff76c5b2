'use client';

import React, { useState } from 'react';
import SequentialAddressBuilder from './SequentialAddressBuilder';

interface SequentialAddressData {
  country: string;
  region: string;
  district: string;
  location: string;
  postal_code: string;
  address_line_1: string;
  address_line_2: string;
}

/**
 * Test component to verify the integration of SequentialAddressBuilder
 * in the address-info page context
 */
const AddressInfoIntegrationTest: React.FC = () => {
  const [useSequentialBuilder, setUseSequentialBuilder] = useState(false);
  const [formData, setFormData] = useState({
    address_line_1: '',
    address_line_2: '',
    address_line_3: '',
    city: '',
    postal_code: '',
    country: ''
  });
  const [sequentialData, setSequentialData] = useState<Partial<SequentialAddressData>>({});
  const [isComplete, setIsComplete] = useState(false);

  const toggleAddressMode = () => {
    setUseSequentialBuilder(!useSequentialBuilder);
    if (!useSequentialBuilder) {
      // Switching to sequential mode - populate with current form data
      setSequentialData({
        country: formData.country,
        postal_code: formData.postal_code,
        address_line_1: formData.address_line_1,
        address_line_2: formData.address_line_2,
      });
    }
  };

  const handleSequentialAddressComplete = (address: SequentialAddressData) => {
    setSequentialData(address);
    // Update the traditional form data to match
    setFormData({
      address_line_1: address.address_line_1 || '',
      address_line_2: address.address_line_2 || '',
      address_line_3: '',
      city: address.location || '',
      postal_code: address.postal_code || '',
      country: address.country || ''
    });
    setIsComplete(true);
  };

  const handleSequentialAddressChange = (address: Partial<SequentialAddressData>) => {
    setSequentialData(address);
    // Update form data in real-time
    setFormData(prev => ({
      ...prev,
      address_line_1: address.address_line_1 || prev.address_line_1,
      address_line_2: address.address_line_2 || prev.address_line_2,
      city: address.location || prev.city,
      postal_code: address.postal_code || prev.postal_code,
      country: address.country || prev.country
    }));
  };

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
        Address Info Integration Test
      </h1>

      {/* Address Input Mode Toggle */}
      <div className="mb-8 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Address Input Mode
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
              Choose between guided step-by-step address building or traditional form input
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`text-sm ${!useSequentialBuilder ? 'text-gray-900 dark:text-gray-100 font-medium' : 'text-gray-500 dark:text-gray-400'}`}>
              Traditional
            </span>
            <button
              type="button"
              onClick={toggleAddressMode}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ${
                useSequentialBuilder ? 'bg-red-600' : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  useSequentialBuilder ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm ${useSequentialBuilder ? 'text-gray-900 dark:text-gray-100 font-medium' : 'text-gray-500 dark:text-gray-400'}`}>
              Guided
            </span>
          </div>
        </div>
      </div>

      {/* Sequential Address Builder */}
      {useSequentialBuilder ? (
        <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              <i className="ri-route-line mr-2"></i>
              Guided Address Builder
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Follow the step-by-step process to build your address. For Malawi addresses, 
              we'll guide you through region, district, and location selection.
            </p>
          </div>

          <SequentialAddressBuilder
            onAddressComplete={handleSequentialAddressComplete}
            onAddressChange={handleSequentialAddressChange}
            initialData={sequentialData}
            disabled={false}
          />
        </div>
      ) : (
        /* Traditional Form */
        <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              <i className="ri-edit-line mr-2"></i>
              Traditional Form Input
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Enter your address information using the standard form fields.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Country
              </label>
              <input
                type="text"
                value={formData.country}
                onChange={(e) => handleFormChange('country', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter country"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City
              </label>
              <input
                type="text"
                value={formData.city}
                onChange={(e) => handleFormChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter city"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Postal Code
              </label>
              <input
                type="text"
                value={formData.postal_code}
                onChange={(e) => handleFormChange('postal_code', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter postal code"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Address Line 1
              </label>
              <input
                type="text"
                value={formData.address_line_1}
                onChange={(e) => handleFormChange('address_line_1', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter address line 1"
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Address Line 2
              </label>
              <input
                type="text"
                value={formData.address_line_2}
                onChange={(e) => handleFormChange('address_line_2', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                placeholder="Enter address line 2 (optional)"
              />
            </div>
          </div>
        </div>
      )}

      {/* Current Data Preview */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3">
          <i className="ri-eye-line mr-2"></i>
          Current Form Data
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
          {Object.entries(formData).map(([key, value]) => (
            value && (
              <div key={key} className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-400 capitalize">
                  {key.replace(/_/g, ' ')}:
                </span>
                <span className="text-blue-800 dark:text-blue-200 font-medium">
                  {value}
                </span>
              </div>
            )
          ))}
        </div>
        {isComplete && (
          <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-800">
            <div className="flex items-center text-sm text-green-600 dark:text-green-400">
              <i className="ri-check-circle-line mr-2"></i>
              Address is complete via sequential builder
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressInfoIntegrationTest;
