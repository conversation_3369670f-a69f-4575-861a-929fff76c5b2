import { apiClient } from '@/lib/apiClient';
import { Payment, PaymentFilters } from '@/types/invoice';
import { PaginatedResponse, PaginateQuery } from '@/types';

export interface PaymentUpdateData {
  status?: 'pending' | 'approved' | 'cancelled' | 'overdue';
  paid_date?: string;
  notes?: string;
  payment_method?: string;
  transaction_reference?: string;
}

export interface PaymentApprovalResult {
  success: boolean;
  message: string;
  data?: {
    payment: Payment;
    invoice_fully_paid?: boolean;
    total_paid_amount?: number;
    remaining_amount?: number;
    invoice_amount?: number;
    task_closed?: boolean;
  };
  error?: string;
}

export interface PaymentRejectionResult {
  success: boolean;
  message: string;
  data?: {
    payment: Payment;
  };
  error?: string;
}

class AdminPaymentService {
  /**
   * Get all payments with admin privileges
   */
  async getPayments(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {
    try {
      const params: any = {};

      if (query.page) params.page = query.page;
      if (query.limit) params.limit = query.limit;
      if (query.status) params.status = query.status;
      if (query.payment_type) params.paymentType = query.payment_type;
      if (query.search) params.search = query.search;
      if (query.dateRange) params.dateRange = query.dateRange;

      const response = await apiClient.get('/payments', { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching payments:', error);
      throw new Error('Failed to fetch payments');
    }
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(id: string): Promise<Payment> {
    try {
      const response = await apiClient.get(`/payments/${id}`);
      return response.data;
    } catch (error) {
      console.error(`❌ Error fetching payment ${id}:`, error);
      throw new Error('Payment not found');
    }
  }

  /**
   * Update payment status and details
   */
  async updatePayment(id: string, data: PaymentUpdateData): Promise<PaymentApprovalResult> {
    try {
      const response = await apiClient.put(`/payments/${id}`, data);
      return {
        success: true,
        message: response.data.message || 'Payment updated successfully',
        data: response.data.data ? {
          payment: response.data.data,
          invoice_fully_paid: response.data.data.approval_result?.invoice_fully_paid,
          total_paid_amount: response.data.data.approval_result?.total_paid_amount,
          remaining_amount: response.data.data.approval_result?.remaining_amount,
          invoice_amount: response.data.data.approval_result?.invoice_amount,
          task_closed: response.data.data.approval_result?.task_closed
        } : undefined
      };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to update payment';
      const errorCode = error.response?.data?.error || 'UNKNOWN_ERROR';

      return {
        success: false,
        message: errorMessage,
        error: errorCode
      };
    }
  }

  /**
   * Approve payment - sets status to 'paid' and handles invoice/task closure
   */
  async approvePayment(paymentId: string): Promise<PaymentApprovalResult> {
    try {
      const updateData: PaymentUpdateData = {
        status: 'approved',
        paid_date: new Date().toISOString()
      };

      const result = await this.updatePayment(paymentId, updateData);

      if (result.success && result.data?.invoice_fully_paid) {
        result.message = '✅ Payment approved and invoice marked as fully paid! Client has been notified via email.';
      } else if (result.success && result.data?.remaining_amount) {
        result.message = `Payment approved successfully. Remaining balance: MWK ${result.data.remaining_amount.toLocaleString()}`;
      }

      return result;
    } catch (error) {
      console.error(`❌ Error approving payment ${paymentId}:`, error);
      return {
        success: false,
        message: 'Failed to approve payment',
        error: 'APPROVAL_FAILED'
      };
    }
  }

  /**
   * Reject payment - sets status to 'cancelled' with reason
   */
  async rejectPayment(paymentId: string, reason: string): Promise<PaymentRejectionResult> {
    try {
      console.log(`❌ Rejecting payment ${paymentId} with reason: ${reason}`);

      const updateData: PaymentUpdateData = {
        status: 'cancelled',
        notes: reason.trim()
      };

      const result = await this.updatePayment(paymentId, updateData);

      return {
        success: result.success,
        message: result.message || 'Payment rejected successfully',
        data: result.data ? { payment: result.data.payment } : undefined,
        error: result.error
      };
    } catch (error) {
      console.error(`❌ Error rejecting payment ${paymentId}:`, error);
      return {
        success: false,
        message: 'Failed to reject payment',
        error: 'REJECTION_FAILED'
      };
    }
  }
}

export const adminPaymentService = new AdminPaymentService();
