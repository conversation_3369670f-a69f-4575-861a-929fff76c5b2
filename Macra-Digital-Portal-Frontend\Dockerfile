# 1. Build stage
FROM node:22-alpine3.20 AS builder

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock if you use yarn) into the container
COPY package*.json ./

# Install dependencies, including 'devDependencies'
RUN npm ci

# Copy the rest of the application into the container
COPY . .

# Set environment variables
ENV NEXT_PUBLIC_API_URL="https://mdp-services.dev.softwarehouse.global"

# Build the Next.js application
RUN npm run build

# 2. Runtime stage
FROM node:22-alpine3.20 AS runner

# Working directory
WORKDIR /app

# Add system packages only in the small runtime layer
RUN apk add --no-cache tzdata

# Copy artefacts from builder
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/public ./public

# Remove devDependencies
RUN npm prune --production

# Expose the port that Next.js usually runs on
EXPOSE 3000

# Use a command to start the production server
CMD ["npm", "start"]