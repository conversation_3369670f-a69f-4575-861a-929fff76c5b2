import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';

export interface DocumentData {
  document_id?: string;
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size?: number;
  mime_type: string;
  is_required: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
}

export interface CreateDocumentData {
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size?: number;
  mime_type: string;
  is_required?: boolean;
}

export interface LicenseCategoryDocumentData {
  license_category_document_id: string;
  license_category_id: string;
  name: string;
  is_required: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface UploadDocumentResponse {
  document: DocumentData;
  message: string;
}

// Cache for preventing duplicate requests
const requestCache = new Map<string, Promise<any>>();

export const documentService = {
  // Get all documents with pagination and filtering
  async getDocuments(params?: { page?: number; limit?: number; search?: string; sortBy?: string }): Promise<any> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);

      const cacheKey = `/documents?${queryParams.toString()}`;

      // Check if we already have a pending request for this exact query
      if (requestCache.has(cacheKey)) {
        console.log('Returning cached request for:', cacheKey);
        return await requestCache.get(cacheKey);
      }

      // Create new request and cache it
      const requestPromise = apiClient.get(cacheKey).then(response => {
        // Remove from cache after completion
        requestCache.delete(cacheKey);
        return processApiResponse(response);
      }).catch(error => {
        // Remove from cache on error too
        requestCache.delete(cacheKey);
        throw error;
      });

      requestCache.set(cacheKey, requestPromise);
      return await requestPromise;
    } catch (error) {
      console.error('DocumentService.getDocuments error:', error);
      throw error;
    }
  },

  // Get documents by entity (polymorphic relationship)
  async getDocumentsByEntity(entityType: string, entityId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/documents/by-entity/${entityType}/${entityId}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('DocumentService.getDocumentsByEntity error:', error);
      throw error;
    }
  },

  // Get documents by application
  async getDocumentsByApplication(applicationId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/documents/by-application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      throw error;
    }
  },

  // Get required documents for license category
  async getRequiredDocumentsForLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocumentData[]> {
    try {
      const response = await apiClient.get(`/license-category-documents/category/${licenseCategoryId}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('DocumentService.getRequiredDocumentsForLicenseCategory error:', error);
      throw error;
    }
  },

  // Upload document (using the dedicated upload endpoint)
  async uploadDocument(file: File, documentData: Omit<CreateDocumentData, 'file_name' | 'file_path' | 'file_size' | 'mime_type'>): Promise<UploadDocumentResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('document_type', documentData.document_type);
      formData.append('entity_type', documentData.entity_type);
      formData.append('entity_id', documentData.entity_id);
      formData.append('is_required', (documentData.is_required || false).toString());
      formData.append('file_name', file.name);

      const response = await apiClient.post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = processApiResponse(response);
      return {
        document: result.data,
        message: result.message || 'Document uploaded successfully'
      };
    } catch (error) {
      console.error('DocumentService.uploadDocument error:', error);
      throw error;
    }
  },

  // Create document record (without file upload)
  async createDocument(documentData: CreateDocumentData): Promise<DocumentData> {
    try {
      const response = await apiClient.post('/documents', documentData);
      return processApiResponse(response);
    } catch (error) {
      console.error('DocumentService.createDocument error:', error);
      throw error;
    }
  },

  // Update document
  async updateDocument(documentId: string, updateData: Partial<CreateDocumentData>): Promise<DocumentData> {
    try {
      const response = await apiClient.put(`/documents/${documentId}`, updateData);
      return processApiResponse(response);
    } catch (error) {
      console.error('DocumentService.updateDocument error:', error);
      throw error;
    }
  },

  // Delete document
  async deleteDocument(documentId: string): Promise<void> {
    try {
      await apiClient.delete(`/documents/${documentId}`);
    } catch (error) {
      console.error('DocumentService.deleteDocument error:', error);
      throw error;
    }
  },

  // Get document by ID
  async getDocument(documentId: string): Promise<DocumentData> {
    try {
      const response = await apiClient.get(`/documents/${documentId}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('DocumentService.getDocument error:', error);
      throw error;
    }
  },

  // Download document
  async downloadDocument(documentId: string): Promise<Blob> {
    try {
      const response = await apiClient.get(`/documents/${documentId}/download`, {
        responseType: 'blob',
      });
      return response.data; // Return blob directly, not processed
    } catch (error) {
      console.error('DocumentService.downloadDocument error:', error);
      throw error;
    }
  },

  // Preview document
  async previewDocument(documentId: string): Promise<Blob> {
    try {
      const response = await apiClient.get(`/documents/${documentId}/preview`, {
        responseType: 'blob',
      });
      return response.data; // Return blob directly for preview
    } catch (error) {
      console.error('DocumentService.previewDocument error:', error);
      throw error;
    }
  },

  // Check if document type is previewable
  isPreviewable(mimeType: string = ""): boolean {
    if (!mimeType) return false;
    const previewableMimeTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'text/plain',
      'text/html',
      'text/css',
      'text/javascript',
      'application/json',
    ];

    return previewableMimeTypes.includes(mimeType.toLowerCase());
  },

  // Check if all required documents are uploaded for an application
  async checkRequiredDocuments(applicationId: string, licenseCategoryId: string): Promise<{
    allUploaded: boolean;
    missing: LicenseCategoryDocumentData[];
    uploaded: DocumentData[];
  }> {
    try {
      // Get required documents for license category
      const requiredDocs = await this.getRequiredDocumentsForLicenseCategory(licenseCategoryId);
      
      // Get uploaded documents for application
      const data = await this.getDocumentsByApplication(applicationId);
      const uploadedDocs: DocumentData[] = data.data
      
      // Check which required documents are missing
      const uploadedTypes = uploadedDocs.map(doc => doc.document_type);
      const missing = requiredDocs.filter(reqDoc => 
        reqDoc.is_required && !uploadedTypes.includes(reqDoc.name.toLowerCase().replace(/\s+/g, '_'))
      );
      
      return {
        allUploaded: missing.length === 0,
        missing,
        uploaded: uploadedDocs
      };
    } catch (error) {
      console.error('DocumentService.checkRequiredDocuments error:', error);
      throw error;
    }
  },

  // Get document types enum
  getDocumentTypes(): string[] {
    return [
      'certificate_incorporation',
      'memorandum_association',
      'shareholding_structure',
      'business_plan',
      'financial_statements',
      'technical_proposal',
      'coverage_plan',
      'network_diagram',
      'equipment_specifications',
      'insurance_certificate',
      'tax_clearance',
      'audited_accounts',
      'bank_statement',
      'cv_document',
      'other'
    ];
  },

  // Format document type for display
  formatDocumentType(type: string): string {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  },

  // Map document name to DocumentType enum value
  mapDocumentNameToType(documentName: string): string {
    const nameToTypeMap: Record<string, string> = {
      'Certificate of Incorporation': 'certificate_incorporation',
      'Memorandum of Association': 'memorandum_association',
      'Shareholding Structure': 'shareholding_structure',
      'Business Plan': 'business_plan',
      'Financial Statements': 'financial_statements',
      'Technical Proposal': 'technical_proposal',
      'Coverage Plan': 'coverage_plan',
      'Network Diagram': 'network_diagram',
      'Equipment Specifications': 'equipment_specifications',
      'Insurance Certificate': 'insurance_certificate',
      'Tax Clearance Certificate': 'tax_clearance',
      'Tax Clearance': 'tax_clearance',
      'Audited Accounts': 'audited_accounts',
      'Bank Statement': 'bank_statement',
      'CV Document': 'cv_document',
      'Other': 'other'
    };

    // Try exact match first
    if (nameToTypeMap[documentName]) {
      return nameToTypeMap[documentName];
    }

    // Try case-insensitive match
    const lowerName = documentName.toLowerCase();
    for (const [name, type] of Object.entries(nameToTypeMap)) {
      if (name.toLowerCase() === lowerName) {
        return type;
      }
    }

    // Fallback: convert name to snake_case
    return documentName.toLowerCase().replace(/\s+/g, '_');
  },

  // Validate file type and size
  validateFile(file: File, maxSizeMB: number = 10, allowedTypes: string[] = []): {
    isValid: boolean;
    error?: string;
  } {
    // Check file size (convert MB to bytes)
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSizeMB}MB`
      };
    }

    // Check file type if specified
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    return { isValid: true };
  }
};
