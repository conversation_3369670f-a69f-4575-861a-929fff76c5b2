'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { EvaluationLayout, DataDisplayCard } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { useEvaluationData } from '@/hooks/useEvaluationData';
import EvaluationStepValidator from '@/components/evaluation/EvaluationStepValidator';



interface EvaluateProfessionalServicesPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateProfessionalServicesPage: React.FC<EvaluateProfessionalServicesPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Use evaluation data hook
  const { data, loading, error } = useEvaluationData(applicationId);

  // Dynamic navigation hook - same as apply pages
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep,
    previousStep,
    currentStep,
    totalSteps,
    licenseTypeCode: navLicenseTypeCode
  } = useDynamicNavigation({
    currentStepRoute: 'professional-services',
    licenseCategoryId,
    applicationId
  });

  // Set license category ID when data loads
  useEffect(() => {
    if (data?.application?.license_category_id) {
      setLicenseCategoryId(data.application.license_category_id);
    }
  }, [data]);

  // Authentication check
  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!applicationId) {
      router.push('/applications');
      return;
    }
  }, [isAuthenticated, authLoading, router, applicationId]);

  // Navigation handlers
  const handleNext = async () => {
    setIsSubmitting(true);
    try {
      await dynamicHandleNext();
    } catch (error) {
      console.error('Error navigating to next step:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrevious = async () => {
    setIsSubmitting(true);
    try {
      await dynamicHandlePrevious();
    } catch (error) {
      console.error('Error navigating to previous step:', error);
    } finally {
      setIsSubmitting(false);
    }
  };



  // Loading state
  if (authLoading || loading) {
    return (
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="professional-services"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={true}
        previousButtonDisabled={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading professional services data...</span>
        </div>
      </EvaluationLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="professional-services"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={true}
        previousButtonDisabled={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400 text-xl"></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Data
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </EvaluationLayout>
    );
  }

  // No application found
  if (!data.application && !loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <EvaluationStepValidator
      licenseType={licenseType}
      currentStepRoute="professional-services"
      applicationId={applicationId!}
      licenseCategoryId={licenseCategoryId || undefined}
    >
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="professional-services"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        {/* Professional Services Evaluation */}
        <div className="space-y-6">

          {/* Professional Services Information */}
          {data?.professionalServices && data.professionalServices.length > 0 ? (
            data.professionalServices.map((serviceData, index) => (
              <DataDisplayCard
                key={index}
                title="Professional Services Information"
                icon="ri-service-line"
                fields={[
                  { label: 'Service Type', value: serviceData.service_type || 'N/A' },
                  { label: 'Service Category', value: serviceData.service_category || 'N/A' },
                  { label: 'Target Market', value: serviceData.target_market || 'N/A' },
                  { label: 'Geographic Coverage', value: serviceData.geographic_coverage || 'N/A' },
                  { label: 'Expected Start Date', value: serviceData.expected_start_date ? new Date(serviceData.expected_start_date).toLocaleDateString() : 'N/A' },
                  { label: 'Estimated Investment', value: serviceData.estimated_investment || 'N/A' },
                  { label: 'Service Description', value: serviceData.service_description || 'N/A' },
                  { label: 'Technical Requirements', value: serviceData.technical_requirements || 'N/A' },
                  { label: 'Quality Assurance', value: serviceData.quality_assurance || 'N/A' },
                  { label: 'Compliance Framework', value: serviceData.compliance_framework || 'N/A' },
                  { label: 'Created Date', value: serviceData.created_at ? new Date(serviceData.created_at).toLocaleDateString() : 'N/A' }
                ]}
              />
            ))
          ) : (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <i className="ri-information-line text-yellow-400 text-xl"></i>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No Professional Services Information Found
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>No professional services information has been submitted for this application yet.</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Applicant Information */}
          {data?.applicant && (
            <DataDisplayCard
              title="Applicant Information"
              icon="ri-user-line"
              fields={[
                { label: 'Full Name', value: `${data.applicant.name}` },
                { label: 'Email', value: data.applicant.email },
                { label: 'Phone', value: data.applicant.phone || 'N/A' },
                { label: 'Organization', value: data.applicant.organization_name || 'N/A' }
              ]}
            />
          )}
        </div>
      </EvaluationLayout>
    </EvaluationStepValidator>
  );
};

export default EvaluateProfessionalServicesPage;
