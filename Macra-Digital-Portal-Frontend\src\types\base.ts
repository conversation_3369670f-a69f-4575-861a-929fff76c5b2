/**
 * Base Types
 * Common interfaces and types used across the application
 */

// Base entity interface
export interface BaseEntity {
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;
}

// User reference interface
export interface UserReference {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
}

// Pagination query interface
export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
  filter?: Record<string, any>;
}

// API response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
  path: string;
  statusCode: number;
}

// File upload interface
export interface FileUpload {
  file: File;
  name: string;
  type: string;
  size: number;
}


// Contact information interface
export interface ContactInfo {
  contact_id: string;
  phone: string;
  email: string;
  fax?: string;
  website?: string;
}
