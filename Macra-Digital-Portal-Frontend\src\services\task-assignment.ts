import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { AssignApplicationRequest, AssignTaskDto, CreateTaskDto, PaginatedResponse, PaginateQuery, Task, TaskFilters, TaskStats, UpdateTaskDto } from '@/types';


export const taskService = {
  // Main task CRUD operations
  getTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    // Handle assignment_status filter by using different endpoints or filters
    if (params?.assignment_status === 'unassigned') {
      // Use the unassigned tasks endpoint
      return taskService.getUnassignedTasks(params);
    } else if (params?.assignment_status === 'assigned') {
      // Use the assigned tasks endpoint instead of recursive call
      return taskService.getAssignedTasks(params);
    }

    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    // Use nestjs-paginate filter format: filter.columnName=value
    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);
    if (params?.status) searchParams.append('filter.status', params.status);
    if (params?.priority) searchParams.append('filter.priority', params.priority);
    if (params?.assigned_to) searchParams.append('filter.assigned_to', params.assigned_to);

    const queryString = searchParams.toString();
    const url = `/tasks${queryString ? `?${queryString}` : ''}`;

    console.log('🔗 Making API call to:', url);
    console.log('📊 Request params:', params);

    const response = await apiClient.get(url);
    console.log('✅ API response:', response.data);
    return response.data;
  },

  getUnassignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    // Use nestjs-paginate filter format: filter.columnName=value
    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);
    if (params?.status) searchParams.append('filter.status', params.status);
    if (params?.priority) searchParams.append('filter.priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getAssignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    // Use nestjs-paginate filter format: filter.columnName=value
    if (params?.task_type) searchParams.append('filter.task_type', params.task_type);
    if (params?.status) searchParams.append('filter.status', params.status);
    if (params?.priority) searchParams.append('filter.priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getMyTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;
    const response = await apiClient.get(url);
    return processApiResponse(response);
  },

  getTaskStats: async (): Promise<TaskStats> => {
    const response = await apiClient.get('/tasks/stats');
    return processApiResponse(response);
  },

  getTask: async (taskId: string): Promise<Task> => {
    const response = await apiClient.get(`/tasks/${taskId}`);
    return processApiResponse(response);
  },

  createTask: async (taskData: CreateTaskDto): Promise<Task> => {
    const response = await apiClient.post('/tasks', taskData);
    return processApiResponse(response);
  },

  updateTask: async (taskId: string, taskData: UpdateTaskDto): Promise<Task> => {
    const response = await apiClient.patch(`/tasks/${taskId}`, taskData);
    return processApiResponse(response);
  },

  getTaskForApplication: async (applicationId: string): Promise<Task | null> => {
    try {
      const response = await apiClient.get(`/tasks/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null; // No task found for application
      }
      throw error;
    }
  },

  assignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {
    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);
    return processApiResponse(response);
  },

  reassignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {
    const response = await apiClient.put(`/tasks/${taskId}/reassign`, assignData);
    return processApiResponse(response);
  },

  deleteTask: async (taskId: string): Promise<void> => {
    await apiClient.delete(`/tasks/${taskId}`);
  },

  // Get users for assignment (officers only, exclude customers)
  getUsers: async (): Promise<any> => {
    try {
      const response = await apiClient.get('/users', {
        params: {
          limit: 100,
          filter: {
            exclude_customers: true
          }
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  },

  // Get officers specifically (non-customer users)
  getOfficers: async (): Promise<any> => {
    try {
      const response = await apiClient.get('/users/list/officers');
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching officers:', error);
      // Fallback to regular users endpoint with filtering
      try {
        const fallbackResponse = await apiClient.get('/users', {
          params: {
            limit: 100,
            filter: {
              exclude_customers: true
            }
          }
        });
        return processApiResponse(fallbackResponse);
      } catch (fallbackError) {
        console.error('Error fetching users as fallback:', fallbackError);
      }
    }
  },
};

// Legacy service for backward compatibility
export const taskAssignmentService = {
  // Generic task management methods
  getUnassignedTasks: taskService.getUnassignedTasks,
  getAssignedTasks: taskService.getAssignedTasks,
  assignTask: taskService.assignTask,
  getTaskById: taskService.getTask,

  // Legacy application-specific methods (for backward compatibility)
  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get all applications (including assigned)
  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get applications assigned to current user
  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get officers for assignment
  getOfficers: async () => {
    try {
      const response = await apiClient.get('/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching officers:', error);
      return { data: [] };
    }
  },

  // Assign application to officer
  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {
    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);
    return response.data;
  },

  // Get application details
  getApplication: async (applicationId: string) => {
    const response = await apiClient.get(`/applications/${applicationId}`);
    return response.data;
  },
};