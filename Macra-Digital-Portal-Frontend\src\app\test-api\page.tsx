'use client';

import React, { useState } from 'react';
import { apiClient } from '@/lib/apiClient';

export default function TestApiPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const testEndpoint = async (endpoint: string, method: 'GET' | 'POST' = 'GET') => {
    setLoading(true);
    try {
      console.log(`Testing ${method} ${endpoint}`);

      let response;
      if (method === 'GET') {
        response = await apiClient.get(endpoint);
      } else {
        response = await apiClient.post(endpoint, {
          title: 'Test Complaint',
          description: 'This is a test complaint for API testing',
          category: 'Other'
        });
      }

      console.log(`✅ ${endpoint} response:`, response.data);
      console.log(`✅ ${endpoint} response.data type:`, typeof response.data.data);
      console.log(`✅ ${endpoint} response.data.data:`, response.data.data);

      setResults(prev => ({
        ...prev,
        [endpoint]: {
          success: true,
          data: response.data,
          status: response.status,
          dataType: typeof response.data.data,
          isArray: Array.isArray(response.data.data)
        }
      }));
    } catch (error: any) {
      console.error(`❌ ${endpoint} error:`, error);
      setResults(prev => ({
        ...prev,
        [endpoint]: {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const endpoints = [
    { path: '/consumer-affairs-complaints', method: 'GET' as const },
    { path: '/data-breach-reports', method: 'GET' as const },
    { path: '/consumer-affairs-complaints', method: 'POST' as const },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          API Endpoint Testing
        </h1>

        <div className="space-y-4 mb-8">
          {endpoints.map((endpoint) => (
            <button
              key={`${endpoint.method}-${endpoint.path}`}
              onClick={() => testEndpoint(endpoint.path, endpoint.method)}
              disabled={loading}
              className="w-full text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow border hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            >
              <div className="flex justify-between items-center">
                <span className="font-medium">
                  {endpoint.method} {endpoint.path}
                </span>
                <span className="text-sm text-gray-500">
                  {loading ? 'Testing...' : 'Click to test'}
                </span>
              </div>
            </button>
          ))}
        </div>

        <div className="space-y-6">
          {Object.entries(results).map(([endpoint, result]: [string, any]) => (
            <div key={endpoint} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                {endpoint}
                <span className={`ml-2 px-2 py-1 text-xs rounded ${
                  result.success 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {result.success ? 'SUCCESS' : 'ERROR'}
                </span>
                {result.status && (
                  <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded">
                    {result.status}
                  </span>
                )}
              </h3>
              
              <div className="bg-gray-50 dark:bg-gray-900 rounded p-4 overflow-auto">
                <pre className="text-sm text-gray-700 dark:text-gray-300">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Instructions:
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Make sure you're logged in before testing</li>
            <li>• Check the browser console for detailed logs</li>
            <li>• Backend should be running on port 3001</li>
            <li>• Test credentials: <EMAIL> / Password123!</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
