/**
 * Shortcode Types and Interfaces
 * Based on backend DTOs and entity definitions
 */

import { Application } from "./license";
import { Organization } from "./organization";
import { User } from "./user";

// Enums matching backend
export enum ShortcodeCategory {
  RESERVED = 'reserved',
  LIFELINE_SERVICES = 'lifeline_services',
  DATA_VOICE_USSD = 'data_voice_ussd',
  CUSTOMER_CARE = 'customer_care',
  LIFE_AND_SAFETY = 'life_and_safety',
  FUTURE_USE = 'future_use',
}

export enum ShortcodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum ShortcodeAudience {
  COMMUNITY = 'community',
  NATIONAL = 'national',
  REGIONAL = 'regional',
  DISTRICT = 'district',
}

// Base shortcode interface matching the entity
export interface Shortcode {
  shortcode_id: string;
  shortcode: string;
  application_id?: string;
  assigned_to?: string;
  shortcode_length: number;
  audience: string;
  status: string;
  category: string;
  description?: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
  created_by: string;
  updated_by?: string;
  creator?: User; // User entity
  updater?: User; // User entity
  application?: Application; // Application entity
  assignee?: Organization; // Organization entity
}

// Create shortcode DTO interface
export interface CreateShortcodeDto {
  application_id?: string;
  audience: string;
  status: string;
  category: string;
  description: string;
  notes?: string;
  shortcode_length: number;
}

// Update shortcode DTO interface
export interface UpdateShortcodeDto {
  application_id?: string;
  audience?: string;
  status?: string;
  category?: string;
  description?: string;
  notes?: string;
  shortcode_length?: number;
  updated_by?: string;
  assigned_to?: string;
  shortcode?: string;
}

// Shortcode numbering plan category interface
export interface ShortcodeNumberingCategory {
  category: ShortcodeCategory;
  start: number;
  end: number;
}

// Shortcode numbering plan length interface
export interface ShortcodeNumberingPlan {
  length: number;
  categories: ShortcodeNumberingCategory[];
}

// Shortcode filters for API queries
export interface ShortcodeFilters {
  status?: ShortcodeStatus;
  category?: ShortcodeCategory;
  audience?: ShortcodeAudience;
  assigned_to?: string;
  application_id?: string;
  search?: string;
}

// Form data interface for the ShortCodeUsage component
export interface ShortcodeFormData {
  audience: ShortcodeAudience;
  category: ShortcodeCategory;
  description: string;
  shortcode_length?: 3 | 4;
  notes?: string;
}

// API response interfaces
export interface ShortcodeApiResponse {
  success: boolean;
  message: string;
  data: Shortcode;
  timestamp: string;
  path: string;
  statusCode: number;
}

export interface ShortcodeListApiResponse {
  success: boolean;
  message: string;
  data: Shortcode[];
  timestamp: string;
  path: string;
  statusCode: number;
}

// Category display options for forms
export const SHORTCODE_CATEGORY_OPTIONS = [
  { value: ShortcodeCategory.RESERVED, label: 'Reserved' },
  { value: ShortcodeCategory.LIFELINE_SERVICES, label: 'Lifeline Services' },
  { value: ShortcodeCategory.DATA_VOICE_USSD, label: 'Data/Voice/USSD' },
  { value: ShortcodeCategory.CUSTOMER_CARE, label: 'Customer Care' },
  { value: ShortcodeCategory.LIFE_AND_SAFETY, label: 'Life and Safety' },
  { value: ShortcodeCategory.FUTURE_USE, label: 'Future Use' },
];

// Audience display options for forms
export const SHORTCODE_AUDIENCE_OPTIONS = [
  { value: ShortcodeAudience.COMMUNITY, label: 'Community' },
  { value: ShortcodeAudience.NATIONAL, label: 'National' },
  { value: ShortcodeAudience.REGIONAL, label: 'Regional' },
  { value: ShortcodeAudience.DISTRICT, label: 'District' },
];



// Status display options (for admin use)
export const SHORTCODE_STATUS_OPTIONS = [
  { value: ShortcodeStatus.ACTIVE, label: 'Active' },
  { value: ShortcodeStatus.INACTIVE, label: 'Inactive' },
];

// Validation rules
export const SHORTCODE_VALIDATION = {
  DESCRIPTION_MAX_LENGTH: 255,
  NOTES_MAX_LENGTH: 255,
  SHORTCODE_MIN_LENGTH: 3,
  SHORTCODE_MAX_LENGTH: 4,
};

// Helper functions
export const getShortcodeCategoryLabel = (category: string): string => {
  const option = SHORTCODE_CATEGORY_OPTIONS.find(opt => opt.value === category);
  return option?.label || category;
};

export const getShortcodeAudienceLabel = (audience: string): string => {
  const option = SHORTCODE_AUDIENCE_OPTIONS.find(opt => opt.value === audience);
  return option?.label || audience;
};

export const getShortcodeStatusLabel = (status: string): string => {
  const option = SHORTCODE_STATUS_OPTIONS.find(opt => opt.value === status);
  return option?.label || status;
};

// Shortcode numbering plan (from backend)
export const shortCodeNumbering: ShortcodeNumberingPlan[] = [
  {
    length: 3,
    categories: [
      { category: ShortcodeCategory.CUSTOMER_CARE, start: 100, end: 109 },
      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 110, end: 119 },
      { category: ShortcodeCategory.RESERVED, start: 120, end: 199 },
      { category: ShortcodeCategory.RESERVED, start: 200, end: 299 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 300, end: 499 },
      { category: ShortcodeCategory.FUTURE_USE, start: 500, end: 899 },
      { category: ShortcodeCategory.LIFE_AND_SAFETY, start: 900, end: 999 },
    ]
  },
  {
    length: 4,
    categories: [
      { category: ShortcodeCategory.RESERVED, start: 1000, end: 1099 },
      { category: ShortcodeCategory.LIFELINE_SERVICES, start: 1100, end: 1199 },
      { category: ShortcodeCategory.RESERVED, start: 1200, end: 1999 },
      { category: ShortcodeCategory.RESERVED, start: 2000, end: 2999 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 3000, end: 3999 },
      { category: ShortcodeCategory.DATA_VOICE_USSD, start: 4000, end: 4999 },
      { category: ShortcodeCategory.RESERVED, start: 5000, end: 8999 }
    ]
  }
];

// Default form values
export const DEFAULT_SHORTCODE_FORM_DATA: ShortcodeFormData = {
  audience: ShortcodeAudience.COMMUNITY,
  category: ShortcodeCategory.RESERVED,
  description: '',
  notes: '',
};
