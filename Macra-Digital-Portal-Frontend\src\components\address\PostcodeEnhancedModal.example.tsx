// Example: Enhanced AddressModal with Postcode Auto-Population
// This shows how to integrate the new postcode lookup functions

'use client';

import { useState, useEffect } from 'react';
import { Address, AddressModalProps, ADDRESS_TYPES, ENTITY_TYPES, CreateAddressDto, EditAddressData, SequentialAddressData } from '@/types/address_types';
import { useAddresses } from '@/hooks/useAddressing';
import SequentialAddressBuilder from './SequentialAddressBuilder';

const EnhancedAddressModal = ({ isOpen, onClose, onSave, address, entityType, entityId }: AddressModalProps) => {
  const [sequentialData, setSequentialData] = useState<Partial<SequentialAddressData>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);

  const { createAddress, editAddress, getPostcodeDetails, populateAddressFromPostcode } = useAddresses();

  const isEditing = !!address;

  // Enhanced initialization with postcode lookup
  useEffect(() => {
    const initializeWithPostcodeLookup = async () => {
      if (isOpen) {
        if (address) {
          // Start with basic address data
          const basicData: Partial<SequentialAddressData> = {
            country: address.country || 'Malawi',
            region: '',
            district: '',
            location: address.city || '',
            postal_code: address.postal_code || '',
            address_line_1: address.address_line_1 || '',
            address_line_2: address.address_line_2 || '',
          };

          setSequentialData(basicData);

          // If we have a postal code but missing region/district data, enhance it
          if (address.postal_code && address.country === 'Malawi') {
            setIsEnhancing(true);
            try {
              console.log(`Enhancing address data with postal code: ${address.postal_code}`);
              const enhancedData = await populateAddressFromPostcode(address.postal_code);
              
              if (enhancedData) {
                // Merge enhanced data with existing address lines
                const completeData: Partial<SequentialAddressData> = {
                  ...enhancedData,
                  address_line_1: address.address_line_1 || '',
                  address_line_2: address.address_line_2 || '',
                };
                
                setSequentialData(completeData);
                console.log('Address enhanced with postcode data:', completeData);
              }
            } catch (error) {
              console.error('Failed to enhance address with postcode data:', error);
              // Continue with basic data if enhancement fails
            } finally {
              setIsEnhancing(false);
            }
          }
        } else {
          // Creating new address - start with empty data
          setSequentialData({
            country: '',
            region: '',
            district: '',
            location: '',
            postal_code: '',
            address_line_1: '',
            address_line_2: '',
          });
        }
        setErrors({});
      }
    };

    initializeWithPostcodeLookup();
  }, [isOpen, address, populateAddressFromPostcode]);

  // Enhanced field change handler with postcode auto-population
  const handleFieldChange = async (field: string, value: string) => {
    // Update the field immediately
    setSequentialData(prev => ({ ...prev, [field]: value }));

    // If postal code is being changed and it's for Malawi, auto-populate other fields
    if (field === 'postal_code' && value.length >= 3 && sequentialData.country === 'Malawi') {
      try {
        console.log(`Auto-populating address data for postal code: ${value}`);
        const addressData = await populateAddressFromPostcode(value);
        
        if (addressData) {
          // Merge with existing data, preserving address lines
          setSequentialData(prev => ({
            ...prev,
            ...addressData,
            // Keep existing address lines
            address_line_1: prev.address_line_1 || '',
            address_line_2: prev.address_line_2 || '',
          }));
          
          console.log('Address auto-populated from postal code:', value);
          
          // Clear any location-related errors since we just populated valid data
          setErrors(prev => {
            const newErrors = { ...prev };
            delete newErrors.region;
            delete newErrors.district;
            delete newErrors.location;
            return newErrors;
          });
        }
      } catch (error) {
        console.error('Failed to auto-populate address from postal code:', error);
        // Continue normally if auto-population fails
      }
    }
  };

  // Validation with postcode verification
  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    // Basic validation
    if (!sequentialData.address_line_1?.trim()) {
      newErrors.address_line_1 = 'Address line 1 is required';
    }

    if (!sequentialData.postal_code?.trim()) {
      newErrors.postal_code = 'Postal code is required';
    } else if (sequentialData.country === 'Malawi') {
      // For Malawi postal codes, verify they exist in our system
      try {
        const postcodeDetails = await getPostcodeDetails(sequentialData.postal_code);
        if (!postcodeDetails) {
          newErrors.postal_code = 'Invalid postal code. Please select from available options.';
        }
      } catch (error) {
        console.warn('Could not verify postal code:', error);
        // Don't fail validation if verification service is down
      }
    }

    if (!sequentialData.country?.trim()) {
      newErrors.country = 'Country is required';
    }

    if (sequentialData.country === 'Malawi') {
      if (!sequentialData.location?.trim()) {
        newErrors.location = 'Location is required';
      }
    } else {
      if (!sequentialData.location?.trim()) {
        newErrors.location = 'City is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!(await validateForm())) {
      return;
    }

    try {
      setIsLoading(true);

      // Convert to API format
      const formData: CreateAddressDto = {
        address_type: ADDRESS_TYPES.BUSINESS,
        entity_type: entityType || ENTITY_TYPES.USER,
        entity_id: entityId || '',
        address_line_1: sequentialData.address_line_1 || '',
        address_line_2: sequentialData.address_line_2 || '',
        address_line_3: '',
        postal_code: sequentialData.postal_code || '',
        country: sequentialData.country || '',
        city: sequentialData.location || '',
      };

      let savedAddress: Address;

      if (isEditing && address) {
        const updateData: EditAddressData = {
          address_id: address.address_id,
          ...formData,
        };
        const response = await editAddress(updateData);
        savedAddress = response.data;
      } else {
        const response = await createAddress(formData);
        savedAddress = response.data;
      }

      onSave(savedAddress);
      onClose();
    } catch (error: any) {
      console.error('Error saving address:', error);
      setErrors({ general: 'Failed to save address. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            {/* Header */}
            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                  {isEditing ? 'Edit Address' : 'Add New Address'}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>

              {/* Enhancement status */}
              {isEnhancing && (
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                  <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Enhancing address data with postal code information...
                  </div>
                </div>
              )}

              {/* General error */}
              {errors.general && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.general}</p>
                </div>
              )}

              {/* Enhanced Sequential Address Builder */}
              <div className="space-y-4">
                <SequentialAddressBuilder
                  onAddressComplete={(address) => setSequentialData(address)}
                  onAddressChange={(address) => setSequentialData(prev => ({ ...prev, ...address }))}
                  onFieldChange={handleFieldChange} // Enhanced with postcode auto-population
                  validationErrors={errors}
                  initialData={sequentialData}
                  disabled={isLoading || isEnhancing}
                  className="space-y-4"
                />
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={isLoading || isEnhancing}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-2"></i>
                    {isEditing ? 'Update Address' : 'Create Address'}
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading || isEnhancing}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAddressModal;
