'use client';

import React from 'react';

interface ComplaintStage {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}

interface ComplaintStatusBarProps {
  currentStage: number; // 0-based index
  stages: ComplaintStage[];
  status?: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  progressPercentage?: number;
  showPercentage?: boolean;
  showStageNames?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'horizontal' | 'vertical';
  className?: string;
}

const ComplaintStatusBar: React.FC<ComplaintStatusBarProps> = ({
  currentStage,
  stages,
  status = 'submitted',
  progressPercentage,
  showPercentage = true,
  showStageNames = true,
  size = 'md',
  variant = 'horizontal',
  className = ''
}) => {
  // Calculate progress percentage if not provided
  const calculatedProgress = progressPercentage ?? Math.round(((currentStage + 1) / stages.length) * 100);
  
  // Get status color
  const getStatusColor = () => {
    switch (status) {
      case 'submitted': return 'bg-blue-500';
      case 'under_review': return 'bg-yellow-500';
      case 'investigating': return 'bg-orange-500';
      case 'resolved': return 'bg-green-500';
      case 'closed': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  // Get status text color
  const getStatusTextColor = () => {
    switch (status) {
      case 'submitted': return 'text-blue-600';
      case 'under_review': return 'text-yellow-600';
      case 'investigating': return 'text-orange-600';
      case 'resolved': return 'text-green-600';
      case 'closed': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  // Size configurations
  const sizeClasses = {
    sm: {
      stage: 'w-6 h-6 text-xs',
      bar: 'h-1',
      text: 'text-xs'
    },
    md: {
      stage: 'w-8 h-8 text-sm',
      bar: 'h-2',
      text: 'text-sm'
    },
    lg: {
      stage: 'w-10 h-10 text-base',
      bar: 'h-3',
      text: 'text-base'
    }
  };

  // Vertical variant
  if (variant === 'vertical') {
    return (
      <div className={`space-y-4 ${className}`}>
        {stages.map((stage, index) => {
          const isCompleted = index < currentStage;
          const isCurrent = index === currentStage;

          return (
            <div key={stage.id} className="flex items-center space-x-3">
              {/* Stage Circle */}
              <div className={`
                ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium
                ${isCompleted ? `${getStatusColor()} text-white` : 
                  isCurrent ? `border-2 border-current ${getStatusTextColor()} bg-white` :
                  'bg-gray-200 text-gray-400'}
              `}>
                {isCompleted ? (
                  <i className="ri-check-line"></i>
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>

              {/* Stage Info */}
              <div className="flex-1">
                <div className={`font-medium ${isCurrent ? getStatusTextColor() : isCompleted ? 'text-gray-900' : 'text-gray-400'}`}>
                  {stage.name}
                </div>
                {stage.description && (
                  <div className={`${sizeClasses[size].text} ${isCurrent ? 'text-gray-600' : 'text-gray-400'}`}>
                    {stage.description}
                  </div>
                )}
              </div>

              {/* Progress Indicator */}
              {isCurrent && showPercentage && (
                <div className={`${sizeClasses[size].text} font-medium ${getStatusTextColor()}`}>
                  {calculatedProgress}%
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  // Horizontal variant
  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative">
        <div className={`w-full ${sizeClasses[size].bar} bg-gray-200 rounded-full overflow-hidden`}>
          <div 
            className={`${sizeClasses[size].bar} ${getStatusColor()} transition-all duration-500 ease-out rounded-full`}
            style={{ width: `${calculatedProgress}%` }}
          />
        </div>

        {/* Stage Markers */}
        <div className="absolute top-0 left-0 w-full flex justify-between items-center" style={{ transform: 'translateY(-50%)' }}>
          {stages.map((stage, index) => {
            const isCompleted = index < currentStage;
            const isCurrent = index === currentStage;
            const position = (index / (stages.length - 1)) * 100;

            return (
              <div 
                key={stage.id}
                className="flex flex-col items-center"
                style={{ position: 'absolute', left: `${position}%`, transform: 'translateX(-50%)' }}
              >
                {/* Stage Circle */}
                <div className={`
                  ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium border-2 bg-white
                  ${isCompleted ? `${getStatusColor().replace('bg-', 'border-')} ${getStatusColor()} text-white` : 
                    isCurrent ? `border-current ${getStatusTextColor()}` :
                    'border-gray-300 text-gray-400'}
                `}>
                  {isCompleted ? (
                    <i className="ri-check-line"></i>
                  ) : stage.icon ? (
                    <i className={stage.icon}></i>
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>

                {/* Stage Name */}
                {showStageNames && (
                  <div className={`mt-2 ${sizeClasses[size].text} font-medium text-center max-w-20 ${
                    isCurrent ? getStatusTextColor() : 
                    isCompleted ? 'text-gray-900' : 
                    'text-gray-400'
                  }`}>
                    {stage.name}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Progress Percentage */}
      {showPercentage && (
        <div className="flex justify-between items-center mt-8">
          <div className={`${sizeClasses[size].text} ${getStatusTextColor()} font-medium`}>
            Progress: {calculatedProgress}%
          </div>
          <div className={`${sizeClasses[size].text} text-gray-500 capitalize`}>
            Status: {status.replace('_', ' ')}
          </div>
        </div>
      )}

      {/* Current Stage Description */}
      {showStageNames && stages[currentStage]?.description && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className={`${sizeClasses[size].text} text-gray-600 dark:text-gray-400`}>
            <strong>Current Stage:</strong> {stages[currentStage].description}
          </div>
        </div>
      )}
    </div>
  );
};

export default ComplaintStatusBar;

// Predefined stage configurations for different complaint types
export const COMPLAINT_STAGES = {
  CONSUMER_AFFAIRS: [
    { id: 'submitted', name: 'Submitted', description: 'Complaint has been received and logged', icon: 'ri-file-text-line' },
    { id: 'under_review', name: 'Under Review', description: 'Initial review and assessment in progress', icon: 'ri-search-line' },
    { id: 'investigating', name: 'Investigating', description: 'Detailed investigation and fact-finding', icon: 'ri-spy-line' },
    { id: 'resolved', name: 'Resolved', description: 'Issue has been resolved and action taken', icon: 'ri-check-double-line' }
  ],
  DATA_BREACH: [
    { id: 'submitted', name: 'Reported', description: 'Data breach report has been received', icon: 'ri-shield-line' },
    { id: 'under_review', name: 'Assessment', description: 'Assessing severity and impact', icon: 'ri-search-line' },
    { id: 'investigating', name: 'Investigation', description: 'Investigating the breach and gathering evidence', icon: 'ri-spy-line' },
    { id: 'resolved', name: 'Resolved', description: 'Breach contained and remediation completed', icon: 'ri-shield-check-line' }
  ]
};

// Helper function to get current stage index from status
export const getStageIndexFromStatus = (status: string): number => {
  const statusMap: { [key: string]: number } = {
    'submitted': 0,
    'under_review': 1,
    'investigating': 2,
    'resolved': 3,
    'closed': 3
  };
  return statusMap[status] || 0;
};
