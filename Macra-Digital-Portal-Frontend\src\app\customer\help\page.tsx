'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';

interface FAQItem {
  question: string;
  answer: string;
}

interface HelpCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  faqs: FAQItem[];
  guides: Array<{
    title: string;
    description: string;
  }>;
  subcategories?: HelpSubcategory[];
}

interface HelpSubcategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  faqs: FAQItem[];
  guides: Array<{
    title: string;
    description: string;
  }>;
  fees?: Array<{
    service: string;
    fee: string;
    description: string;
    acceptance?: string;
    modification?: string;
  }>;
}

const HelpCenterPage = () => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();
  
  const [activeCategory, setActiveCategory] = useState('contact-support');
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Help categories data
  const helpCategories: HelpCategory[] = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: 'ri-rocket-line',
      description: 'Learn the basics of using MACRA Digital Portal',
      guides: [
        {
          title: 'How to Navigate the Dashboard',
          description: 'The dashboard provides an overview of your licenses, applications, and recent transactions. Use the sidebar menu to access different sections, and the search bar to quickly find specific items.'
        },
        {
          title: 'Setting Up Your Account',
          description: 'Complete your profile information, set up notification preferences, and configure your settings to get the most out of the portal.'
        },
        {
          title: 'Understanding Your Dashboard',
          description: 'Dashboard cards show key metrics and status updates. Click on any card to view more detailed information about your licenses, applications, and payments.'
        }
      ],
      faqs: [
        {
          question: 'How do I reset my password?',
          answer: 'You can reset your password by clicking the "Forgot Password" link on the login page. Enter your email address and follow the instructions sent to your email.'
        },
        {
          question: 'How do I update my profile information?',
          answer: 'Go to your Profile page from the user menu in the top right corner. You can update your personal information, contact details, and organization settings there.'
        },
        {
          question: 'Can I have multiple users on one account?',
          answer: 'Yes, you can add team members to your organization account. Go to Account Settings to manage user roles and permissions.'
        }
      ]
    },
    {
      id: 'postal-licenses',
      name: 'Postal Licenses',
      icon: 'ri-mail-line',
      description: 'Postal and courier service licensing requirements',
      guides: [
        {
          title: 'Postal Service License Application',
          description: 'Learn how to apply for postal service licenses including requirements for mail handling, delivery services, and postal infrastructure.'
        },
        {
          title: 'Courier Service Licensing',
          description: 'Understand the requirements for courier and express delivery service licenses, including vehicle registration and service area coverage.'
        },
        {
          title: 'Postal Infrastructure Requirements',
          description: 'Guidelines for postal facilities, sorting centers, and delivery networks required for postal service operations.'
        }
      ],
      faqs: [
        {
          question: 'What are the requirements for a postal service license?',
          answer: 'Postal service licenses require proof of financial capability, adequate infrastructure, trained personnel, and compliance with postal service standards and regulations.'
        },
        {
          question: 'How long is a postal license valid?',
          answer: 'Postal licenses are typically valid for 5 years and must be renewed before expiration to continue operations.'
        },
        {
          question: 'Can I operate courier services with a postal license?',
          answer: 'Courier services may require separate licensing depending on the scope of services. Contact MACRA to determine specific requirements for your operations.'
        }
      ]
    },
    {
      id: 'standards-licenses',
      name: 'Standards',
      icon: 'ri-shield-check-line',
      description: 'Technical standards and compliance certification',
      guides: [
        {
          title: 'Standards Overview',
          description: 'MACRA ensures that all telecommunications equipment and services meet required technical standards for safety, quality, and interoperability.'
        },
        {
          title: 'Compliance Requirements',
          description: 'Understanding the regulatory framework for technical standards compliance in the telecommunications sector.'
        }
      ],
      faqs: [
        {
          question: 'What are MACRA technical standards?',
          answer: 'MACRA technical standards ensure that telecommunications equipment and services meet safety, quality, and interoperability requirements for the Malawi market.'
        },
        {
          question: 'Who needs to comply with technical standards?',
          answer: 'All manufacturers, importers, distributors, and service providers of telecommunications equipment and services must comply with MACRA technical standards.'
        }
      ],
      subcategories: [
        {
          id: 'type-approval',
          name: 'Type Approval',
          icon: 'ri-award-line',
          description: 'Equipment certification and type approval services',
          guides: [
            {
              title: 'Type Approval Process',
              description: 'Step-by-step guide to obtaining type approval certificates for telecommunications equipment before market introduction in Malawi.'
            },
            {
              title: 'Required Documentation',
              description: 'Complete list of technical documents, test reports, and certificates required for type approval applications.'
            },
            {
              title: 'Equipment Categories',
              description: 'Understanding different equipment categories and their specific type approval requirements and testing procedures.'
            },
            {
              title: 'Certificate Management',
              description: 'How to manage your type approval certificates, track expiry dates, and handle renewals or modifications.'
            }
          ],
          faqs: [
            {
              question: 'What equipment requires type approval?',
              answer: 'All telecommunications equipment including mobile phones, smartphones, tablets, radio equipment, broadcasting devices, base stations, and network infrastructure must obtain type approval before being sold or used in Malawi.'
            },
            {
              question: 'How long does type approval take?',
              answer: 'Type approval applications are typically processed within 30-60 business days, depending on the complexity of the equipment and completeness of documentation. Expedited processing is available for an additional fee.'
            },
            {
              question: 'What are the testing requirements?',
              answer: 'Equipment must undergo testing for electromagnetic compatibility (EMC), radio frequency (RF) emissions, specific absorption rate (SAR), and safety standards. Testing must be conducted by accredited laboratories.'
            },
            {
              question: 'Is type approval required for imported equipment?',
              answer: 'Yes, all telecommunications equipment imported into Malawi must have valid type approval certificates or obtain approval before importation. This includes equipment for personal use above certain quantities.'
            },
            {
              question: 'How long is a type approval certificate valid?',
              answer: 'Type approval certificates are typically valid for 5 years from the date of issue and can be renewed upon application with updated documentation.'
            },
            {
              question: 'Can I modify approved equipment?',
              answer: 'Any modifications to type-approved equipment require a new type approval application or modification approval, depending on the extent of changes made.'
            }
          ],
          fees: [
            {
              service: 'Base Stations (BTSs, NodeBs, NodeBs), Repeaters and Ancillary Equipment',
              fee: 'USD 600',
              description: 'Standards: EN 301 489-8, EN 301 502, EN 301 908-3, EN 301 908-7, EN 301 908-11, EN 301 489-23',
              acceptance: 'USD 300',
              modification: 'USD 100'
            },
            {
              service: 'Handsets, terminals & ancillary equipment',
              fee: 'USD 400',
              description: 'Standards: EN 301 489-7, EN 301 511, EN 301 908-5, EN 301 908-2, EN 301 908-6, EN 301 489-24',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Amateur radio and ancillary Equipment',
              fee: 'USD 100',
              description: 'Standards: EN 301 489-15, EN 301 783-2',
              acceptance: 'USD 50',
              modification: 'USD 50'
            },
            {
              service: 'Land Mobile Radio',
              fee: 'USD 100',
              description: 'Standards: EN 300 113-1, EN 300 390-1, EN 301 783-2, EN 301 908-5',
              acceptance: 'USD 50',
              modification: 'USD 50'
            },
            {
              service: 'Radar for Radio Navigation',
              fee: 'USD 600',
              description: 'Standards: EN 302 248, EN 302 194, EN 301 4891',
              acceptance: 'USD 300',
              modification: 'USD 100'
            },
            {
              service: 'Maritime Radio',
              fee: 'USD 100',
              description: 'Standards: EN 300 698, EN 301 025, EN 301 178',
              acceptance: 'USD 50',
              modification: 'USD 50'
            },
            {
              service: 'RLAN, Wi-Fi (WLAN), Bluetooth',
              fee: 'USD 400',
              description: 'Standards: EN 301 489-17, EN 301 893, EN 300 328',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Microwave Radio',
              fee: 'USD 600',
              description: 'Standards: EN 301 489-4, EN 302 217-2-2, EN 302 217-3, EN 302 217-4-2',
              acceptance: 'USD 400',
              modification: 'USD 100'
            },
            {
              service: 'Point to Point Radio, Fixed Link Equipment and Antenna',
              fee: 'Contact MACRA',
              description: 'Various standards depending on equipment specifications',
              acceptance: 'Contact MACRA',
              modification: 'Contact MACRA'
            },
            {
              service: 'SRD Radar Systems',
              fee: 'USD 400',
              description: 'Standards: EN 300 440, EN 302 288, EN 302 372, EN 301 4891',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Radio determination application',
              fee: 'USD 400',
              description: 'Standards: EN 300 440, EN 302 288, EN 302 372, EN 301 4891',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Vehicle Telematics',
              fee: 'USD 400',
              description: 'Standards: EN 300 674, EN 200 674, EN 301 091',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Road transport and traffic telematics',
              fee: 'USD 400',
              description: 'Standards: EN 300 674, EN 200 674, EN 301 091',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Car immobilizers, Alarm systems, data transfer to handheld devices etc.',
              fee: 'USD 400',
              description: 'Standards: EN 302 291, EN 300 330, FCC part 15, EN 300 220',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Inductive applications',
              fee: 'USD 400',
              description: 'Standards: EN 302 291, EN 300 330, FCC part 15, EN 300 220',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Article identification, asset tracking, alarms',
              fee: 'USD 400',
              description: 'Standards: EN 302 291, EN 300 440',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Radio Frequency identification applications',
              fee: 'USD 400',
              description: 'Standards: EN 302 291, EN 300 440',
              acceptance: 'USD 200',
              modification: 'USD 100'
            },
            {
              service: 'Sound broadcasting equipment - FM Radio',
              fee: 'USD 600',
              description: 'Standards: EN 301 489-11, EN 302 018-1, EN 302 018-2',
              acceptance: 'USD 400',
              modification: 'USD 100'
            },
            {
              service: 'TV Broadcast',
              fee: 'USD 600',
              description: 'Standards: EN 301 489-14, EN 302 297',
              acceptance: 'USD 400',
              modification: 'USD 100'
            }
          ]
        },
        {
          id: 'short-code',
          name: 'Short Code',
          icon: 'ri-hashtag',
          description: 'Short code allocation and management services',
          guides: [
            {
              title: 'Short Code Application Process',
              description: 'How to apply for short codes for SMS services, premium services, and value-added services in Malawi.'
            },
            {
              title: 'Short Code Categories',
              description: 'Understanding different types of short codes: common short codes, premium short codes, and dedicated short codes.'
            },
            {
              title: 'Service Requirements',
              description: 'Technical and service requirements for operating short code services including content guidelines and billing procedures.'
            },
            {
              title: 'Compliance and Monitoring',
              description: 'Ongoing compliance requirements, service monitoring, and reporting obligations for short code operators.'
            }
          ],
          faqs: [
            {
              question: 'What is a short code?',
              answer: 'A short code is a 3-6 digit number used for SMS services, allowing customers to send text messages to access information, services, or participate in campaigns.'
            },
            {
              question: 'What types of short codes are available?',
              answer: 'MACRA allocates Common Short Codes (free for users), Premium Short Codes (charged to users), and Dedicated Short Codes for specific organizations or services.'
            },
            {
              question: 'How do I apply for a short code?',
              answer: 'Submit an application through the MACRA portal with service description, technical specifications, content samples, and proof of agreements with mobile network operators.'
            },
            {
              question: 'What are the content restrictions?',
              answer: 'Short code services must comply with content guidelines prohibiting adult content, gambling, illegal activities, and must include clear terms and conditions for premium services.'
            },
            {
              question: 'How long does short code allocation take?',
              answer: 'Short code applications are typically processed within 21-30 business days after submission of complete documentation and payment of applicable fees.'
            },
            {
              question: 'Can I transfer my short code to another provider?',
              answer: 'Short codes can be transferred between service providers subject to MACRA approval and compliance with transfer procedures and requirements.'
            }
          ],
          fees: [
            {
              service: 'Common Short Code (3-digit)',
              fee: 'MWK 50,000',
              description: 'Annual fee for 3-digit common short codes (free for end users)'
            },
            {
              service: 'Common Short Code (4-digit)',
              fee: 'MWK 30,000',
              description: 'Annual fee for 4-digit common short codes (free for end users)'
            },
            {
              service: 'Premium Short Code (3-digit)',
              fee: 'MWK 100,000',
              description: 'Annual fee for 3-digit premium short codes (charged to end users)'
            },
            {
              service: 'Premium Short Code (4-digit)',
              fee: 'MWK 75,000',
              description: 'Annual fee for 4-digit premium short codes (charged to end users)'
            },
            {
              service: 'Dedicated Short Code (5-digit)',
              fee: 'MWK 25,000',
              description: 'Annual fee for 5-digit dedicated short codes for specific organizations'
            },
            {
              service: 'Short Code Transfer',
              fee: 'MWK 15,000',
              description: 'One-time fee for transferring short code between service providers'
            },
            {
              service: 'Short Code Modification',
              fee: 'MWK 10,000',
              description: 'Fee for modifying short code service parameters or content'
            },
            {
              service: 'Application Processing',
              fee: 'MWK 5,000',
              description: 'Non-refundable application processing fee'
            }
          ]
        }
      ]
    },
    {
      id: 'telecommunications-licenses',
      name: 'Telecommunications Licenses',
      icon: 'ri-signal-tower-line',
      description: 'Telecommunications service provider licensing',
      guides: [
        {
          title: 'Network Operator License',
          description: 'Requirements and process for obtaining licenses to operate telecommunications networks including mobile, fixed-line, and data services.'
        },
        {
          title: 'Service Provider License',
          description: 'Guidelines for telecommunications service provider licenses covering voice, data, and value-added services.'
        },
        {
          title: 'Infrastructure Sharing Agreements',
          description: 'Regulations and requirements for telecommunications infrastructure sharing and co-location agreements.'
        }
      ],
      faqs: [
        {
          question: 'What types of telecommunications licenses are available?',
          answer: 'MACRA issues various licenses including Network Facility Licenses, Network Service Licenses, Application Service Licenses, and Content Service Licenses depending on your service offerings.'
        },
        {
          question: 'What are the financial requirements for telecom licenses?',
          answer: 'Financial requirements vary by license type but typically include minimum capital requirements, bank guarantees, and proof of financial sustainability for the license period.'
        },
        {
          question: 'Can foreign companies apply for telecom licenses?',
          answer: 'Foreign companies can apply for telecommunications licenses subject to foreign ownership restrictions and local partnership requirements as specified in the Communications Act.'
        }
      ]
    },
    {
      id: 'broadcasting-licenses',
      name: 'Broadcasting Licenses',
      icon: 'ri-radio-line',
      description: 'Radio and television broadcasting licensing',
      guides: [
        {
          title: 'Radio Broadcasting License',
          description: 'Process for obtaining radio broadcasting licenses including commercial, community, and campus radio station licenses.'
        },
        {
          title: 'Television Broadcasting License',
          description: 'Requirements for television broadcasting licenses covering terrestrial, satellite, and cable television services.'
        },
        {
          title: 'Content and Programming Requirements',
          description: 'Guidelines for broadcasting content, local content quotas, and programming standards for licensed broadcasters.'
        }
      ],
      faqs: [
        {
          question: 'What are the different types of broadcasting licenses?',
          answer: 'Broadcasting licenses include Commercial Broadcasting, Public Broadcasting, Community Broadcasting, and Campus Broadcasting licenses, each with specific requirements and coverage areas.'
        },
        {
          question: 'What are the local content requirements?',
          answer: 'Broadcasters must comply with local content quotas as specified in their license conditions, typically requiring a minimum percentage of locally produced content.'
        },
        {
          question: 'How often must broadcasting licenses be renewed?',
          answer: 'Broadcasting licenses are typically valid for 10 years and must be renewed before expiration. Renewal applications should be submitted at least 6 months before expiry.'
        }
      ]
    },
    {
      id: 'applications',
      name: 'Applications',
      icon: 'ri-file-list-3-line',
      description: 'Managing your license applications',
      guides: [
        {
          title: 'Application Process Overview',
          description: 'Learn about the step-by-step process of submitting license applications, from initial submission to final approval.'
        },
        {
          title: 'Required Documentation',
          description: 'Understand what documents you need to prepare for different types of license applications to avoid delays.'
        },
        {
          title: 'Tracking Application Status',
          description: 'Monitor your application progress and respond to any requests for additional information promptly.'
        }
      ],
      faqs: [
        {
          question: 'Can I save my application as a draft?',
          answer: 'Yes, you can save your application as a draft and return to complete it later. Drafts are automatically saved as you fill out the form.'
        },
        {
          question: 'How do I upload supporting documents?',
          answer: 'Use the file upload section in your application form. Accepted formats include PDF, DOC, DOCX, JPG, and PNG files up to 10MB each.'
        },
        {
          question: 'Can I edit my application after submission?',
          answer: 'Once submitted, applications cannot be edited. However, you can respond to requests for additional information or clarification from MACRA staff.'
        }
      ]
    },
    {
      id: 'payments',
      name: 'Payments & Billing',
      icon: 'ri-bank-card-line',
      description: 'Payment processing and billing information',
      guides: [
        {
          title: 'Making Payments',
          description: 'Learn about available payment methods including bank transfers, mobile money, and online payments for license fees and other charges.'
        },
        {
          title: 'Understanding Your Bills',
          description: 'Review your billing statements, understand different fee types, and track your payment history through the portal.'
        },
        {
          title: 'Payment Schedules',
          description: 'Set up automatic payments and understand payment due dates to avoid late fees and service interruptions.'
        }
      ],
      faqs: [
        {
          question: 'What payment methods are accepted?',
          answer: 'We accept bank transfers, mobile money payments (Airtel Money, TNM Mpamba), and online card payments through our secure payment gateway.'
        },
        {
          question: 'How long does payment processing take?',
          answer: 'Bank transfers typically take 1-2 business days, while mobile money and card payments are processed immediately.'
        },
        {
          question: 'Can I get a refund for overpayments?',
          answer: 'Yes, overpayments can be refunded or applied to future bills. Contact support to process refund requests.'
        }
      ]
    },
    {
      id: 'account-settings',
      name: 'Account Settings',
      icon: 'ri-settings-line',
      description: 'Managing your account and preferences',
      guides: [
        {
          title: 'Profile Management',
          description: 'Update your personal information, contact details, and organization settings to keep your account current.'
        },
        {
          title: 'Security Settings',
          description: 'Manage your password, enable two-factor authentication, and review your account security settings.'
        },
        {
          title: 'Notification Preferences',
          description: 'Configure email and SMS notifications for license renewals, payment reminders, and application updates.'
        }
      ],
      faqs: [
        {
          question: 'How do I enable two-factor authentication?',
          answer: 'Go to Account Settings > Security and follow the instructions to set up 2FA using your mobile phone or authenticator app.'
        },
        {
          question: 'Can I change my registered email address?',
          answer: 'Yes, you can update your email address in your profile settings. You will need to verify the new email address before the change takes effect.'
        },
        {
          question: 'How do I delete my account?',
          answer: 'Account deletion requests must be submitted through our support team. Note that you must settle all outstanding obligations before account closure.'
        }
      ]
    },
    {
      id: 'contact-support',
      name: 'Contact Support',
      icon: 'ri-customer-service-2-line',
      description: 'Get in touch with our support team',
      guides: [
        {
          title: 'How to Contact Support',
          description: 'Multiple ways to reach our support team including phone, email, and online support requests through the portal.'
        },
        {
          title: 'Support Hours & Response Times',
          description: 'Our support team is available Monday to Friday, 8:00 AM to 5:00 PM. Emergency support is available 24/7 for critical issues.'
        },
        {
          title: 'Preparing Your Support Request',
          description: 'To help us assist you faster, please have your license numbers, error messages, and detailed description of the issue ready.'
        }
      ],
      faqs: [
        {
          question: 'What information should I include in my support request?',
          answer: 'Please include your organization name, license numbers (if applicable), detailed description of the issue, steps you&apos;ve already tried, and any error messages you&apos;ve encountered.'
        },
        {
          question: 'How quickly will I receive a response?',
          answer: 'We aim to respond to all support requests within 24 hours during business days. Critical issues are prioritized and may receive faster response times.'
        },
        {
          question: 'Can I track the status of my support request?',
          answer: 'Yes, when you submit a support request through the portal, you&apos;ll receive a ticket number that you can use to track the status of your request.'
        },
        {
          question: 'Is there emergency support available?',
          answer: 'Yes, we provide 24/7 emergency support for critical system issues that affect your operations. Use our emergency contact number for urgent matters.'
        }
      ]
    }
  ];

  // Get current category data
  const currentCategory = helpCategories.find(cat => cat.id === activeCategory) || helpCategories[0];
  const currentSubcategory = activeSubcategory
    ? currentCategory.subcategories?.find(sub => sub.id === activeSubcategory)
    : null;

  // Get current content (category or subcategory)
  const currentContent = currentSubcategory || currentCategory;

  // Filter FAQs based on search query
  const filteredFAQs = currentContent.faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Toggle FAQ expansion
  const toggleFAQ = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  // Toggle category expansion
  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    const category = helpCategories.find(cat => cat.id === categoryId);

    if (category?.subcategories && category.subcategories.length > 0) {
      // If category has subcategories, toggle expansion
      toggleCategoryExpansion(categoryId);
    } else {
      // If no subcategories, select the category directly
      setActiveCategory(categoryId);
      setActiveSubcategory(null);
    }
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (categoryId: string, subcategoryId: string) => {
    setActiveCategory(categoryId);
    setActiveSubcategory(subcategoryId);
    setExpandedFAQ(null); // Reset FAQ expansion
  };

  // Show loading state
  if (authLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-primary">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Help Center</span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Help & Support</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">Find answers to common questions and get support for MACRA Digital Portal</p>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="max-w-md">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400"></i>
              </div>
              <input
                type="text"
                placeholder="Search help articles and FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
        </div>

        {/* Help Content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Left sidebar with categories */}
          <div className="md:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Help Categories</h3>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {helpCategories.map((category) => (
                  <div key={category.id}>
                    <button
                      onClick={() => handleCategorySelect(category.id)}
                      className={`w-full px-4 py-4 flex items-center justify-between text-left text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        activeCategory === category.id && !activeSubcategory
                          ? 'text-primary bg-primary/5 dark:bg-primary/10'
                          : 'text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className="w-5 h-5 flex items-center justify-center mr-3">
                          <i className={category.icon}></i>
                        </div>
                        {category.name}
                      </div>
                      {category.subcategories && category.subcategories.length > 0 && (
                        <i className={`ri-arrow-${expandedCategories.includes(category.id) ? 'up' : 'down'}-s-line text-gray-400`}></i>
                      )}
                    </button>

                    {/* Subcategories dropdown */}
                    {category.subcategories && expandedCategories.includes(category.id) && (
                      <div className="bg-gray-50 dark:bg-gray-700/50">
                        {category.subcategories.map((subcategory) => (
                          <button
                            key={subcategory.id}
                            onClick={() => handleSubcategorySelect(category.id, subcategory.id)}
                            className={`w-full px-8 py-3 flex items-center text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors ${
                              activeCategory === category.id && activeSubcategory === subcategory.id
                                ? 'text-primary bg-primary/10 dark:bg-primary/20 font-medium'
                                : 'text-gray-600 dark:text-gray-400'
                            }`}
                          >
                            <div className="w-4 h-4 flex items-center justify-center mr-3">
                              <i className={subcategory.icon}></i>
                            </div>
                            {subcategory.name}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Contact Support Card */}
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Need More Help?</h3>
              </div>
              <div className="p-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Can&apos;t find what you&apos;re looking for? Our support team is here to help.
                </p>
                <button
                  onClick={() => setActiveCategory('contact-support')}
                  className="block w-full bg-primary text-white text-center py-2 px-4 rounded-lg hover:bg-red-700 transition duration-150 mb-4"
                >
                  Contact Support
                </button>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600 dark:text-gray-400 text-sm">
                    <i className="ri-phone-line mr-2"></i>
                    <span>+265 1 770 100</span>
                  </div>
                  <div className="flex items-center text-gray-600 dark:text-gray-400 text-sm">
                    <i className="ri-mail-line mr-2"></i>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center text-gray-600 dark:text-gray-400 text-sm">
                    <i className="ri-time-line mr-2"></i>
                    <span>Mon-Fri, 8:00 AM - 5:00 PM</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main content area */}
          <div className="md:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <i className={`${currentContent.icon} mr-3 text-primary text-xl`}></i>
                  <div>
                    <div className="flex items-center space-x-2">
                      {currentSubcategory && (
                        <>
                          <span className="text-sm text-gray-500 dark:text-gray-400">{currentCategory.name}</span>
                          <i className="ri-arrow-right-s-line text-gray-400"></i>
                        </>
                      )}
                      <h2 className="text-xl font-medium text-gray-900 dark:text-gray-100">{currentContent.name}</h2>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{currentContent.description}</p>
                  </div>
                </div>
              </div>
              
              <div className="p-6">
                <div className="space-y-8">
                  {/* Guides Section */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Guides & Tutorials</h3>
                    <div className="space-y-4">
                      {currentContent.guides.map((guide, index) => (
                        <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{guide.title}</h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">{guide.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Fees Section - Show only for subcategories with fees */}
                  {currentSubcategory?.fees && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        {currentSubcategory.name === 'Type Approval' ? 'Equipment Categories and Fees' : 'Fees & Charges'}
                      </h3>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                            <thead className="bg-gray-100 dark:bg-gray-600">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                  {currentSubcategory.name === 'Type Approval' ? 'Equipment Category' : 'Service'}
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                  {currentSubcategory.name === 'Type Approval' ? 'Type Approval per model (USD)' : 'Fee'}
                                </th>
                                {currentSubcategory.name === 'Type Approval' && (
                                  <>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                      Type Approval Acceptance per model (USD)
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                      Modification per model (USD)
                                    </th>
                                  </>
                                )}
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                  {currentSubcategory.name === 'Type Approval' ? 'Reference Standards of Conformity' : 'Description'}
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                              {currentSubcategory.fees.map((fee, index) => (
                                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-600">
                                  <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {fee.service}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary font-semibold">
                                    {fee.fee}
                                  </td>
                                  {currentSubcategory.name === 'Type Approval' && (
                                    <>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-primary font-semibold">
                                        {fee.acceptance || 'N/A'}
                                      </td>
                                      <td className="px-6 py-4 whitespace-nowrap text-sm text-primary font-semibold">
                                        {fee.modification || 'N/A'}
                                      </td>
                                    </>
                                  )}
                                  <td className="px-6 py-4 text-sm text-gray-600 dark:text-gray-400">
                                    {fee.description}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        <div className="px-6 py-4 bg-blue-50 dark:bg-blue-900/20 border-t border-gray-200 dark:border-gray-600">
                          <div className="flex items-start space-x-2">
                            <i className="ri-information-line text-blue-600 mt-0.5"></i>
                            <div className="text-sm text-blue-800 dark:text-blue-200">
                              <p className="font-medium mb-1">Important Notes:</p>
                              <ul className="list-disc list-inside space-y-1 text-xs">
                                <li>All fees are subject to applicable taxes and may change without prior notice</li>
                                <li>Payment must be made before service delivery or certificate issuance</li>
                                <li>Fees are non-refundable once processing has commenced</li>
                                <li>Additional charges may apply for complex applications requiring extra review</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* FAQ Section */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Frequently Asked Questions</h3>
                    <div className="space-y-3">
                      {filteredFAQs.length > 0 ? (
                        filteredFAQs.map((faq, index) => (
                          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                            <button
                              onClick={() => toggleFAQ(index)}
                              className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            >
                              <span className="font-medium text-gray-900 dark:text-gray-100 pr-4">{faq.question}</span>
                              <i className={`ri-arrow-${expandedFAQ === index ? 'up' : 'down'}-s-line text-gray-400 flex-shrink-0`}></i>
                            </button>
                            {expandedFAQ === index && (
                              <div className="px-4 pb-3">
                                <p className="text-gray-600 dark:text-gray-400 text-sm">{faq.answer}</p>
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                          No FAQs found matching your search query.
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Contact Support Details - Show only for contact-support category */}
                  {activeCategory === 'contact-support' && (
                    <div className="bg-gradient-to-r from-primary/5 to-red-100 dark:from-primary/10 dark:to-red-900/20 rounded-lg p-6 mb-8">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                        <i className="ri-customer-service-2-line mr-2 text-primary"></i>
                        Contact Our Support Team
                      </h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Contact Methods */}
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Get in Touch</h4>
                          <div className="space-y-3">
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                                <i className="ri-phone-line text-primary"></i>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-gray-100">Phone Support</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">+265 1 770 100</p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                                <i className="ri-mail-line text-primary"></i>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-gray-100">Email Support</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center mr-3">
                                <i className="ri-alarm-warning-line text-red-600"></i>
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-gray-100">Emergency Support</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">+265 999 123 456 (24/7)</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Support Hours */}
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Support Hours</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">Monday - Friday</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">8:00 AM - 5:00 PM</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">Saturday</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">9:00 AM - 1:00 PM</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">Sunday</span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">Closed</span>
                            </div>
                            <div className="flex justify-between border-t border-gray-200 dark:border-gray-600 pt-2 mt-3">
                              <span className="text-gray-600 dark:text-gray-400">Emergency Support</span>
                              <span className="font-medium text-red-600">24/7</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-6 flex flex-col sm:flex-row gap-3">
                        <Link
                          href="/customer/resources"
                          className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                          <i className="ri-send-plane-line mr-2"></i>
                          Submit Support Request
                        </Link>
                        <a
                          href="tel:+2651770100"
                          className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                          <i className="ri-phone-line mr-2"></i>
                          Call Now
                        </a>
                        <a
                          href="mailto:<EMAIL>"
                          className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                          <i className="ri-mail-line mr-2"></i>
                          Send Email
                        </a>
                      </div>
                    </div>
                  )}

                  {/* Still Need Help Section - Show for other categories */}
                  {activeCategory !== 'contact-support' && (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Still Need Help?</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4">
                        If you couldn&apos;t find the answer you&apos;re looking for, don&apos;t hesitate to reach out to our support team.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Link
                          href="/customer/resources"
                          className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                          <i className="ri-customer-service-2-line mr-2"></i>
                          Submit Support Request
                        </Link>
                        <a
                          href="mailto:<EMAIL>"
                          className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                        >
                          <i className="ri-mail-line mr-2"></i>
                          Email Support
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default HelpCenterPage;