import React from 'react';

interface StructuredDataProps {
  data: object;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

// Organization structured data for MACRA
export function MacraOrganizationSchema() {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "GovernmentOrganization",
    "name": "Malawi Communications Regulatory Authority",
    "alternateName": "MACRA",
    "url": "https://macra.mw",
    "logo": "https://portal.macra.mw/images/macra-logo.png",
    "description": "The Malawi Communications Regulatory Authority (MACRA) is responsible for regulating the communications, broadcasting, and postal services sectors in Malawi.",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Private Bag 261",
      "addressLocality": "Lilongwe",
      "addressRegion": "Central Region",
      "postalCode": "3",
      "addressCountry": "MW"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+265-1-770-100",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://www.facebook.com/MACRA.MW",
      "https://twitter.com/MACRA_MW"
    ]
  };

  return <StructuredData data={organizationData} />;
}

// Website structured data
export function WebsiteSchema() {
  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "MACRA License Verification Portal",
    "url": "https://portal.macra.mw/public",
    "description": "Official license verification portal for the Malawi Communications Regulatory Authority (MACRA)",
    "publisher": {
      "@type": "GovernmentOrganization",
      "name": "Malawi Communications Regulatory Authority"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://portal.macra.mw/public/verify?license={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return <StructuredData data={websiteData} />;
}

// Service structured data for license verification
export function LicenseVerificationServiceSchema() {
  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "License Verification Service",
    "description": "Verify the authenticity of licenses issued by the Malawi Communications Regulatory Authority (MACRA)",
    "provider": {
      "@type": "GovernmentOrganization",
      "name": "Malawi Communications Regulatory Authority"
    },
    "serviceType": "License Verification",
    "areaServed": {
      "@type": "Country",
      "name": "Malawi"
    },
    "availableChannel": {
      "@type": "ServiceChannel",
      "serviceUrl": "https://portal.macra.mw/public/verify",
      "serviceSmsNumber": null,
      "servicePhone": "+265-1-770-100"
    }
  };

  return <StructuredData data={serviceData} />;
}

// FAQ structured data
export function FAQSchema() {
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How do I verify a license?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "To verify a license, enter the license number in the format LIC-YYYY-MM-NNN (e.g., LIC-2024-01-001) on the verification page. You can optionally add a verification code if available for enhanced security."
        }
      },
      {
        "@type": "Question",
        "name": "What is the license number format?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "License numbers follow the format LIC-YYYY-MM-NNN where LIC is the license prefix, YYYY is the year of issue (4 digits), MM is the month of issue (2 digits), and NNN is the sequential number (3 digits)."
        }
      },
      {
        "@type": "Question",
        "name": "What do the different license statuses mean?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Active: License is currently valid and in good standing. Expired: License has passed its expiration date and requires renewal. Suspended: License is temporarily inactive due to regulatory actions. Revoked: License has been permanently cancelled."
        }
      },
      {
        "@type": "Question",
        "name": "Is this verification service official?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, this is the official license verification service provided by the Malawi Communications Regulatory Authority (MACRA). All verification results are sourced directly from MACRA's official database."
        }
      }
    ]
  };

  return <StructuredData data={faqData} />;
}

// Breadcrumb structured data
export function BreadcrumbSchema({ items }: { items: Array<{ name: string; url: string }> }) {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };

  return <StructuredData data={breadcrumbData} />;
}
