'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { CheckCircleIcon, EnvelopeIcon } from '@heroicons/react/24/solid';

interface SuccessStateProps {
  title: string;
  message: string;
  submessage?: string;
  showEmailIcon?: boolean;
  actionText?: string;
  actionHref?: string;
  secondaryActionText?: string;
  secondaryActionHref?: string;
  autoRedirect?: boolean;
  redirectDelay?: number;
  redirectMessage?: string;
  className?: string;
}

const SuccessState: React.FC<SuccessStateProps> = ({
  title,
  message,
  submessage,
  showEmailIcon = false,
  actionText,
  actionHref,
  secondaryActionText,
  secondaryActionHref,
  autoRedirect = false,
  redirectDelay = 5000,
  redirectMessage,
  className = ''
}) => {
  const [countdown, setCountdown] = useState(Math.ceil(redirectDelay / 1000));

  useEffect(() => {
    if (autoRedirect && actionHref) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            window.location.href = actionHref;
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [autoRedirect, actionHref]);

  return (
    <div className={`flex flex-col items-center justify-center text-center ${className}`}>
      {/* Success Icon */}
      <div className="w-16 h-16 mb-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center shadow-lg success-icon">
        {showEmailIcon ? (
          <EnvelopeIcon className="w-8 h-8 text-green-600 dark:text-green-300" />
        ) : (
          <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-300" />
        )}
      </div>

      {/* Title */}
      <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 success-content">
        {title}
      </h3>

      {/* Main Message */}
      <p className="text-gray-600 dark:text-gray-400 mb-2 max-w-md success-content">
        {message}
      </p>

      {/* Submessage */}
      {submessage && (
        <p className="text-sm text-gray-500 dark:text-gray-500 mb-6 max-w-md success-content">
          {submessage}
        </p>
      )}

      {/* Auto Redirect Message */}
      {autoRedirect && redirectMessage && countdown > 0 && (
        <div className="mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg animate-pulse">
          <p className="text-sm text-blue-600 dark:text-blue-400">
            {redirectMessage.replace('{countdown}', countdown.toString())}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 w-full max-w-sm success-content">
        {actionText && actionHref && (
          <Link
            href={actionHref}
            className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth"
          >
            {actionText}
          </Link>
        )}

        {secondaryActionText && secondaryActionHref && (
          <Link
            href={secondaryActionHref}
            className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth"
          >
            {secondaryActionText}
          </Link>
        )}
      </div>
    </div>
  );
};

export default SuccessState;
