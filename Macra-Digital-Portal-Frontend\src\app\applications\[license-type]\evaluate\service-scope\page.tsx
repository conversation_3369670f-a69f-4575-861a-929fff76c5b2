'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { EvaluationLayout, ServiceScopeCard, ApplicantInfoCard } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { useEvaluationData } from '@/hooks/useEvaluationData';
import EvaluationStepValidator from '@/components/evaluation/EvaluationStepValidator';

interface EvaluateServiceScopePageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateServiceScopePage: React.FC<EvaluateServiceScopePageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Use evaluation data hook
  const { data, loading, error } = useEvaluationData(applicationId);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep,
    previousStep,
    currentStep,
    totalSteps,
    licenseTypeCode: navLicenseTypeCode
  } = useDynamicNavigation({
    currentStepRoute: 'service-scope',
    licenseCategoryId,
    applicationId
  });

  // Set license category ID when data is loaded
  useEffect(() => {
    if (data?.application?.license_category_id) {
      setLicenseCategoryId(data.application.license_category_id);
    }
  }, [data]);

  // Authentication check
  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (!applicationId) {
      router.push('/applications');
      return;
    }
  }, [isAuthenticated, authLoading, router, applicationId]);

  // Navigation handlers
  const handleNext = async () => {
    setIsSubmitting(true);
    try {
      await dynamicHandleNext();
    } catch (error) {
      console.error('Error navigating to next step:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrevious = async () => {
    setIsSubmitting(true);
    try {
      await dynamicHandlePrevious();
    } catch (error) {
      console.error('Error navigating to previous step:', error);
    } finally {
      setIsSubmitting(false);
    }
  };



  // Loading state
  if (authLoading || loading) {
    return (
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="service-scope"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={true}
        previousButtonDisabled={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading service scope data...</span>
        </div>
      </EvaluationLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="service-scope"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={true}
        previousButtonDisabled={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400 text-xl"></i>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Data
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </EvaluationLayout>
    );
  }

  return (
    <EvaluationStepValidator
      licenseType={licenseType}
      currentStepRoute="service-scope"
      applicationId={applicationId!}
      licenseCategoryId={licenseCategoryId || undefined}
    >
      <EvaluationLayout
        applicationId={applicationId!}
        applicationNumber={data?.application?.application_number}
        licenseTypeCode={licenseType}
        currentStepRoute="service-scope"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
        {/* Service Scope Evaluation */}
        <div className="space-y-6">
          <ServiceScopeCard application={data.application} />
          <ApplicantInfoCard applicant={data.applicant} />
        </div>
      </EvaluationLayout>
    </EvaluationStepValidator>
  );
};

export default EvaluateServiceScopePage;
