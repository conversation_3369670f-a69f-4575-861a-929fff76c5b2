# Pagination Component

A smart pagination component that condenses page numbers when there are many pages, providing a better user experience for large datasets.

## Features

- **Smart Page Condensation**: Shows only relevant pages with ellipsis (`...`) when there are many pages
- **Configurable Visibility**: Control what elements to show (page info, page size selector, first/last buttons)
- **Responsive Design**: Works well on both desktop and mobile devices
- **Dark Mode Support**: Fully compatible with dark/light theme switching
- **Accessibility**: Proper ARIA labels and keyboard navigation support
- **Integration Ready**: Works seamlessly with existing `PaginatedResponse` structure

## Usage

### Basic Usage

```tsx
import Pagination from '@/components/common/Pagination';

// Your component
const MyComponent = () => {
  const [paginationData, setPaginationData] = useState({
    itemsPerPage: 10,
    totalItems: 247,
    currentPage: 1,
    totalPages: 25,
    sortBy: [],
    searchBy: [],
    search: '',
    filter: {}
  });

  const handlePageChange = (page: number) => {
    // Update your data/API call
    setPaginationData(prev => ({ ...prev, currentPage: page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    // Update page size and recalculate total pages
    const newTotalPages = Math.ceil(paginationData.totalItems / pageSize);
    setPaginationData(prev => ({
      ...prev,
      itemsPerPage: pageSize,
      totalPages: newTotalPages,
      currentPage: 1
    }));
  };

  return (
    <div>
      {/* Your content here */}
      
      <Pagination
        meta={paginationData}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};
```

### With DataTable (Automatic Integration)

The pagination component is already integrated into the `DataTable` component:

```tsx
import DataTable from '@/components/common/DataTable';

<DataTable
  columns={columns}
  data={paginatedData} // PaginatedResponse<T>
  loading={loading}
  onQueryChange={handleQueryChange}
  searchPlaceholder="Search..."
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `meta` | `PaginationMeta` | Required | Pagination metadata from API response |
| `onPageChange` | `(page: number) => void` | Required | Callback when page changes |
| `onPageSizeChange` | `(pageSize: number) => void` | Optional | Callback when page size changes |
| `showFirstLast` | `boolean` | `true` | Show first/last page buttons |
| `showPageSizeSelector` | `boolean` | `true` | Show page size dropdown |
| `showInfo` | `boolean` | `true` | Show "Showing X to Y of Z results" |
| `maxVisiblePages` | `number` | `7` | Maximum number of page buttons to show |
| `pageSizeOptions` | `number[]` | `[10, 25, 50, 100]` | Available page size options |
| `className` | `string` | `''` | Additional CSS classes |

## PaginationMeta Interface

```tsx
interface PaginationMeta {
  itemsPerPage: number;
  totalItems: number;
  currentPage: number;
  totalPages: number;
  sortBy: [string, string][];
  searchBy: string[];
  search: string;
  filter?: Record<string, string | string[]>;
}
```

## Examples

### Full Featured Pagination
```tsx
<Pagination
  meta={paginationData}
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
  showFirstLast={true}
  showPageSizeSelector={true}
  showInfo={true}
  maxVisiblePages={7}
  pageSizeOptions={[10, 25, 50, 100]}
/>
```

### Minimal Pagination
```tsx
<Pagination
  meta={paginationData}
  onPageChange={handlePageChange}
  showFirstLast={false}
  showPageSizeSelector={false}
  showInfo={false}
  maxVisiblePages={5}
/>
```

### Compact Pagination
```tsx
<Pagination
  meta={paginationData}
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
  maxVisiblePages={3}
  pageSizeOptions={[5, 10, 20]}
/>
```

## Page Condensation Logic

The component intelligently shows pages based on the current page and total pages:

- **Few pages** (≤ maxVisiblePages): Shows all pages
- **Many pages**: Shows first page, current page ± surrounding pages, last page
- **Ellipsis**: Used to indicate hidden pages between visible ranges

### Examples of page display:

- **Page 1 of 20**: `[1] 2 3 4 5 ... 20`
- **Page 5 of 20**: `1 ... 3 4 [5] 6 7 ... 20`
- **Page 18 of 20**: `1 ... 16 17 [18] 19 20`

## Styling

The component uses Tailwind CSS classes and supports:
- Light/dark mode
- Hover states
- Disabled states
- Focus states for accessibility
- Responsive design

## Integration with Backend

The component works with the existing pagination structure from your backend:

```tsx
// Backend response structure
interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
  links: {
    first: string;
    previous: string;
    current: string;
    next: string;
    last: string;
  };
}
```

The `meta` object from the response can be passed directly to the pagination component.
