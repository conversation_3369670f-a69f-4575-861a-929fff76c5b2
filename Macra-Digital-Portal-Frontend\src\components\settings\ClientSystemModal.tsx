'use client';

import React, { useState, useEffect } from 'react';
import {
  ClientSystem,
  ClientSystemType,
  ClientSystemStatus,
  CreateClientSystemDto,
  UpdateClientSystemDto,
  getSystemTypeLabel,
  getSystemStatusLabel,
} from '../../services/clientSystemService';

interface ClientSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: CreateClientSystemDto | UpdateClientSystemDto) => Promise<void>;
  clientSystem?: ClientSystem | null;
}

const ClientSystemModal: React.FC<ClientSystemModalProps> = ({
  isOpen,
  onClose,
  onSave,
  clientSystem,
}) => {
  const [formData, setFormData] = useState<CreateClientSystemDto>({
    name: '',
    system_code: '',
    description: '',
    system_type: ClientSystemType.WEB_APPLICATION,
    status: ClientSystemStatus.ACTIVE,
    api_endpoint: '',
    callback_url: '',
    contact_email: '',
    contact_phone: '',
    organization: '',
    access_permissions: '',
    version: '',
    notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const isEditing = !!clientSystem;

  useEffect(() => {
    if (isOpen) {
      if (clientSystem) {
        // Editing mode - populate form with existing data
        setFormData({
          name: clientSystem.name,
          system_code: clientSystem.system_code,
          description: clientSystem.description || '',
          system_type: clientSystem.system_type,
          status: clientSystem.status,
          api_endpoint: clientSystem.api_endpoint || '',
          callback_url: clientSystem.callback_url || '',
          contact_email: clientSystem.contact_email || '',
          contact_phone: clientSystem.contact_phone || '',
          organization: clientSystem.organization || '',
          access_permissions: clientSystem.access_permissions || '',
          version: clientSystem.version || '',
          notes: clientSystem.notes || '',
        });
      } else {
        // Creating mode - reset form
        setFormData({
          name: '',
          system_code: '',
          description: '',
          system_type: ClientSystemType.WEB_APPLICATION,
          status: ClientSystemStatus.ACTIVE,
          api_endpoint: '',
          callback_url: '',
          contact_email: '',
          contact_phone: '',
          organization: '',
          access_permissions: '',
          version: '',
          notes: '',
        });
      }
      setErrors({});
    }
  }, [isOpen, clientSystem]);

  const handleInputChange = (field: keyof CreateClientSystemDto, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'System name is required';
    }

    if (!formData.system_code.trim()) {
      newErrors.system_code = 'System code is required';
    } else if (!/^[A-Z0-9_]+$/.test(formData.system_code)) {
      newErrors.system_code = 'System code must contain only uppercase letters, numbers, and underscores';
    }

    if (formData.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contact_email)) {
      newErrors.contact_email = 'Please enter a valid email address';
    }

    if (formData.api_endpoint && !/^https?:\/\/.+/.test(formData.api_endpoint)) {
      newErrors.api_endpoint = 'Please enter a valid URL (http:// or https://)';
    }

    if (formData.callback_url && !/^https?:\/\/.+/.test(formData.callback_url)) {
      newErrors.callback_url = 'Please enter a valid URL (http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving client system:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Edit Client System' : 'Add New Client System'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                System Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="e.g., MACRA Mobile App"
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                System Code *
              </label>
              <input
                type="text"
                value={formData.system_code}
                onChange={(e) => handleInputChange('system_code', e.target.value.toUpperCase())}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.system_code ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="e.g., MACRA_MOBILE_V1"
              />
              {errors.system_code && <p className="text-red-500 text-xs mt-1">{errors.system_code}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Brief description of the client system..."
            />
          </div>

          {/* System Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                System Type
              </label>
              <select
                value={formData.system_type}
                onChange={(e) => handleInputChange('system_type', e.target.value as ClientSystemType)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                {Object.values(ClientSystemType).map((type) => (
                  <option key={type} value={type}>
                    {getSystemTypeLabel(type)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value as ClientSystemStatus)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                {Object.values(ClientSystemStatus).map((status) => (
                  <option key={status} value={status}>
                    {getSystemStatusLabel(status)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* URLs */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                API Endpoint
              </label>
              <input
                type="url"
                value={formData.api_endpoint}
                onChange={(e) => handleInputChange('api_endpoint', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.api_endpoint ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="https://api.example.com"
              />
              {errors.api_endpoint && <p className="text-red-500 text-xs mt-1">{errors.api_endpoint}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Callback URL
              </label>
              <input
                type="url"
                value={formData.callback_url}
                onChange={(e) => handleInputChange('callback_url', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.callback_url ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="https://example.com/callback"
              />
              {errors.callback_url && <p className="text-red-500 text-xs mt-1">{errors.callback_url}</p>}
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Contact Email
              </label>
              <input
                type="email"
                value={formData.contact_email}
                onChange={(e) => handleInputChange('contact_email', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.contact_email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="<EMAIL>"
              />
              {errors.contact_email && <p className="text-red-500 text-xs mt-1">{errors.contact_email}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Contact Phone
              </label>
              <input
                type="tel"
                value={formData.contact_phone}
                onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="+260211123456"
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Organization
              </label>
              <input
                type="text"
                value={formData.organization}
                onChange={(e) => handleInputChange('organization', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="MACRA IT Department"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Version
              </label>
              <input
                type="text"
                value={formData.version}
                onChange={(e) => handleInputChange('version', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="1.0.0"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Access Permissions (JSON)
            </label>
            <textarea
              value={formData.access_permissions}
              onChange={(e) => handleInputChange('access_permissions', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white font-mono text-sm"
              placeholder='{"read": true, "write": false, "admin": false}'
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Additional notes about this system..."
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
              {isEditing ? 'Update System' : 'Create System'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ClientSystemModal;
