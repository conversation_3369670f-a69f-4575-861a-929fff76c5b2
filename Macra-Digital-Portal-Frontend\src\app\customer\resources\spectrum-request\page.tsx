'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';

interface SpectrumBand {
  id: string;
  name: string;
  frequency: string;
  bandwidth: string;
  usage: string;
  status: 'available' | 'allocated' | 'reserved';
  price: string;
  applications: string[];
}

interface SpectrumRequest {
  id: string;
  requestNumber: string;
  frequency: string;
  bandwidth: string;
  purpose: string;
  status: 'pending' | 'approved' | 'rejected' | 'under-review';
  submittedDate: string;
  expiryDate?: string;
}

const SpectrumRequestPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'available' | 'request' | 'my-requests'>('available');
  const [selectedBand, setSelectedBand] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const spectrumBands: SpectrumBand[] = [
    {
      id: 'vhf-low',
      name: 'VHF Low Band',
      frequency: '30-88 MHz',
      bandwidth: '58 MHz',
      usage: 'FM Radio Broadcasting',
      status: 'available',
      price: '5,000 MWK/MHz/year',
      applications: ['FM Radio', 'Emergency Services', 'Public Safety']
    },
    {
      id: 'vhf-high',
      name: 'VHF High Band',
      frequency: '174-230 MHz',
      bandwidth: '56 MHz',
      usage: 'TV Broadcasting, Mobile Communications',
      status: 'allocated',
      price: '8,000 MWK/MHz/year',
      applications: ['TV Broadcasting', 'Mobile Radio', 'Amateur Radio']
    },
    {
      id: 'uhf',
      name: 'UHF Band',
      frequency: '470-790 MHz',
      bandwidth: '320 MHz',
      usage: 'Digital TV, Mobile Broadband',
      status: 'available',
      price: '12,000 MWK/MHz/year',
      applications: ['Digital TV', 'LTE', 'Mobile Broadband', 'IoT']
    },
    {
      id: 'gsm-900',
      name: 'GSM 900',
      frequency: '880-960 MHz',
      bandwidth: '80 MHz',
      usage: '2G/3G Mobile Services',
      status: 'allocated',
      price: '15,000 MWK/MHz/year',
      applications: ['2G GSM', '3G UMTS', 'Mobile Voice', 'SMS']
    },
    {
      id: 'gsm-1800',
      name: 'GSM 1800',
      frequency: '1710-1880 MHz',
      bandwidth: '170 MHz',
      usage: '2G/3G/4G Mobile Services',
      status: 'available',
      price: '18,000 MWK/MHz/year',
      applications: ['2G GSM', '3G UMTS', '4G LTE', 'Mobile Data']
    },
    {
      id: 'lte-2100',
      name: 'LTE 2100',
      frequency: '1920-2170 MHz',
      bandwidth: '250 MHz',
      usage: '3G/4G Mobile Services',
      status: 'reserved',
      price: '20,000 MWK/MHz/year',
      applications: ['3G UMTS', '4G LTE', 'Mobile Broadband', '5G NSA']
    }
  ];

  const myRequests: SpectrumRequest[] = [
    {
      id: 'req-001',
      requestNumber: 'SPR-2025-001',
      frequency: '2300-2400 MHz',
      bandwidth: '100 MHz',
      purpose: '4G LTE Network Expansion',
      status: 'under-review',
      submittedDate: '2025-01-15',
    },
    {
      id: 'req-002',
      requestNumber: 'SPR-2024-089',
      frequency: '1800-1850 MHz',
      bandwidth: '50 MHz',
      purpose: 'Rural Coverage Enhancement',
      status: 'approved',
      submittedDate: '2024-12-10',
      expiryDate: '2029-12-10'
    }
  ];

  const bandTypes = [
    { id: 'all', name: 'All Bands', count: spectrumBands.length },
    { id: 'vhf', name: 'VHF', count: spectrumBands.filter(b => b.id.includes('vhf')).length },
    { id: 'uhf', name: 'UHF', count: spectrumBands.filter(b => b.id.includes('uhf')).length },
    { id: 'gsm', name: 'GSM', count: spectrumBands.filter(b => b.id.includes('gsm')).length },
    { id: 'lte', name: 'LTE', count: spectrumBands.filter(b => b.id.includes('lte')).length }
  ];

  const filteredBands = spectrumBands.filter(band => {
    const matchesBand = selectedBand === 'all' || band.id.includes(selectedBand);
    const matchesSearch = band.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         band.frequency.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         band.usage.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesBand && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'allocated':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'pending':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'under-review':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <Link href="/customer" className="hover:text-blue-600">Dashboard</Link>
            <i className="ri-arrow-right-s-line"></i>
            <Link href="/customer/resources" className="hover:text-blue-600">Resources</Link>
            <i className="ri-arrow-right-s-line"></i>
            <span className="text-gray-900 dark:text-gray-100">Spectrum Request</span>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Spectrum Management
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                View available spectrum, submit allocation requests, and manage your spectrum assignments.
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <button 
                onClick={() => setActiveTab('request')}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                <i className="ri-add-line mr-2"></i>
                New Spectrum Request
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('available')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'available'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-radio-line mr-2"></i>
                Available Spectrum
              </button>
              <button
                onClick={() => setActiveTab('request')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'request'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-file-add-line mr-2"></i>
                Submit Request
              </button>
              <button
                onClick={() => setActiveTab('my-requests')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'my-requests'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <i className="ri-file-list-line mr-2"></i>
                My Requests ({myRequests.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Available Spectrum Tab */}
        {activeTab === 'available' && (
          <div>
            {/* Search and Filter */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-search-line text-gray-400"></i>
                  </div>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Search spectrum bands..."
                  />
                </div>
              </div>
              <div className="sm:w-48">
                <select
                  value={selectedBand}
                  onChange={(e) => setSelectedBand(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {bandTypes.map(type => (
                    <option key={type.id} value={type.id}>
                      {type.name} ({type.count})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Spectrum Bands Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredBands.map((band) => (
                <div
                  key={band.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {band.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {band.frequency} • {band.bandwidth}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(band.status)}`}>
                      {band.status.charAt(0).toUpperCase() + band.status.slice(1)}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">Primary Usage:</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{band.usage}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">Applications:</p>
                      <div className="flex flex-wrap gap-1">
                        {band.applications.map((app, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                          >
                            {app}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {band.price}
                        </p>
                      </div>
                      <button
                        disabled={band.status !== 'available'}
                        className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors duration-200 ${
                          band.status === 'available'
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                        }`}
                      >
                        <i className="ri-file-add-line mr-1"></i>
                        Request
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Submit Request Tab */}
        {activeTab === 'request' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
                New Spectrum Request
              </h3>
              
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Frequency Range
                    </label>
                    <input
                      type="text"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., 2300-2400 MHz"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bandwidth Required
                    </label>
                    <input
                      type="text"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., 100 MHz"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Purpose of Use
                  </label>
                  <select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Select purpose</option>
                    <option value="mobile-broadband">Mobile Broadband</option>
                    <option value="broadcasting">Broadcasting</option>
                    <option value="emergency-services">Emergency Services</option>
                    <option value="iot">Internet of Things (IoT)</option>
                    <option value="satellite">Satellite Communications</option>
                    <option value="research">Research & Development</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Technical Justification
                  </label>
                  <textarea
                    rows={4}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Provide detailed technical justification for the spectrum request..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Coverage Area
                    </label>
                    <input
                      type="text"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Lilongwe, Blantyre"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Requested Duration
                    </label>
                    <select className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="">Select duration</option>
                      <option value="1">1 Year</option>
                      <option value="3">3 Years</option>
                      <option value="5">5 Years</option>
                      <option value="10">10 Years</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-6">
                  <button
                    type="button"
                    onClick={() => setActiveTab('available')}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
                  >
                    <i className="ri-send-plane-line mr-2"></i>
                    Submit Request
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* My Requests Tab */}
        {activeTab === 'my-requests' && (
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  My Spectrum Requests
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {myRequests.length} request{myRequests.length !== 1 ? 's' : ''} found
                </p>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Request
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Frequency
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Purpose
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {myRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {request.requestNumber}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {request.bandwidth}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {request.frequency}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {request.purpose}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1).replace('-', ' ')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <div>
                            <div>Submitted: {new Date(request.submittedDate).toLocaleDateString()}</div>
                            {request.expiryDate && (
                              <div>Expires: {new Date(request.expiryDate).toLocaleDateString()}</div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </CustomerLayout>
  );
};

export default SpectrumRequestPage;
