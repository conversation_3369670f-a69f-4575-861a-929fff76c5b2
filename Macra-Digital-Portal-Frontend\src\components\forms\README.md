# Form Components

This directory contains reusable form components for the MACRA Digital Portal. These components ensure consistent styling, proper text visibility across light/dark modes, and enhanced user experience.

## Components

### 1. CountryDropdown

A searchable dropdown component for selecting countries/nationalities.

#### Features
- ✅ **Searchable**: Type to filter countries
- ✅ **Comprehensive**: 195+ countries worldwide
- ✅ **Click-to-select**: Select while typing
- ✅ **Accessible**: ARIA attributes and keyboard navigation
- ✅ **Dark mode**: Full support for light/dark themes
- ✅ **Error handling**: Built-in error state display

#### Usage

```tsx
import { CountryDropdown } from '@/components/forms';

<CountryDropdown
  value={formData.nationality}
  onChange={(value) => setFormData({...formData, nationality: value})}
  placeholder="Select or type country name"
  required
  className="text-sm" // Optional: for smaller variant
  error="Please select a country" // Optional: error message
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | - | Selected country value |
| `onChange` | `(value: string) => void` | - | Change handler |
| `placeholder` | `string` | "Select or type country name" | Input placeholder |
| `required` | `boolean` | `false` | Mark as required field |
| `className` | `string` | `""` | Additional CSS classes |
| `disabled` | `boolean` | `false` | Disable the component |
| `error` | `string` | `""` | Error message to display |
| `id` | `string` | - | Input ID attribute |
| `name` | `string` | - | Input name attribute |

### 2. TextInput

A styled text input component with consistent theming.

#### Features
- ✅ **Consistent styling**: Matches design system
- ✅ **Dark mode**: Proper text visibility in all modes
- ✅ **Error states**: Built-in error handling
- ✅ **Labels**: Integrated label support
- ✅ **Variants**: Default and small sizes

#### Usage

```tsx
import { TextInput } from '@/components/forms';

<TextInput
  type="email"
  label="Email Address"
  value={formData.email}
  onChange={(e) => setFormData({...formData, email: e.target.value})}
  placeholder="Enter your email"
  required
  error={errors.email}
  helperText="We'll never share your email"
/>
```

#### Props

Extends all standard HTML input props, plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Field label |
| `error` | `string` | - | Error message |
| `helperText` | `string` | - | Helper text below input |
| `variant` | `'default' \| 'small'` | `'default'` | Size variant |
| `fullWidth` | `boolean` | `true` | Full width styling |

### 3. TextArea

A styled textarea component with consistent theming.

#### Features
- ✅ **Consistent styling**: Matches design system
- ✅ **Dark mode**: Proper text visibility in all modes
- ✅ **Resizable**: Vertical resize enabled
- ✅ **Error states**: Built-in error handling
- ✅ **Labels**: Integrated label support

#### Usage

```tsx
import { TextArea } from '@/components/forms';

<TextArea
  label="Description"
  value={formData.description}
  onChange={(e) => setFormData({...formData, description: e.target.value})}
  placeholder="Enter description"
  rows={4}
  required
  error={errors.description}
/>
```

#### Props

Extends all standard HTML textarea props, plus:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Field label |
| `error` | `string` | - | Error message |
| `helperText` | `string` | - | Helper text below textarea |
| `variant` | `'default' \| 'small'` | `'default'` | Size variant |
| `fullWidth` | `boolean` | `true` | Full width styling |

## Styling Features

All components include:

- **Cross-mode compatibility**: Proper text visibility in light/dark browser modes
- **Consistent colors**: 
  - Background: `bg-white dark:bg-gray-700`
  - Text: `text-gray-900 dark:text-gray-100`
  - Placeholder: `placeholder-gray-500 dark:placeholder-gray-400`
  - Border: `border-gray-300 dark:border-gray-600`
- **Focus states**: Primary color focus rings
- **Error states**: Red border and text for errors
- **Disabled states**: Proper opacity and cursor styling
- **Transitions**: Smooth color transitions

## Usage Examples

### Complete Form Example

```tsx
import { CountryDropdown, TextInput, TextArea } from '@/components/forms';

const MyForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    nationality: '',
    address: ''
  });

  return (
    <form className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <TextInput
          label="Full Name"
          value={formData.name}
          onChange={(e) => setFormData({...formData, name: e.target.value})}
          required
        />
        
        <TextInput
          type="email"
          label="Email Address"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          required
        />
        
        <CountryDropdown
          value={formData.nationality}
          onChange={(value) => setFormData({...formData, nationality: value})}
          required
        />
        
        <TextArea
          label="Address"
          value={formData.address}
          onChange={(e) => setFormData({...formData, address: e.target.value})}
          rows={3}
          required
        />
      </div>
    </form>
  );
};
```

### Array Field Example

```tsx
// For dynamic arrays (like shareholders, directors, etc.)
{formData.shareholders.map((shareholder, index) => (
  <div key={index} className="grid grid-cols-3 gap-4">
    <TextInput
      label="Name"
      value={shareholder.name}
      onChange={(e) => handleArrayFieldChange('shareholders', index, 'name', e.target.value)}
      variant="small"
      required
    />
    
    <CountryDropdown
      value={shareholder.nationality}
      onChange={(value) => handleArrayFieldChange('shareholders', index, 'nationality', value)}
      className="text-sm"
      required
    />
    
    <TextArea
      label="Address"
      value={shareholder.address}
      onChange={(e) => handleArrayFieldChange('shareholders', index, 'address', e.target.value)}
      variant="small"
      rows={2}
      required
    />
  </div>
))}
```

## Migration Guide

### From Old Input Fields

**Before:**
```tsx
<input
  type="text"
  value={value}
  onChange={onChange}
  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md..."
  required
/>
```

**After:**
```tsx
<TextInput
  value={value}
  onChange={onChange}
  required
/>
```

### From Old Country Fields

**Before:**
```tsx
<input
  type="text"
  value={nationality}
  onChange={(e) => setNationality(e.target.value)}
  placeholder="Enter nationality"
/>
```

**After:**
```tsx
<CountryDropdown
  value={nationality}
  onChange={(value) => setNationality(value)}
/>
```

## Benefits

1. **Consistency**: All forms across the portal will have identical styling
2. **Maintainability**: Update styling in one place
3. **Accessibility**: Built-in ARIA attributes and keyboard navigation
4. **User Experience**: Enhanced interactions (searchable countries, proper focus states)
5. **Dark Mode**: Guaranteed text visibility in all scenarios
6. **Developer Experience**: Simple, consistent API across all components

## Best Practices

1. **Always use labels**: Either via the `label` prop or external `<label>` elements
2. **Handle errors**: Use the `error` prop to display validation messages
3. **Use appropriate variants**: `small` variant for compact forms, tables
4. **Provide placeholders**: Help users understand expected input format
5. **Mark required fields**: Use the `required` prop for validation
6. **Test both modes**: Verify appearance in light and dark themes