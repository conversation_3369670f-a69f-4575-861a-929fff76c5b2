# Shortcode Types Consolidation

## Overview

All shortcode-related types, interfaces, enums, and constants have been consolidated into a single source of truth: `src/types/shortcode.ts`.

## What Was Consolidated

### 1. **Enums** (Previously scattered across backend DTOs)
- `ShortcodeCategory` - Service categories for shortcodes
- `ShortcodeStatus` - Active/Inactive status
- `ShortcodeAudience` - Target audience (community, national, regional, district)

### 2. **Interfaces** (Newly created for frontend)
- `Shortcode` - Main entity interface matching backend
- `CreateShortcodeDto` - For creating new shortcodes
- `UpdateShortcodeDto` - For updating existing shortcodes
- `ShortcodeFormData` - For form handling in components
- `ShortcodeApiResponse` - API response wrapper
- `ShortcodeListApiResponse` - List API response wrapper
- `ShortcodeNumberingCategory` - Numbering plan category
- `ShortcodeNumberingPlan` - Numbering plan structure
- `ShortcodeFilters` - For API filtering

### 3. **Constants and Options**
- `SHORTCODE_CATEGORY_OPTIONS` - Form dropdown options
- `SHORTCODE_AUDIENCE_OPTIONS` - Form dropdown options
- `SHORTCODE_LENGTH_OPTIONS` - Length selection options
- `SHORTCODE_STATUS_OPTIONS` - Status options (admin use)
- `SHORTCODE_VALIDATION` - Validation rules and limits
- `shortCodeNumbering` - Complete numbering plan from backend
- `DEFAULT_SHORTCODE_FORM_DATA` - Default form values

### 4. **Helper Functions**
- `getShortcodeCategoryLabel()` - Get display label for category
- `getShortcodeAudienceLabel()` - Get display label for audience
- `getShortcodeStatusLabel()` - Get display label for status

## Import Usage

All frontend components now import from the centralized location:

```typescript
import {
  ShortcodeFormData,
  DEFAULT_SHORTCODE_FORM_DATA,
  SHORTCODE_CATEGORY_OPTIONS,
  SHORTCODE_AUDIENCE_OPTIONS,
  ShortcodeStatus,
  CreateShortcodeDto,
  UpdateShortcodeDto
} from '@/types/shortcode';
```

## Files Updated

### ✅ **Already Using Centralized Types**
- `src/services/shortcodeService.ts`
- `src/components/customer/application/ShortCodeUsage.tsx`
- `src/components/customer/application/ShortCodeUsage.test.tsx`
- `src/app/customer/applications/apply/short-code-usage/page.tsx`
- `src/types/index.ts` (exports shortcode types)

### 📁 **Backend Files** (Unchanged - Keep Separate)
- `src/dto/shortcodes/shortcode-enums.ts` - Backend validation enums
- `src/dto/shortcodes/create-shortcode.dto.ts` - Backend DTO with decorators
- `src/dto/shortcodes/update-shortcode.dto.ts` - Backend DTO with decorators
- `src/entities/shortcode.entity.ts` - Database entity

## Benefits

1. **Single Source of Truth** - All shortcode types in one place
2. **Consistent Imports** - All frontend code imports from `@/types/shortcode`
3. **Type Safety** - Comprehensive TypeScript interfaces
4. **Maintainability** - Easy to update types across the entire frontend
5. **Documentation** - Clear structure with comments and examples
6. **Validation** - Centralized validation rules and constants

## Backend vs Frontend Separation

- **Backend DTOs** remain in `src/dto/shortcodes/` with validation decorators
- **Frontend Types** are in `src/types/shortcode.ts` for UI components
- **Entity** remains in `src/entities/shortcode.entity.ts` for database mapping

This separation allows:
- Backend to maintain its validation and API structure
- Frontend to have clean, UI-focused type definitions
- No circular dependencies between frontend and backend code

## Usage Examples

### Component Usage
```typescript
import { ShortcodeFormData, SHORTCODE_CATEGORY_OPTIONS } from '@/types/shortcode';

const MyComponent = () => {
  const [formData, setFormData] = useState<ShortcodeFormData>({
    audience: ShortcodeAudience.COMMUNITY,
    category: ShortcodeCategory.RESERVED,
    // ...
  });
};
```

### Service Usage
```typescript
import { CreateShortcodeDto, ShortcodeStatus } from '@/types/shortcode';

const createShortcode = (formData: ShortcodeFormData): CreateShortcodeDto => ({
  audience: formData.audience,
  status: ShortcodeStatus.INACTIVE,
  // ...
});
```

## Validation

All validation rules are centralized in `SHORTCODE_VALIDATION` constant:
- `DESCRIPTION_MAX_LENGTH: 255`
- `NOTES_MAX_LENGTH: 255`
- `SHORTCODE_MIN_LENGTH: 3`
- `SHORTCODE_MAX_LENGTH: 4`
