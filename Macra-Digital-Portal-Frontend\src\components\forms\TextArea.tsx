'use client';

import React, { forwardRef } from 'react';

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: React.ReactNode;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'small';
  fullWidth?: boolean;
}

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  fullWidth = true,
  className = '',
  required,
  disabled,
  rows = 3,
  ...props
}, ref) => {
  // Base textarea styling with proper text visibility for all modes - force text color to ensure visibility
  const baseTextAreaClass = `px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 resize-y min-h-[120px] ${
    fullWidth ? 'w-full' : ''
  } ${variant === 'small' ? 'py-2 text-sm min-h-[80px]' : 'text-base'}`;

  // Error and disabled states
  const textAreaClass = `${baseTextAreaClass} ${
    error
      ? "border-red-300 dark:border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/10"
      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
  } ${
    disabled
      ? "opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800"
      : ""
  } ${className}`;

  const labelClass = `block font-semibold text-gray-800 dark:text-gray-200 mb-3 ${
    variant === 'small' ? 'text-sm text-gray-700 dark:text-gray-300 mb-2' : 'text-base'
  }`;

  return (
    <div className="w-full">
      {label && (
        <label className={labelClass}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        className={textAreaClass}
        disabled={disabled}
        required={required}
        rows={rows}
        {...props}
      />
      
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
          <i className="ri-error-warning-line mr-1"></i>
          {error}
        </p>
      )}

      {helperText && !error && (
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400 flex items-center">
          <i className="ri-information-line mr-1"></i>
          {helperText}
        </p>
      )}
    </div>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;