import React, { useState, useEffect } from 'react';
import { TaskAssignmentModal } from '@/components/shared/TaskAssignmentModal';
import { useTaskAssignment } from '@/hooks/useTaskAssignment';
import { useTaskNavigation } from '@/hooks/useTaskNavigation';
import { Task, User } from '@/types';

interface TaskDetailsResponse {
  task: Task;
  canNavigateToEntity: boolean;
}

interface TaskDetailsViewProps {
  taskId: string;
  users?: User[];
  onRefresh?: () => void;
}

export const TaskDetailsView: React.FC<TaskDetailsViewProps> = ({
  taskId,
  users = [],
  onRefresh,
}) => {
  const [taskDetails, setTaskDetails] = useState<TaskDetailsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { assignTask, isAssigning } = useTaskAssignment({
    onSuccess: () => {
      loadTaskDetails();
      setIsAssignModalOpen(false);
      onRefresh?.();
    },
  });

  const { openTaskViewInNewTab } = useTaskNavigation();

  const loadTaskDetails = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/tasks/${taskId}/navigation`);
      if (response.ok) {
        const data = await response.json();
        setTaskDetails(data);
      }
    } catch (error) {
      console.error('Error loading task details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTaskDetails();
  }, [taskId]);

  const handleAssignment = async (assignmentData: {
    assignedTo: string;
    comment?: string;
    due_date?: string;
    priority?: string;
    assignment_notes?: string;
  }) => {
    await assignTask(taskId, assignmentData);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { className: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Pending' },
      in_progress: { className: 'bg-blue-100 text-blue-800 border-blue-200', label: 'In Progress' },
      completed: { className: 'bg-green-100 text-green-800 border-green-200', label: 'Completed' },
      cancelled: { className: 'bg-red-100 text-red-800 border-red-200', label: 'Cancelled' },
      on_hold: { className: 'bg-gray-100 text-gray-800 border-gray-200', label: 'On Hold' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
        {config.label}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { className: 'bg-green-100 text-green-800 border-green-200', label: 'Low' },
      medium: { className: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Medium' },
      high: { className: 'bg-orange-100 text-orange-800 border-orange-200', label: 'High' },
      urgent: { className: 'bg-red-100 text-red-800 border-red-200', label: 'Urgent' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  if (!taskDetails) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Task not found</p>
      </div>
    );
  }

  const { task, canNavigateToEntity } = taskDetails;

  return (
    <>
      <div className="space-y-6">
        {/* Task Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="p-6">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{task.title}</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Task #{task.task_number}</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(task.status)}
                {getPriorityBadge(task.priority)}
              </div>
            </div>
          </div>
          <div className="px-6 pb-6">
            <p className="text-gray-700 dark:text-gray-300">{task.description}</p>
          </div>
        </div>

        {/* Entity Navigation */}
        {canNavigateToEntity && task.entity_type === 'application' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2 mb-4">
                📄 Related Application
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                This task is related to an application. Click below to open the evaluation interface.
              </p>
              <button
                onClick={() => openTaskViewInNewTab(taskId)}
                className="w-full bg-red-600 text-white hover:bg-red-700 px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center justify-center gap-2"
              >
                🔗 Open Application Evaluation
              </button>
            </div>
          </div>
        )}

        {/* Task Details */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Task Details</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Task Type</p>
                  <p className="text-sm text-gray-900 dark:text-gray-100 capitalize">{task.task_type.replace('_', ' ')}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</p>
                  <p className="text-sm text-gray-900 dark:text-gray-100">{new Date(task.created_at).toLocaleDateString()}</p>
                </div>
                {task.due_date && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100 flex items-center gap-1">
                      📅 {new Date(task.due_date).toLocaleDateString()}
                    </p>
                  </div>
                )}
                {task.completed_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Completed</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{new Date(task.completed_at).toLocaleDateString()}</p>
                  </div>
                )}
              </div>

              {/* Assignment Information */}
              <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Assignment</p>
                {task.assignee ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-gray-400">👤</span>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {task.assignee.first_name} {task.assignee.last_name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{task.assignee.email}</p>
                        {task.assigned_at && (
                          <p className="text-xs text-gray-400">
                            Assigned {new Date(task.assigned_at).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                    <button
                      className="border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 px-3 py-1 rounded-md text-sm font-medium transition-colors"
                      onClick={() => setIsAssignModalOpen(true)}
                    >
                      👥 Reassign
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Unassigned</p>
                    <button
                      className="bg-red-600 text-white hover:bg-red-700 px-3 py-1 rounded-md text-sm font-medium transition-colors"
                      onClick={() => setIsAssignModalOpen(true)}
                    >
                      👥 Assign Task
                    </button>
                  </div>
                )}
              </div>

              {/* Notes */}
              {task.review_notes && (
                <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Notes</p>
                  <p className="text-sm bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-3 rounded-md">{task.review_notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={() => setIsAssignModalOpen(false)}
        onAssign={handleAssignment}
        task={task}
        users={users}
        loading={isAssigning}
      />
    </>
  );
};
